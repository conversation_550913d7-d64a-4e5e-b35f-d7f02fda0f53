(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8541],{52285:function(e,s,t){Promise.resolve().then(t.t.bind(t,23735,23)),Promise.resolve().then(t.t.bind(t,77815,23)),Promise.resolve().then(t.t.bind(t,2778,23)),Promise.resolve().then(t.bind(t,16194)),Promise.resolve().then(t.bind(t,78903)),Promise.resolve().then(t.bind(t,53562)),Promise.resolve().then(t.bind(t,3371)),Promise.resolve().then(t.bind(t,64528)),Promise.resolve().then(t.bind(t,11658)),Promise.resolve().then(t.bind(t,71917))},78903:function(e,s,t){"use strict";t.d(s,{default:function(){return d}});var r=t(57437),c=t(2265),l=t(27648),i=t(33145),a=t(37760),n=()=>(0,r.jsxs)("footer",{className:"bg-[#2c2c27] text-[#f4f3f0]",children:[(0,r.jsx)("div",{className:"py-16",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-12",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{children:(0,r.jsx)(i.default,{src:"/logo.PNG",alt:"Ankkor",width:160,height:50,className:"h-12 w-auto invert"})}),(0,r.jsx)("p",{className:"text-[#d5d0c3] text-sm leading-relaxed",children:"Timeless menswear crafted with exceptional materials and artisanal techniques, designed for the discerning gentleman who values understated luxury."}),(0,r.jsx)("div",{className:"flex space-x-6",children:(0,r.jsx)("a",{href:"https://www.instagram.com/ankkorindia?igsh=bHQwZjJxZHI1c2Iz&utm_source=qr",target:"_blank",rel:"noopener noreferrer",className:"text-[#8a8778] hover:text-[#f4f3f0] transition-colors",children:(0,r.jsx)(a.Z,{className:"h-5 w-5"})})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-serif text-lg mb-6",children:"Shop"}),(0,r.jsxs)("ul",{className:"space-y-3 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/collection/shirts",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Shirts"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/collection",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"View All"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-serif text-lg mb-6",children:"Customer Service"}),(0,r.jsxs)("ul",{className:"space-y-3 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/customer-service/contact",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Contact Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/shipping-policy",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Shipping Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/return-policy",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Return Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/customer-service/size-guide",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Size Guide"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-serif text-lg mb-6",children:"Company"}),(0,r.jsxs)("ul",{className:"space-y-3 text-sm",children:[(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/about",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"About Us"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/privacy-policy",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Privacy Policy"})}),(0,r.jsx)("li",{children:(0,r.jsx)(l.default,{href:"/terms-of-service",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Terms of Service"})})]})]})]})})}),(0,r.jsx)("div",{className:"border-t border-[#3d3d35] py-6",children:(0,r.jsx)("div",{className:"container mx-auto px-4",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4",children:[(0,r.jsxs)("p",{className:"text-[#8a8778] text-xs",children:["\xa9 ",new Date().getFullYear()," Ankkor. All rights reserved."]}),(0,r.jsx)("a",{href:"https://abhijeets-portfolio.vercel.app",target:"_blank",rel:"noopener noreferrer",className:"text-[#8a8778] text-xs hover:text-[#f4f3f0] transition-colors",children:"Contact Developer"})]}),(0,r.jsxs)("div",{className:"flex space-x-8",children:[(0,r.jsx)(l.default,{href:"/privacy-policy",className:"text-[#8a8778] text-xs hover:text-[#f4f3f0] transition-colors",children:"Privacy Policy"}),(0,r.jsx)(l.default,{href:"/terms-of-service",className:"text-[#8a8778] text-xs hover:text-[#f4f3f0] transition-colors",children:"Terms of Service"})]})]})})})]}),o=t(64528),d=()=>{let[e,s]=(0,c.useState)(!1),{isLaunchingSoon:t}=(0,o.Gd)();return((0,c.useEffect)(()=>{s(!0)},[]),!e||t)?null:(0,r.jsx)(n,{})}},53562:function(e,s,t){"use strict";t.d(s,{default:function(){return g}});var r=t(57437),c=t(2265),l=t(27648),i=t(33145),a=t(73247),n=t(92369),o=t(47692),d=t(88997),x=t(42449),h=t(58293),f=t(32489),m=t(91032),u=t(92371),j=t(16194),p=t(3371),v=t(96617),N=()=>{let{toggleCart:e,itemCount:s}=(0,j.j)(),{items:t}=(0,u.Y)(),N=t.length,[b,g]=c.useState(!1),[w,y]=(0,c.useState)(!1),{customer:k,isAuthenticated:C,logout:S}=(0,p.O)();return c.useEffect(()=>{let e=()=>{window.scrollY>10?g(!0):g(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),c.useEffect(()=>{let e=e=>{"Escape"===e.key&&w&&y(!1)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[w]),c.useEffect(()=>{console.log("Navbar: Authentication state",{isAuthenticated:C,customer:k?"".concat(k.firstName," ").concat(k.lastName):"not logged in"})},[C,k]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("nav",{className:"fixed top-0 left-0 right-0 z-[99] transition-all duration-300 ".concat(b?"bg-[#f8f8f5] h-16 shadow-sm":"bg-transparent h-24 py-4"),children:(0,r.jsxs)("div",{className:"container mx-auto px-4 h-full flex items-center justify-between",children:[(0,r.jsx)(l.default,{href:"/",className:"font-serif text-2xl font-bold text-[#2c2c27] relative z-10",children:(0,r.jsx)(i.default,{src:"/logo.PNG",alt:"Ankkor",width:160,height:50,priority:!0,className:"".concat(b?"h-10":"h-14"," w-auto transition-all duration-300")})}),(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-12 relative z-10",children:[(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsx)("button",{className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1 flex items-center",children:"Collections"}),(0,r.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 pt-2 w-[220px] opacity-0 invisible translate-y-1 group-hover:translate-y-0 group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out",children:(0,r.jsxs)("div",{className:"bg-[#f8f8f5] border border-[#e5e2d9] rounded-md shadow-md p-2",children:[(0,r.jsx)(l.default,{href:"/collection/shirts",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Shirts"}),(0,r.jsx)("div",{className:"h-px bg-[#e5e2d9] my-2"}),(0,r.jsx)(l.default,{href:"/collection",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"View All Collections"})]})})]}),(0,r.jsx)(l.default,{href:"/about",className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1",children:"Heritage"}),(0,r.jsx)(l.default,{href:"/customer-service",className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1",children:"Customer Service"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 relative z-10",children:[(0,r.jsx)("button",{onClick:()=>y(!0),className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2","aria-label":"Search",children:(0,r.jsx)(a.Z,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{className:"group relative",children:[(0,r.jsx)("button",{className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2",children:(0,r.jsx)(n.Z,{className:"h-5 w-5"})}),(0,r.jsx)("div",{className:"absolute right-0 pt-2 w-[220px] opacity-0 invisible translate-y-1 group-hover:translate-y-0 group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out",children:(0,r.jsx)("div",{className:"bg-[#f8f8f5] border border-[#e5e2d9] rounded-md shadow-md p-2",children:C?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"px-4 py-1.5 text-sm text-[#5c5c52]",children:["Hello, ",(null==k?void 0:k.firstName)||"there"]}),(0,r.jsx)("div",{className:"h-px bg-[#e5e2d9] my-2"}),(0,r.jsx)(l.default,{href:"/account",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"My Account"}),(0,r.jsx)(l.default,{href:"/account?tab=orders",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Order History"}),(0,r.jsx)(l.default,{href:"/wishlist",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Wishlist"}),(0,r.jsx)(l.default,{href:"/woocommerce-checkout-test",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded text-xs bg-gray-100",children:"Test WooCommerce Checkout"}),(0,r.jsx)("div",{className:"h-px bg-[#e5e2d9] my-2"}),(0,r.jsxs)("button",{onClick:S,className:"flex items-center text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer w-full py-2 px-4 rounded",children:[(0,r.jsx)(o.Z,{className:"h-4 w-4 mr-2"}),"Sign Out"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.default,{href:"/sign-in",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Sign In"}),(0,r.jsx)(l.default,{href:"/sign-up",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Create Account"})]})})})]}),(0,r.jsxs)(l.default,{href:"/wishlist",className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2 relative",children:[(0,r.jsx)(d.Z,{className:"h-5 w-5"}),N>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-[#2c2c27] text-[#f4f3f0] text-xs rounded-full h-5 w-5 flex items-center justify-center",children:N})]}),(0,r.jsxs)("button",{onClick:s=>{s.preventDefault(),s.stopPropagation(),e()},className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors relative p-2","aria-label":"Shopping cart",children:[(0,r.jsx)(x.Z,{className:"h-5 w-5"}),s>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 bg-[#2c2c27] text-[#f4f3f0] text-xs rounded-full h-5 w-5 flex items-center justify-center",children:s})]}),(0,r.jsxs)(m.yo,{children:[(0,r.jsx)(m.aM,{asChild:!0,children:(0,r.jsx)("button",{className:"md:hidden text-[#2c2c27] hover:text-[#8a8778] transition-colors","aria-label":"Menu",children:(0,r.jsx)(h.Z,{className:"h-6 w-6"})})}),(0,r.jsx)(m.ue,{side:"right",className:"bg-[#f8f8f5] w-[300px] p-0",children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsx)("div",{className:"p-6 border-b border-[#e5e2d9]",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-serif text-xl font-bold text-[#2c2c27]",children:(0,r.jsx)(i.default,{src:"/logo.PNG",alt:"Ankkor",width:120,height:40,className:"h-10 w-auto"})}),(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)("button",{className:"text-[#2c2c27]","aria-label":"Close menu",children:(0,r.jsx)(f.Z,{className:"h-5 w-5"})})})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-auto py-6 px-6",children:(0,r.jsxs)("nav",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Collections"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/collection/shirts",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Shirts"})})}),(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/collection",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"View All Collections"})})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Company"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/about",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Heritage"})})}),(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/customer-service",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Customer Service"})})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Account"}),(0,r.jsx)("ul",{className:"space-y-3",children:C?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/account",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"My Account"})})}),(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/account?tab=orders",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Order History"})})}),(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/wishlist",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Wishlist"})})}),(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)("button",{onClick:S,className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Sign Out"})})})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/sign-in",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Sign In"})})}),(0,r.jsx)("li",{children:(0,r.jsx)(m.sw,{asChild:!0,children:(0,r.jsx)(l.default,{href:"/sign-up",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Create Account"})})})]})})]})]})})]})})]}),!C&&(0,r.jsxs)(l.default,{href:"/sign-up",className:"hidden md:flex items-center ml-3 bg-gradient-to-b from-[#2c2c27] to-[#3a3a34] text-[#f8f8f5] border border-[#2c2c27] font-medium text-sm uppercase tracking-wider px-6 py-2.5 rounded-sm shadow-sm hover:shadow-md hover:from-[#3a3a34] hover:to-[#2c2c27] transition-all duration-200",children:[(0,r.jsx)(n.Z,{className:"h-4 w-4 mr-2 opacity-80"}),"Sign Up"]})]})]})}),(0,r.jsx)("div",{className:b?"h-16":"h-20"}),(0,r.jsx)(v.Z,{isOpen:w,onClose:()=>y(!1)})]})},b=t(64528),g=()=>{let[e,s]=(0,c.useState)(!1),{isLaunchingSoon:t}=(0,b.Gd)();return((0,c.useEffect)(()=>{s(!0)},[]),!e||t)?null:(0,r.jsx)(N,{})}},2778:function(){}}]);