(()=>{var e={};e.id=1978,e.ids=[1978],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},24541:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(76469),r(51806),r(12523);var t=r(23191),a=r(88716),n=r(37922),i=r.n(n),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d=["",{children:["order-confirmed",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,76469)),"E:\\ankkorwoo\\ankkor\\src\\app\\order-confirmed\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\order-confirmed\\page.tsx"],m="/order-confirmed/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/order-confirmed/page",pathname:"/order-confirmed",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13417:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},96799:(e,s,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},54039:(e,s,r)=>{Promise.resolve().then(r.bind(r,83846))},64305:(e,s,r)=>{Promise.resolve().then(r.bind(r,40927))},28916:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},48705:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},14228:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Truck",[["path",{d:"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11",key:"hs4xqm"}],["path",{d:"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2",key:"11fp61"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}]])},35047:(e,s,r)=>{"use strict";var t=r(77389);r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},83846:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var t=r(10326);r(17577);var a=r(33265);let n=()=>t.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,t.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),i=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>t.jsx(n,{})});function o(){return t.jsx("div",{className:"container mx-auto py-20",children:t.jsx(i,{})})}},40927:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>m});var t=r(10326),a=r(17577),n=r(35047),i=r(54659),o=r(28916),l=r(48705),d=r(14228),c=r(91664);function m(){let e=(0,n.useRouter)();(0,n.useSearchParams)();let[s,r]=(0,a.useState)(null);return s?t.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[t.jsx("div",{className:"mb-8",children:t.jsx("div",{className:"mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center",children:t.jsx(i.Z,{className:"w-12 h-12 text-green-600"})})}),(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-3xl font-serif mb-4 text-gray-900",children:"Thank You for Your Order!"}),t.jsx("p",{className:"text-lg text-gray-600 mb-6",children:"Your order has been successfully placed and is being processed."}),(0,t.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-6 mb-6",children:[t.jsx("h2",{className:"text-lg font-medium mb-2",children:"Order Details"}),(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[t.jsx("span",{className:"text-gray-600",children:"Order ID:"}),(0,t.jsxs)("span",{className:"font-mono text-lg font-medium text-[#2c2c27]",children:["#",s]})]})]})]}),(0,t.jsxs)("div",{className:"mb-8",children:[t.jsx("h3",{className:"text-xl font-medium mb-6",children:"What happens next?"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(o.Z,{className:"w-6 h-6 text-blue-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"Payment Confirmed"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Your payment has been successfully processed"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(l.Z,{className:"w-6 h-6 text-yellow-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"Order Processing"}),t.jsx("p",{className:"text-sm text-gray-600",children:"We're preparing your items for shipment"})]}),(0,t.jsxs)("div",{className:"text-center",children:[t.jsx("div",{className:"mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3",children:t.jsx(d.Z,{className:"w-6 h-6 text-green-600"})}),t.jsx("h4",{className:"font-medium mb-2",children:"On the Way"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Your order will be shipped soon"})]})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",children:[t.jsx("h3",{className:"font-medium mb-2",children:"Order Confirmation Email"}),t.jsx("p",{className:"text-sm text-gray-600",children:"We've sent an order confirmation email with your order details and tracking information. Please check your inbox and spam folder."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx(c.z,{onClick:()=>e.push("/"),className:"w-full md:w-auto bg-[#2c2c27] hover:bg-[#3c3c37] text-white px-8 py-3",children:"Continue Shopping"}),t.jsx("div",{className:"text-center",children:t.jsx("button",{onClick:()=>e.push("/account"),className:"text-[#2c2c27] hover:underline text-sm",children:"View Order History"})})]}),(0,t.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:[t.jsx("h3",{className:"font-medium mb-4",children:"Need Help?"}),t.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"If you have any questions about your order, please don't hesitate to contact us."}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:[t.jsx("span",{className:"font-medium",children:"Email:"})," ",t.jsx("a",{href:"mailto:<EMAIL>",className:"text-[#2c2c27] hover:underline",children:"<EMAIL>"})]}),(0,t.jsxs)("p",{children:[t.jsx("span",{className:"font-medium",children:"Phone:"})," ",t.jsx("a",{href:"tel:+**********",className:"text-[#2c2c27] hover:underline",children:"+91 12345 67890"})]})]})]})]})}):null}},68897:(e,s,r)=>{"use strict";r.d(s,{CustomerProvider:()=>o,O:()=>i});var t=r(10326),a=r(17577);let n=(0,a.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),i=()=>(0,a.useContext)(n),o=({children:e})=>{let[s,r]=(0,a.useState)(null),[i,o]=(0,a.useState)(!1),[l,d]=(0,a.useState)(null),[c,m]=(0,a.useState)(null),u=async e=>{console.log("Login function called - minimal implementation")},x=async e=>{console.log("Register function called - minimal implementation")},h=async e=>(console.log("Update profile function called - minimal implementation"),{}),p=async()=>{console.log("Refresh customer function called - minimal implementation")};return t.jsx(n.Provider,{value:{customer:s,isLoading:i,isAuthenticated:!!s&&!!c,token:c,login:u,register:x,logout:()=>{console.log("Logout function called - minimal implementation"),r(null),m(null)},updateProfile:h,error:l,refreshCustomer:p},children:e})}},91664:(e,s,r)=>{"use strict";r.d(s,{z:()=>l});var t=r(10326);r(17577);var a=r(34214),n=r(79360),i=r(51223);let o=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:s,size:r,asChild:n=!1,...l}){let d=n?a.g7:"button";return t.jsx(d,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:r,className:e})),...l})}},75367:(e,s,r)=>{"use strict";r.d(s,{ToastProvider:()=>u});var t=r(10326),a=r(17577),n=r(92148),i=r(86462),o=r(54659),l=r(87888),d=r(18019),c=r(94019);let m=(0,a.createContext)(void 0);function u({children:e}){let[s,r]=(0,a.useState)([]);return(0,t.jsxs)(m.Provider,{value:{toasts:s,addToast:(e,s="info",t=3e3)=>{let a=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:a,message:e,type:s,duration:t}])},removeToast:e=>{r(s=>s.filter(s=>s.id!==e))}},children:[e,t.jsx(h,{})]})}function x({toast:e,onRemove:s}){return(0,t.jsxs)(n.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[t.jsx(()=>{switch(e.type){case"success":return t.jsx(o.Z,{className:"h-5 w-5"});case"error":return t.jsx(l.Z,{className:"h-5 w-5"});default:return t.jsx(d.Z,{className:"h-5 w-5"})}},{}),t.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),t.jsx("button",{onClick:s,className:"ml-4 text-gray-400 hover:text-gray-600",children:t.jsx(c.Z,{className:"h-4 w-4"})})]})}function h(){let{toasts:e,removeToast:s}=function(){let e=(0,a.useContext)(m);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return t.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.jsx(i.M,{children:e.map(e=>t.jsx(x,{toast:e,onRemove:()=>s(e.id)},e.id))})})}},51223:(e,s,r)=>{"use strict";r.d(s,{cn:()=>n});var t=r(41135),a=r(31009);function n(...e){return(0,a.m6)((0,t.W)(e))}},51806:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u,metadata:()=>m});var t=r(19510),a=r(10527),n=r.n(a),i=r(36822),o=r.n(i);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let m={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function u({children:e}){return t.jsx("html",{lang:"en",children:t.jsx("body",{className:`${n().variable} ${o().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:t.jsx(c,{children:t.jsx(d,{children:t.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},76469:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\order-confirmed\page.tsx#default`)},5023:()=>{}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,3373,2325],()=>r(24541));module.exports=t})();