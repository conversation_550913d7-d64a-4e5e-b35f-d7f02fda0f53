"use strict";(()=>{var e={};e.id=420,e.ids=[420],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93690:e=>{e.exports=import("graphql-request")},64384:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{originalPathname:()=>m,patchFetch:()=>c,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>l});var a=r(49303),n=r(88716),o=r(60670),i=r(6078),u=e([i]);i=(u.then?(await u)():u)[0];let p=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/user/route",pathname:"/api/auth/user",filename:"route",bundlePath:"app/api/auth/user/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\auth\\user\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:h}=p,m="/api/auth/user/route";function c(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:l})}s()}catch(e){s(e)}})},6078:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>u,dynamic:()=>c});var a=r(87070),n=r(93690),o=r(70591),i=e([n]);n=(i.then?(await i)():i)[0];let c="force-dynamic",p=`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
    }
  }
`;async function u(e){try{let t=e.headers.get("Authorization");if(!t||!t.startsWith("Bearer "))return a.NextResponse.json({success:!1,message:"Unauthorized: No token provided"},{status:401});let r=t.split(" ")[1];if(!r)return a.NextResponse.json({success:!1,message:"Unauthorized: Invalid token format"},{status:401});try{let e=(0,o.o)(r),t=Date.now()/1e3;if(e.exp<t)return a.NextResponse.json({success:!1,message:"Unauthorized: Token expired"},{status:401})}catch(e){return console.error("Error decoding token:",e),a.NextResponse.json({success:!1,message:"Unauthorized: Invalid token"},{status:401})}let s=process.env.WOOCOMMERCE_GRAPHQL_URL||"",i=new n.GraphQLClient(s,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`}}),u=await i.request(p);if(!u.customer)return a.NextResponse.json({success:!1,message:"Unauthorized: Invalid user"},{status:401});return a.NextResponse.json({success:!0,user:u.customer})}catch(e){return console.error("Auth API error:",e),a.NextResponse.json({success:!1,message:"Server error during authentication"},{status:500})}}s()}catch(e){s(e)}})},70591:(e,t,r)=>{r.d(t,{o:()=>a});class s extends Error{}function a(e,t){let r;if("string"!=typeof e)throw new s("Invalid token specified: must be a string");t||(t={});let a=!0===t.header?0:1,n=e.split(".")[a];if("string"!=typeof n)throw new s(`Invalid token specified: missing part #${a+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(n)}catch(e){throw new s(`Invalid token specified: invalid base64 for part #${a+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new s(`Invalid token specified: invalid json for part #${a+1} (${e.message})`)}}s.prototype.name="InvalidTokenError"}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(64384));module.exports=s})();