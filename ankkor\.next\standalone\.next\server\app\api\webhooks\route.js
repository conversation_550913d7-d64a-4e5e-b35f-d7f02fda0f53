"use strict";(()=>{var e={};e.id=8877,e.ids=[8877],e.modules={45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},91366:(e,o,r)=>{r.r(o),r.d(o,{originalPathname:()=>w,patchFetch:()=>x,requestAsyncStorage:()=>b,routeModule:()=>g,serverHooks:()=>v,staticGenerationAsyncStorage:()=>k});var t={};r.r(t),r.d(t,{GET:()=>h,POST:()=>p});var a=r(49303),s=r(88716),n=r(60670),i=r(87070),c=r(84770),d=r.n(c),u=r(57708);let l=async e=>{let o=await e.blob(),r=await o.arrayBuffer();return Buffer.from(r)};async function p(e){try{let o;let r=(await l(e)).toString("utf8"),t=e.headers.get("x-wc-webhook-signature"),a=e.headers.get("x-wc-webhook-topic")||"",s=e.headers.get("x-wc-webhook-event")||"",n=e.headers.get("x-wc-webhook-source");if(!t)return i.NextResponse.json({error:"Missing signature header"},{status:401});{let e=process.env.WOOCOMMERCE_WEBHOOK_SECRET||"";if(d().createHmac("sha256",e).update(r).digest("base64")!==t)return console.error("Signature validation failed - Webhook security compromised"),i.NextResponse.json({error:"Signature validation failed"},{status:401})}try{o=JSON.parse(r)}catch(e){return console.error("Invalid JSON body:",e),i.NextResponse.json({error:"Invalid JSON body"},{status:400})}switch(console.log(`Received WooCommerce webhook: ${a} - ${s} from ${n}`),a){case"product.updated":console.log("Processing product update",o.id),(0,u.revalidatePath)(`/product/${o.slug}`),(0,u.revalidatePath)("/collection"),(0,u.revalidatePath)("/");break;case"product.deleted":console.log("Processing product deletion",o.id),(0,u.revalidatePath)("/collection"),(0,u.revalidatePath)("/");break;case"order.created":console.log("Processing new order",o.id);break;case"order.updated":console.log("Processing order update",o.id);break;case"product_variation.updated":console.log("Processing product variation update",o.id),o.parent_id&&((0,u.revalidatePath)("/collection"),(0,u.revalidatePath)("/"));break;default:console.log(`Unhandled webhook topic: ${a}`)}return i.NextResponse.json({success:!0,topic:a,event:s,processed:Date.now()},{status:200})}catch(e){return console.error("Error processing webhook:",e),i.NextResponse.json({error:e.message},{status:500})}}async function h(){return i.NextResponse.json({message:"WooCommerce webhook endpoint active"},{status:200})}let g=new a.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/webhooks/route",pathname:"/api/webhooks",filename:"route",bundlePath:"app/api/webhooks/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\webhooks\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:b,staticGenerationAsyncStorage:k,serverHooks:v}=g,w="/api/webhooks/route";function x(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:k})}}};var o=require("../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),t=o.X(0,[8948,5972,7708],()=>r(91366));module.exports=t})();