(()=>{var e={};e.id=5914,e.ids=[5914],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30209:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>p,routeModule:()=>x,tree:()=>l}),t(49708),t(51806),t(12523);var n=t(23191),a=t(88716),s=t(37922),o=t.n(s),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(r,c);let l=["",{children:["sign-up",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,49708)),"E:\\ankkorwoo\\ankkor\\src\\app\\sign-up\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],p=["E:\\ankkorwoo\\ankkor\\src\\app\\sign-up\\page.tsx"],d="/sign-up/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/sign-up/page",pathname:"/sign-up",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9332:(e,r,t)=>{Promise.resolve().then(t.bind(t,32457))},49708:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>s});var n=t(19510),a=t(55361);let s={title:"Create Account | Ankkor",description:"Create an Ankkor account to enjoy a personalized shopping experience, track orders, and more."};function o(){return n.jsx("div",{className:"container mx-auto py-12 px-4",children:(0,n.jsxs)("div",{className:"max-w-4xl mx-auto",children:[n.jsx("h1",{className:"text-3xl font-serif mb-8 text-center",children:"Create Account"}),n.jsx(a.Z,{mode:"register"}),n.jsx("div",{className:"mt-8 text-center",children:(0,n.jsxs)("p",{className:"text-gray-600",children:["Already have an account?"," ",n.jsx("a",{href:"/sign-in",className:"text-[#2c2c27] underline hover:text-[#8a8778]",children:"Sign in here"})]})})]})})}},55361:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});let n=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\auth\AuthForm.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[8948,3373,2696,4154],()=>t(30209));module.exports=n})();