exports.id=1067,exports.ids=[1067],exports.modules={13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},65789:(e,t,r)=>{Promise.resolve().then(r.bind(r,77321)),Promise.resolve().then(r.bind(r,41595)),Promise.resolve().then(r.bind(r,81201)),Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,30292)),Promise.resolve().then(r.bind(r,2861)),Promise.resolve().then(r.bind(r,75367))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var a=r(10326);r(17577);var s=r(33265);let o=()=>a.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,a.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),a.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),i=(0,s.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>a.jsx(o,{})});function n(){return a.jsx("div",{className:"container mx-auto py-20",children:a.jsx(i,{})})}},6134:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var a=r(10326),s=r(17577),o=r(92148),i=r(75290);let n=({onClick:e,isDisabled:t=!1,text:r="Proceed to Checkout",loadingText:n="Processing..."})=>{let[l,c]=(0,s.useState)(!1),[d,u]=(0,s.useState)(null),m=async()=>{if(!t&&!l){c(!0),u(null);try{await e()}catch(e){console.error("Checkout button error:",e),u(e instanceof Error?e.message:"An error occurred"),c(!1)}}};return(0,a.jsxs)("div",{className:"w-full",children:[a.jsx(o.E.button,{whileHover:t||l?{}:{scale:1.02},whileTap:t||l?{}:{scale:.98},transition:{duration:.2},className:`w-full py-3 px-4 rounded-md font-medium text-center transition-colors ${t?"bg-gray-300 text-gray-500 cursor-not-allowed":l?"bg-indigo-500 text-white cursor-wait":"bg-indigo-600 text-white hover:bg-indigo-700"}`,onClick:m,disabled:t||l,children:l?(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[a.jsx(i.Z,{className:"animate-spin h-4 w-4 mr-2"}),n]}):r}),d&&a.jsx("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded-md",children:a.jsx("p",{className:"text-xs text-red-600",children:d})})]})}},96094:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Z:()=>$});var s=r(10326),o=r(17577),i=r(46226),n=r(35047),l=r(86462),c=r(92148),d=r(34565),u=r(94019),m=r(24230),h=r(37202),p=r(21405),f=r(62783),x=r(11019),g=r(83855),y=r(98091),v=r(86806),b=r(15725),w=r(68211),j=r(68471),N=r(91664),k=r(75367),C=r(6134),I=r(68897),P=e([b]);b=(P.then?(await P)():P)[0];let S=({item:e,updateQuantity:t,removeFromCart:r,formatPrice:a})=>(0,s.jsxs)("li",{className:"flex gap-4 py-4 border-b",children:[s.jsx("div",{className:"relative h-20 w-20 bg-gray-100 flex-shrink-0",children:e.image?.url&&s.jsx(i.default,{src:e.image.url,alt:e.image.altText||e.name,fill:!0,sizes:"80px",className:"object-cover",priority:!1})}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col",children:[s.jsx("h4",{className:"text-sm font-medium line-clamp-2",children:e.name}),e.attributes&&e.attributes.length>0&&s.jsx("div",{className:"mt-1 text-xs text-gray-500",children:e.attributes.map((t,r)=>(0,s.jsxs)("span",{children:[t.name,": ",t.value,r<e.attributes.length-1?", ":""]},t.name))}),s.jsx("div",{className:"mt-1 text-sm font-medium",children:e.price&&"string"==typeof e.price&&e.price.toString().includes("₹")?e.price:`${j.J6}${a(e.price||"0")}`}),(0,s.jsxs)("div",{className:"mt-2 flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center border border-gray-300",children:[s.jsx("button",{onClick:()=>{e.quantity>1&&t(e.id,e.quantity-1)},disabled:e.quantity<=1,className:"px-2 py-1 hover:bg-gray-100 disabled:opacity-50","aria-label":"Decrease quantity",children:s.jsx(x.Z,{className:"h-3 w-3"})}),s.jsx("span",{className:"px-2 py-1 text-sm",children:e.quantity}),s.jsx("button",{onClick:()=>{t(e.id,e.quantity+1)},className:"px-2 py-1 hover:bg-gray-100","aria-label":"Increase quantity",children:s.jsx(g.Z,{className:"h-3 w-3"})})]}),s.jsx("button",{onClick:()=>{r(e.id)},className:"p-1 hover:bg-gray-100 rounded-full","aria-label":"Remove item",children:s.jsx(y.Z,{className:"h-4 w-4 text-gray-500"})})]})]})]}),$=({isOpen:e,toggleCart:t})=>{let[r,a]=(0,o.useState)(!1),[i,x]=(0,o.useState)(null),[g,y]=(0,o.useState)(!1),[j,P]=(0,o.useState)(!1),[$,E]=(0,o.useState)({}),T=(0,n.useRouter)(),{isAuthenticated:q,customer:L,token:U}=(0,I.O)(),D=(0,v.rY)(),{items:_,itemCount:A,removeCartItem:F,updateCartItem:R,clearCart:z,error:O,setError:Z}=D,W=(0,k.p)(),G=e=>{try{let t="string"==typeof e?parseFloat(e):e;if(isNaN(t))return"0.00";return t.toFixed(2)}catch(e){return console.error("Error formatting price:",e),"0.00"}};(0,o.useEffect)(()=>{console.log("Cart items:",_),console.log("Cart subtotal calculation:");let e=0;_.forEach(t=>{let r=0;if("string"==typeof t.price){let e=t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");r=parseFloat(e)}else r=t.price;let a=r*t.quantity;console.log(`Item: ${t.name}, Price: ${t.price}, Cleaned price: ${r}, Quantity: ${t.quantity}, Total: ${a}`),e+=a}),console.log(`Manual subtotal calculation: ${e}`),console.log(`Store subtotal calculation: ${D.subtotal()}`)},[_,D]);let V=_.reduce((e,t)=>{let r=0;if("string"==typeof t.price){let e=t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");r=parseFloat(e)}else r=t.price;return isNaN(r)?(console.warn(`Invalid price for item ${t.id}: ${t.price}`),e):e+r*t.quantity},0),Q=G(V);(0,o.useEffect)(()=>{(async()=>{let e={};for(let t of _)try{if(!$[t.productId])try{let r=await b.gk(t.productId);r?.slug?e[t.productId]=r.slug:(console.warn(`Product with ID ${t.productId} has no slug`),e[t.productId]="product-not-found")}catch(r){console.error(`Failed to load handle for product ${t.productId}:`,r),e[t.productId]="product-not-found",r.message?.includes("No product ID was found")&&console.warn(`Product with ID ${t.productId} not found in WooCommerce, but keeping in cart`)}}catch(e){console.error(`Error processing product ${t.productId}:`,e)}Object.keys(e).length>0&&E(t=>({...t,...e}))})()},[_,$]);let M=async(e,t)=>{y(!0);try{await R(e,t)}catch(e){console.error("Error updating quantity:",e),Z(e instanceof Error?e.message:"Failed to update quantity")}finally{y(!1)}},B=async e=>{try{await F(e)}catch(e){console.error("Error removing item:",e),Z(e instanceof Error?e.message:"Failed to remove item")}},H=async()=>{a(!0),x(null);try{if(0===_.length)throw Error("Your cart is empty");if(!q)throw Error("Please log in to continue with checkout");t(),T.push("/checkout")}catch(e){console.error("Checkout error:",e),x(e instanceof Error?e.message:"An error occurred during checkout"),W.addToast(e instanceof Error?e.message:"An error occurred during checkout","error"),a(!1)}},J=async()=>{P(!0),x(null);try{await H()}catch(e){console.error("Retry error:",e),x(e instanceof Error?e.message:"Retry failed")}finally{P(!1)}},K=_.length>0;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(l.M,{children:e&&s.jsx(c.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:t,className:"fixed inset-0 bg-black/50 z-40","aria-hidden":"true"})}),s.jsx(l.M,{children:e&&(0,s.jsxs)(c.E.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"tween",ease:"easeInOut",duration:.3},className:"fixed top-0 right-0 h-full w-full max-w-md bg-white z-50 shadow-xl flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,s.jsxs)("h2",{className:"text-lg font-medium flex items-center gap-2",children:[s.jsx(d.Z,{className:"h-5 w-5"}),"Your Cart"]}),s.jsx("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-full","aria-label":"Close cart",children:s.jsx(u.Z,{className:"h-5 w-5"})})]}),(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto p-4",children:[!K&&!O&&(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center p-4",children:[s.jsx(d.Z,{className:"h-12 w-12 text-gray-300 mb-2"}),s.jsx("h3",{className:"text-lg font-medium mb-1",children:"Your cart is empty"}),s.jsx("p",{className:"text-gray-500 mb-4",children:"Looks like you haven't added any items yet."}),(0,s.jsxs)(N.z,{onClick:t,className:"flex items-center gap-2",children:["Continue Shopping",s.jsx(m.Z,{className:"h-4 w-4"})]})]}),O&&(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center p-4",children:[s.jsx(h.Z,{className:"h-12 w-12 text-red-500 mb-2"}),s.jsx("h3",{className:"text-lg font-medium mb-1",children:"Something went wrong"}),s.jsx("p",{className:"text-gray-500 mb-4",children:O}),(0,s.jsxs)(N.z,{onClick:()=>Z(null),className:"flex items-center gap-2",variant:"outline",children:[s.jsx(p.Z,{className:"h-4 w-4"}),"Try Again"]})]}),K&&s.jsx("ul",{className:"divide-y",children:_.map(e=>s.jsx(S,{item:e,updateQuantity:M,removeFromCart:B,formatPrice:G},e.id))})]}),(0,s.jsxs)("div",{className:"border-t p-4 space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between font-medium",children:[s.jsx("span",{children:"Subtotal"}),(0,s.jsxs)("span",{children:["₹",Q]})]}),(0,s.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[s.jsx("span",{children:"Total"}),(0,s.jsxs)("span",{children:["₹",Q]})]}),s.jsx("div",{className:"mb-4",children:s.jsx(C.Z,{onClick:H,isDisabled:!K||g,text:"Proceed to Checkout",loadingText:"Preparing Checkout..."})}),i&&s.jsx("div",{className:"bg-red-50 border border-red-200 p-3 rounded-md",children:(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx(h.Z,{className:"h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-red-700",children:i}),s.jsx("button",{onClick:J,disabled:j,className:"mt-2 text-xs flex items-center text-red-700 hover:text-red-800",children:j?(0,s.jsxs)(s.Fragment,{children:[s.jsx(w.Z,{className:"h-3 w-3 animate-spin mr-1"}),"Retrying..."]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(p.Z,{className:"h-3 w-3 mr-1"}),"Try again"]})})]})]})}),!navigator.onLine&&s.jsx("div",{className:"bg-yellow-50 border border-yellow-200 p-3 rounded-md",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(f.Z,{className:"h-4 w-4 text-yellow-500 mr-2"}),s.jsx("p",{className:"text-xs text-yellow-700",children:"You appear to be offline. Please check your internet connection."})]})})]}),s.jsx("button",{onClick:z,className:"w-full text-center text-gray-500 text-sm mt-2 hover:text-gray-700",disabled:r||g,children:"Clear Cart"})]})})]})};a()}catch(e){a(e)}})},77321:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{default:()=>u,j:()=>d});var s=r(10326),o=r(17577),i=r(86806),n=r(96094),l=e([n]);n=(l.then?(await l)():l)[0];let c=(0,o.createContext)(void 0),d=()=>{let e=(0,o.useContext)(c);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e},u=({children:e})=>{let t=(0,i.rY)(),[r,a]=(0,o.useState)(!1),l={openCart:()=>a(!0),closeCart:()=>a(!1),toggleCart:()=>a(e=>!e),isOpen:r,itemCount:t.itemCount};return(0,s.jsxs)(c.Provider,{value:l,children:[e,s.jsx(n.Z,{isOpen:l.isOpen,toggleCart:l.toggleCart})]})};a()}catch(e){a(e)}})},41595:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(10326),s=r(17577),o=r(90434),i=r(46226),n=r(34738);let l=()=>(0,a.jsxs)("footer",{className:"bg-[#2c2c27] text-[#f4f3f0]",children:[a.jsx("div",{className:"py-16",children:a.jsx("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-12",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{children:a.jsx(i.default,{src:"/logo.PNG",alt:"Ankkor",width:160,height:50,className:"h-12 w-auto invert"})}),a.jsx("p",{className:"text-[#d5d0c3] text-sm leading-relaxed",children:"Timeless menswear crafted with exceptional materials and artisanal techniques, designed for the discerning gentleman who values understated luxury."}),a.jsx("div",{className:"flex space-x-6",children:a.jsx("a",{href:"https://www.instagram.com/ankkorindia?igsh=bHQwZjJxZHI1c2Iz&utm_source=qr",target:"_blank",rel:"noopener noreferrer",className:"text-[#8a8778] hover:text-[#f4f3f0] transition-colors",children:a.jsx(n.Z,{className:"h-5 w-5"})})})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-serif text-lg mb-6",children:"Shop"}),(0,a.jsxs)("ul",{className:"space-y-3 text-sm",children:[a.jsx("li",{children:a.jsx(o.default,{href:"/collection/shirts",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Shirts"})}),a.jsx("li",{children:a.jsx(o.default,{href:"/collection",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"View All"})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-serif text-lg mb-6",children:"Customer Service"}),(0,a.jsxs)("ul",{className:"space-y-3 text-sm",children:[a.jsx("li",{children:a.jsx(o.default,{href:"/customer-service/contact",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Contact Us"})}),a.jsx("li",{children:a.jsx(o.default,{href:"/shipping-policy",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Shipping Policy"})}),a.jsx("li",{children:a.jsx(o.default,{href:"/return-policy",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Return Policy"})}),a.jsx("li",{children:a.jsx(o.default,{href:"/customer-service/size-guide",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Size Guide"})})]})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-serif text-lg mb-6",children:"Company"}),(0,a.jsxs)("ul",{className:"space-y-3 text-sm",children:[a.jsx("li",{children:a.jsx(o.default,{href:"/about",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"About Us"})}),a.jsx("li",{children:a.jsx(o.default,{href:"/privacy-policy",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Privacy Policy"})}),a.jsx("li",{children:a.jsx(o.default,{href:"/terms-of-service",className:"text-[#d5d0c3] hover:text-[#f4f3f0] transition-colors",children:"Terms of Service"})})]})]})]})})}),a.jsx("div",{className:"border-t border-[#3d3d35] py-6",children:a.jsx("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-4",children:[(0,a.jsxs)("p",{className:"text-[#8a8778] text-xs",children:["\xa9 ",new Date().getFullYear()," Ankkor. All rights reserved."]}),a.jsx("a",{href:"https://abhijeets-portfolio.vercel.app",target:"_blank",rel:"noopener noreferrer",className:"text-[#8a8778] text-xs hover:text-[#f4f3f0] transition-colors",children:"Contact Developer"})]}),(0,a.jsxs)("div",{className:"flex space-x-8",children:[a.jsx(o.default,{href:"/privacy-policy",className:"text-[#8a8778] text-xs hover:text-[#f4f3f0] transition-colors",children:"Privacy Policy"}),a.jsx(o.default,{href:"/terms-of-service",className:"text-[#8a8778] text-xs hover:text-[#f4f3f0] transition-colors",children:"Terms of Service"})]})]})})})]});var c=r(30292);let d=()=>{let[e,t]=(0,s.useState)(!1),{isLaunchingSoon:r}=(0,c.Gd)();return((0,s.useEffect)(()=>{t(!0)},[]),!e||r)?null:a.jsx(l,{})}},16681:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Z:()=>j});var s=r(10326),o=r(17577),i=r.n(o),n=r(90434),l=r(46226),c=r(88307),d=r(79635),u=r(71810),m=r(67427),h=r(34565),p=r(90748),f=r(94019),x=r(76066),g=r(96040),y=r(77321),v=r(68897),b=r(20711),w=e([g,y,b]);[g,y,b]=w.then?(await w)():w;let j=()=>{let{toggleCart:e,itemCount:t}=(0,y.j)(),{items:r}=(0,g.Y)(),a=r.length,[w,j]=i().useState(!1),[N,k]=(0,o.useState)(!1),{customer:C,isAuthenticated:I,logout:P}=(0,v.O)();i().useEffect(()=>{let e=()=>{window.scrollY>10?j(!0):j(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),i().useEffect(()=>{let e=e=>{"Escape"===e.key&&N&&k(!1)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[N]),i().useEffect(()=>{console.log("Navbar: Authentication state",{isAuthenticated:I,customer:C?`${C.firstName} ${C.lastName}`:"not logged in"})},[I,C]);let S=`fixed top-0 left-0 right-0 z-[99] transition-all duration-300 ${w?"bg-[#f8f8f5] h-16 shadow-sm":"bg-transparent h-24 py-4"}`;return(0,s.jsxs)(s.Fragment,{children:[s.jsx("nav",{className:S,children:(0,s.jsxs)("div",{className:"container mx-auto px-4 h-full flex items-center justify-between",children:[s.jsx(n.default,{href:"/",className:"font-serif text-2xl font-bold text-[#2c2c27] relative z-10",children:s.jsx(l.default,{src:"/logo.PNG",alt:"Ankkor",width:160,height:50,priority:!0,className:`${w?"h-10":"h-14"} w-auto transition-all duration-300`})}),(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-12 relative z-10",children:[(0,s.jsxs)("div",{className:"group relative",children:[s.jsx("button",{className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1 flex items-center",children:"Collections"}),s.jsx("div",{className:"absolute left-1/2 transform -translate-x-1/2 pt-2 w-[220px] opacity-0 invisible translate-y-1 group-hover:translate-y-0 group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out",children:(0,s.jsxs)("div",{className:"bg-[#f8f8f5] border border-[#e5e2d9] rounded-md shadow-md p-2",children:[s.jsx(n.default,{href:"/collection/shirts",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Shirts"}),s.jsx("div",{className:"h-px bg-[#e5e2d9] my-2"}),s.jsx(n.default,{href:"/collection",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"View All Collections"})]})})]}),s.jsx(n.default,{href:"/about",className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1",children:"Heritage"}),s.jsx(n.default,{href:"/customer-service",className:"text-[#2c2c27] text-sm uppercase tracking-wider hover:text-[#8a8778] transition-colors py-2 px-1",children:"Customer Service"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-6 relative z-10",children:[s.jsx("button",{onClick:()=>k(!0),className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2","aria-label":"Search",children:s.jsx(c.Z,{className:"h-5 w-5"})}),(0,s.jsxs)("div",{className:"group relative",children:[s.jsx("button",{className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2",children:s.jsx(d.Z,{className:"h-5 w-5"})}),s.jsx("div",{className:"absolute right-0 pt-2 w-[220px] opacity-0 invisible translate-y-1 group-hover:translate-y-0 group-hover:opacity-100 group-hover:visible transition-all duration-200 ease-out",children:s.jsx("div",{className:"bg-[#f8f8f5] border border-[#e5e2d9] rounded-md shadow-md p-2",children:I?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"px-4 py-1.5 text-sm text-[#5c5c52]",children:["Hello, ",C?.firstName||"there"]}),s.jsx("div",{className:"h-px bg-[#e5e2d9] my-2"}),s.jsx(n.default,{href:"/account",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"My Account"}),s.jsx(n.default,{href:"/account?tab=orders",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Order History"}),s.jsx(n.default,{href:"/wishlist",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Wishlist"}),s.jsx(n.default,{href:"/woocommerce-checkout-test",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded text-xs bg-gray-100",children:"Test WooCommerce Checkout"}),s.jsx("div",{className:"h-px bg-[#e5e2d9] my-2"}),(0,s.jsxs)("button",{onClick:P,className:"flex items-center text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer w-full py-2 px-4 rounded",children:[s.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Sign Out"]})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(n.default,{href:"/sign-in",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Sign In"}),s.jsx(n.default,{href:"/sign-up",className:"block text-[#2c2c27] hover:bg-[#f4f3f0] cursor-pointer py-2 px-4 rounded",children:"Create Account"})]})})})]}),(0,s.jsxs)(n.default,{href:"/wishlist",className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors p-2 relative",children:[s.jsx(m.Z,{className:"h-5 w-5"}),a>0&&s.jsx("span",{className:"absolute -top-1 -right-1 bg-[#2c2c27] text-[#f4f3f0] text-xs rounded-full h-5 w-5 flex items-center justify-center",children:a})]}),(0,s.jsxs)("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),e()},className:"text-[#2c2c27] hover:text-[#8a8778] transition-colors relative p-2","aria-label":"Shopping cart",children:[s.jsx(h.Z,{className:"h-5 w-5"}),t>0&&s.jsx("span",{className:"absolute -top-1 -right-1 bg-[#2c2c27] text-[#f4f3f0] text-xs rounded-full h-5 w-5 flex items-center justify-center",children:t})]}),(0,s.jsxs)(x.yo,{children:[s.jsx(x.aM,{asChild:!0,children:s.jsx("button",{className:"md:hidden text-[#2c2c27] hover:text-[#8a8778] transition-colors","aria-label":"Menu",children:s.jsx(p.Z,{className:"h-6 w-6"})})}),s.jsx(x.ue,{side:"right",className:"bg-[#f8f8f5] w-[300px] p-0",children:(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[s.jsx("div",{className:"p-6 border-b border-[#e5e2d9]",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:"font-serif text-xl font-bold text-[#2c2c27]",children:s.jsx(l.default,{src:"/logo.PNG",alt:"Ankkor",width:120,height:40,className:"h-10 w-auto"})}),s.jsx(x.sw,{asChild:!0,children:s.jsx("button",{className:"text-[#2c2c27]","aria-label":"Close menu",children:s.jsx(f.Z,{className:"h-5 w-5"})})})]})}),s.jsx("div",{className:"flex-1 overflow-auto py-6 px-6",children:(0,s.jsxs)("nav",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Collections"}),(0,s.jsxs)("ul",{className:"space-y-3",children:[s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/collection/shirts",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Shirts"})})}),s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/collection",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"View All Collections"})})})]})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Company"}),(0,s.jsxs)("ul",{className:"space-y-3",children:[s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/about",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Heritage"})})}),s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/customer-service",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Customer Service"})})})]})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-serif text-sm uppercase tracking-wider text-[#8a8778] mb-3",children:"Account"}),s.jsx("ul",{className:"space-y-3",children:I?(0,s.jsxs)(s.Fragment,{children:[s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/account",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"My Account"})})}),s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/account?tab=orders",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Order History"})})}),s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/wishlist",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Wishlist"})})}),s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx("button",{onClick:P,className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Sign Out"})})})]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/sign-in",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Sign In"})})}),s.jsx("li",{children:s.jsx(x.sw,{asChild:!0,children:s.jsx(n.default,{href:"/sign-up",className:"block text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:"Create Account"})})})]})})]})]})})]})})]}),!I&&(0,s.jsxs)(n.default,{href:"/sign-up",className:"hidden md:flex items-center ml-3 bg-gradient-to-b from-[#2c2c27] to-[#3a3a34] text-[#f8f8f5] border border-[#2c2c27] font-medium text-sm uppercase tracking-wider px-6 py-2.5 rounded-sm shadow-sm hover:shadow-md hover:from-[#3a3a34] hover:to-[#2c2c27] transition-all duration-200",children:[s.jsx(d.Z,{className:"h-4 w-4 mr-2 opacity-80"}),"Sign Up"]})]})]})}),s.jsx("div",{className:w?"h-16":"h-20"}),s.jsx(b.Z,{isOpen:N,onClose:()=>k(!1)})]})};a()}catch(e){a(e)}})},81201:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{default:()=>c});var s=r(10326),o=r(17577),i=r(16681),n=r(30292),l=e([i]);i=(l.then?(await l)():l)[0];let c=()=>{let[e,t]=(0,o.useState)(!1),{isLaunchingSoon:r}=(0,n.Gd)();return((0,o.useEffect)(()=>{t(!0)},[]),!e||r)?null:s.jsx(i.Z,{})};a()}catch(e){a(e)}})},68897:(e,t,r)=>{"use strict";r.d(t,{CustomerProvider:()=>n,O:()=>i});var a=r(10326),s=r(17577);let o=(0,s.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),i=()=>(0,s.useContext)(o),n=({children:e})=>{let[t,r]=(0,s.useState)(null),[i,n]=(0,s.useState)(!1),[l,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(null),m=async e=>{console.log("Login function called - minimal implementation")},h=async e=>{console.log("Register function called - minimal implementation")},p=async e=>(console.log("Update profile function called - minimal implementation"),{}),f=async()=>{console.log("Refresh customer function called - minimal implementation")};return a.jsx(o.Provider,{value:{customer:t,isLoading:i,isAuthenticated:!!t&&!!d,token:d,login:m,register:h,logout:()=>{console.log("Logout function called - minimal implementation"),r(null),u(null)},updateProfile:p,error:l,refreshCustomer:f},children:e})}},30292:(e,t,r)=>{"use strict";r.d(t,{Gd:()=>n,default:()=>c});var a=r(10326),s=r(17577),o=r(60114),i=r(85251);let n=(0,o.Ue)()((0,i.tJ)(e=>({isLaunchingSoon:!1,setIsLaunchingSoon:e=>{console.warn("Changing launch state is disabled in production.")}}),{name:"ankkor-launch-state",storage:{getItem:e=>null,setItem:(e,t)=>{},removeItem:e=>{}}})),l=(0,s.createContext)(void 0),c=({children:e})=>{let t=n(),[r,o]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{o(!0),!1!==t.isLaunchingSoon&&n.setState({isLaunchingSoon:!1})},[t]),a.jsx(l.Provider,{value:t,children:e})}},2861:(e,t,r)=>{"use strict";r.d(t,{default:()=>f,r:()=>u});var a=r(10326),s=r(17577),o=r(35047),i=r(86462),n=r(92148);let l=({size:e="md",variant:t="thread",className:r=""})=>{let s={sm:{container:"w-16 h-16",text:"text-xs"},md:{container:"w-24 h-24",text:"text-sm"},lg:{container:"w-32 h-32",text:"text-base"}};return"thread"===t?(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[(0,a.jsxs)("div",{className:`relative ${s[e].container}`,children:[a.jsx(n.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27",borderRightColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1.5,repeat:1/0,ease:"linear"}}),a.jsx(n.E.div,{className:"absolute inset-2 rounded-full border-2 border-[#e5e2d9]",style:{borderBottomColor:"#8a8778",borderLeftColor:"#8a8778"},animate:{rotate:-360},transition:{duration:2,repeat:1/0,ease:"linear"}}),a.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:a.jsx("div",{className:"w-2 h-2 rounded-full bg-[#2c2c27]"})})]}),a.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${s[e].text}`,children:"Loading Collection"})]}):"fabric"===t?(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[(0,a.jsxs)("div",{className:`relative ${s[e].container} flex items-center justify-center`,children:[a.jsx(n.E.div,{className:"absolute w-1/3 h-1/3 bg-[#e5e2d9]",animate:{rotate:360,scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"}}),a.jsx(n.E.div,{className:"absolute w-1/3 h-1/3 bg-[#8a8778]",animate:{rotate:-360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.3}}),a.jsx(n.E.div,{className:"absolute w-1/3 h-1/3 bg-[#2c2c27]",animate:{rotate:360,scale:[1,.8,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut",delay:.6}})]}),a.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${s[e].text}`,children:"Preparing Your Style"})]}):"button"===t?(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[a.jsx("div",{className:`relative ${s[e].container} flex items-center justify-center`,children:a.jsx("div",{className:"relative flex",children:[0,1,2,3].map(e=>a.jsx(n.E.div,{className:"w-3 h-3 mx-1 rounded-full bg-[#2c2c27] border border-[#8a8778]",animate:{y:[0,-10,0],opacity:[.5,1,.5]},transition:{duration:1,repeat:1/0,ease:"easeInOut",delay:.2*e}},e))})}),a.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${s[e].text}`,children:"Tailoring Experience"})]}):(0,a.jsxs)("div",{className:`flex flex-col items-center justify-center ${r}`,children:[a.jsx("div",{className:`relative ${s[e].container}`,children:a.jsx(n.E.div,{className:"absolute inset-0 rounded-full border-2 border-[#e5e2d9]",style:{borderTopColor:"#2c2c27"},animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}})}),a.jsx("p",{className:`mt-4 font-serif text-[#5c5c52] ${s[e].text}`,children:"Loading"})]})},c=({isLoading:e,variant:t="thread"})=>a.jsx(i.M,{children:e&&a.jsx(n.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3},className:"fixed inset-0 z-[200] flex items-center justify-center bg-[#f8f8f5]/90 backdrop-blur-sm",children:a.jsx(l,{variant:t,size:"lg"})})}),d=(0,s.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),u=()=>(0,s.useContext)(d),m={"/collection":"fabric","/collection/shirts":"fabric","/collection/polos":"fabric","/product":"thread","/about":"button","/customer-service":"button","/account":"thread","/wishlist":"thread"},h=({setIsLoading:e,setVariant:t})=>{let r;let a=(0,o.usePathname)();try{r=(0,o.useSearchParams)()}catch(e){r=null}return(0,s.useEffect)(()=>{e(!0),t(m["/"+a.split("/")[1]]||m[a]||"thread");let r=setTimeout(()=>{e(!1)},1200);return()=>clearTimeout(r)},[a,r,e,t]),null},p=()=>a.jsx("div",{className:"hidden",children:"Loading route..."}),f=({children:e})=>{let[t,r]=(0,s.useState)(!1),[o,i]=(0,s.useState)("thread");return(0,a.jsxs)(d.Provider,{value:{isLoading:t,setLoading:r,variant:o,setVariant:i},children:[a.jsx(s.Suspense,{fallback:a.jsx(p,{}),children:a.jsx(h,{setIsLoading:r,setVariant:i})}),e,a.jsx(c,{isLoading:t,variant:o})]})}},20711:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Z:()=>p});var s=r(10326),o=r(17577),i=r(88307),n=r(94019),l=r(24230),c=r(35047),d=r(68211),u=r(46226),m=r(15725),h=e([m]);m=(h.then?(await h)():h)[0];let p=({isOpen:e,onClose:t})=>{let[r,a]=(0,o.useState)(""),[h,p]=(0,o.useState)(!1),[f,x]=(0,o.useState)(!0),[g,y]=(0,o.useState)([]),[v,b]=(0,o.useState)([]),[w,j]=(0,o.useState)(!1),N=(0,o.useRef)(null),k=(0,o.useRef)(null),C=(0,c.useRouter)(),I=function(e,t){let[r,a]=(0,o.useState)(e);return r}(r,0);(0,o.useEffect)(()=>{let t=async()=>{try{x(!0);let e=(await (0,m.Dg)(100)).map(e=>{let t=e.variants?.edges?.[0]?.node||null,r=e.images?.edges?.[0]?.node?.url||"",a=e.collections?.edges?.[0]?.node?.handle||"clothing";return{id:e.id||"",title:e.title||"Untitled Product",handle:e.handle||"",description:e.description||"",image:r,price:t?.price?.amount||e.priceRange?.minVariantPrice?.amount||"0.00",tags:Array.isArray(e.tags)?e.tags:[],category:a}});y(e)}catch(e){console.error("Error loading products for search:",e)}finally{x(!1)}};e&&t()},[e]),(0,o.useEffect)(()=>{e&&N.current&&N.current.focus()},[e]),(0,o.useEffect)(()=>{if(!I.trim()||I.length<2){b([]),j(!1);return}p(!0);let e=I.toLowerCase().split(" ").filter(e=>e.length>0),t=g.filter(t=>!!t&&e.every(e=>{let r=t.title?.toLowerCase().includes(e),a=t.description?.toLowerCase().includes(e),s=t.tags?.some(t=>t.toLowerCase().includes(e)),o=t.category?.toLowerCase().includes(e);return r||a||s||o})).slice(0,5);b(t),j(t.length>0),p(!1)},[I,g]),(0,o.useEffect)(()=>{let e=e=>{k.current&&N.current&&!k.current.contains(e.target)&&!N.current.contains(e.target)&&j(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let P=e=>{e.preventDefault(),r.trim()&&(p(!0),j(!1),C.push(`/search?q=${encodeURIComponent(r.trim())}`),setTimeout(()=>{p(!1),t()},300))},S=e=>{j(!1),C.push(`/product/${e}`),t()};return e?s.jsx("div",{className:"fixed inset-0 z-[102] flex items-start justify-center bg-[#2c2c27]/90 pt-24 px-4",children:(0,s.jsxs)("div",{className:"w-full max-w-2xl bg-[#f8f8f5] rounded-lg shadow-xl overflow-hidden",children:[(0,s.jsxs)("div",{className:"p-4 border-b border-[#e5e2d9]",children:[(0,s.jsxs)("form",{onSubmit:P,className:"relative",children:[s.jsx("input",{ref:N,type:"text",value:r,onChange:e=>a(e.target.value),onKeyDown:e=>{"Escape"===e.key&&t()},placeholder:"Search for products...",className:"w-full pl-10 pr-10 py-3 border-none bg-transparent text-[#2c2c27] placeholder-[#8a8778] focus:outline-none focus:ring-0"}),s.jsx(i.Z,{className:"absolute left-0 top-1/2 -translate-y-1/2 h-5 w-5 text-[#8a8778]"}),s.jsx("button",{type:"button",onClick:t,className:"absolute right-0 top-1/2 -translate-y-1/2 h-8 w-8 flex items-center justify-center text-[#8a8778] hover:text-[#2c2c27] transition-colors",children:s.jsx(n.Z,{className:"h-5 w-5"})})]}),w&&(0,s.jsxs)("div",{ref:k,className:"absolute z-10 mt-1 w-full max-w-2xl bg-[#f8f8f5] border border-[#e5e2d9] rounded-lg shadow-lg overflow-hidden",children:[s.jsx("div",{className:"max-h-96 overflow-y-auto",children:v.map(e=>(0,s.jsxs)("div",{onClick:()=>S(e.handle),className:"flex items-center p-3 hover:bg-[#f4f3f0] cursor-pointer transition-colors border-b border-[#e5e2d9] last:border-0",children:[s.jsx("div",{className:"flex-shrink-0 w-16 h-16 bg-[#f4f3f0] overflow-hidden rounded",children:e.image&&s.jsx(u.default,{src:e.image,alt:e.title,width:64,height:64,className:"w-full h-full object-cover"})}),(0,s.jsxs)("div",{className:"ml-4 flex-1",children:[s.jsx("h4",{className:"text-[#2c2c27] font-medium line-clamp-1",children:e.title}),(0,s.jsxs)("p",{className:"text-[#8a8778] text-sm mt-1",children:["₹",parseFloat(e.price).toFixed(2)]})]}),s.jsx(l.Z,{className:"h-4 w-4 text-[#8a8778] ml-2"})]},e.id))}),s.jsx("div",{className:"p-3 border-t border-[#e5e2d9] bg-[#f4f3f0]",children:(0,s.jsxs)("button",{onClick:P,className:"w-full text-[#2c2c27] text-sm font-medium py-2 flex items-center justify-center",children:["View all results",s.jsx(l.Z,{className:"h-4 w-4 ml-2"})]})})]})]}),s.jsx("div",{className:"p-4 text-[#5c5c52] text-sm",children:f?s.jsx("div",{className:"flex items-center justify-center py-8",children:s.jsx(d.Z,{size:"md",color:"#8a8778"})}):h&&!w?s.jsx("div",{className:"flex items-center justify-center py-8",children:s.jsx(d.Z,{size:"md",color:"#8a8778"})}):(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("p",{className:"font-medium",children:"Popular Searches:"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:["Shirt","Pant","Polo"].map(e=>s.jsx("button",{onClick:()=>{a(e),N.current&&N.current.focus()},className:"px-3 py-1 bg-[#f4f3f0] rounded-full text-[#5c5c52] hover:bg-[#e5e2d9] transition-colors",children:e},e))})]})})]})}):null};a()}catch(e){a(e)}})},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var a=r(10326);r(17577);var s=r(34214),o=r(79360),i=r(51223);let n=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:o=!1,...l}){let c=o?s.g7:"button";return a.jsx(c,{"data-slot":"button",className:(0,i.cn)(n({variant:t,size:r,className:e})),...l})}},68211:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var a=r(10326);r(17577);let s=({size:e="md",color:t="#2c2c27",className:r=""})=>{let s={sm:{container:"w-6 h-6",dot:"w-1 h-1"},md:{container:"w-10 h-10",dot:"w-1.5 h-1.5"},lg:{container:"w-16 h-16",dot:"w-2 h-2"}};return(0,a.jsxs)(a.Fragment,{children:[a.jsx("style",{children:`
        @keyframes loaderRotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
        
        @keyframes loaderDot1 {
          0%, 100% {
            opacity: 0.2;
          }
          25% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot2 {
          0%, 100% {
            opacity: 0.2;
          }
          50% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot3 {
          0%, 100% {
            opacity: 0.2;
          }
          75% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot4 {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.2;
          }
        }
      `}),a.jsx("div",{className:`flex items-center justify-center ${r}`,children:(0,a.jsxs)("div",{className:`relative ${s[e].container}`,children:[a.jsx("div",{className:`absolute top-0 left-1/2 -translate-x-1/2 ${s[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot1 1.5s infinite"}}),a.jsx("div",{className:`absolute top-1/2 right-0 -translate-y-1/2 ${s[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot2 1.5s infinite"}}),a.jsx("div",{className:`absolute bottom-0 left-1/2 -translate-x-1/2 ${s[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot3 1.5s infinite"}}),a.jsx("div",{className:`absolute top-1/2 left-0 -translate-y-1/2 ${s[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot4 1.5s infinite"}}),a.jsx("div",{className:"absolute inset-0 rounded-full",style:{border:`2px solid ${t}`,borderTopColor:"transparent",animation:"loaderRotate 1s linear infinite"}})]})})]})}},76066:(e,t,r)=>{"use strict";r.d(t,{aM:()=>l,sw:()=>c,ue:()=>m,yo:()=>n});var a=r(10326);r(17577);var s=r(79641),o=r(94019),i=r(51223);function n({...e}){return a.jsx(s.fC,{"data-slot":"sheet",...e})}function l({...e}){return a.jsx(s.xz,{"data-slot":"sheet-trigger",...e})}function c({...e}){return a.jsx(s.x8,{"data-slot":"sheet-close",...e})}function d({...e}){return a.jsx(s.h_,{"data-slot":"sheet-portal",...e})}function u({className:e,...t}){return a.jsx(s.aV,{"data-slot":"sheet-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function m({className:e,children:t,side:r="right",...n}){return(0,a.jsxs)(d,{children:[a.jsx(u,{}),(0,a.jsxs)(s.VY,{"data-slot":"sheet-content",className:(0,i.cn)("bg-[#f8f8f5] data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...n,children:[t,(0,a.jsxs)(s.x8,{className:"ring-offset-[#f8f8f5] focus:ring-[#8a8778] data-[state=open]:bg-[#e5e2d9] absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[a.jsx(o.Z,{className:"size-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>m,p:()=>h});var a=r(10326),s=r(17577),o=r(92148),i=r(86462),n=r(54659),l=r(87888),c=r(18019),d=r(94019);let u=(0,s.createContext)(void 0);function m({children:e}){let[t,r]=(0,s.useState)([]);return(0,a.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",a=3e3)=>{let s=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:s,message:e,type:t,duration:a}])},removeToast:e=>{r(t=>t.filter(t=>t.id!==e))}},children:[e,a.jsx(f,{})]})}function h(){let e=(0,s.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}function p({toast:e,onRemove:t}){return(0,a.jsxs)(o.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[a.jsx(()=>{switch(e.type){case"success":return a.jsx(n.Z,{className:"h-5 w-5"});case"error":return a.jsx(l.Z,{className:"h-5 w-5"});default:return a.jsx(c.Z,{className:"h-5 w-5"})}},{}),a.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),a.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:a.jsx(d.Z,{className:"h-4 w-4"})})]})}function f(){let{toasts:e,removeToast:t}=h();return a.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:a.jsx(i.M,{children:e.map(e=>a.jsx(p,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},68471:(e,t,r)=>{"use strict";r.d(t,{EJ:()=>s,J6:()=>a});let a="₹",s="INR"},86806:(e,t,r)=>{"use strict";r.d(t,{rY:()=>n});var a=r(60114),s=r(85251);let o=()=>Math.random().toString(36).substring(2,15),i=async(e,t,r)=>{try{let a=await fetch(`/api/products/${e}/stock${r?`?variation_id=${r}`:""}`);if(!a.ok)return console.warn("Stock validation API failed, allowing add to cart"),{available:!0,message:"Stock validation temporarily unavailable"};let s=await a.json();if("IN_STOCK"!==s.stockStatus&&"instock"!==s.stockStatus)return{available:!1,message:"This product is currently out of stock",stockStatus:s.stockStatus};if(null!==s.stockQuantity&&s.stockQuantity<t)return{available:!1,message:`Only ${s.stockQuantity} items available in stock`,stockQuantity:s.stockQuantity,stockStatus:s.stockStatus};return{available:!0,stockQuantity:s.stockQuantity,stockStatus:s.stockStatus}}catch(e){return console.error("Stock validation error:",e),console.warn("Stock validation failed, allowing add to cart for better UX"),{available:!0,message:"Stock validation temporarily unavailable"}}},n=(0,a.Ue)()((0,s.tJ)((e,t)=>({items:[],itemCount:0,isLoading:!1,error:null,addToCart:async r=>{e({isLoading:!0,error:null});try{let a=await i(r.productId,r.quantity,r.variationId);if(!a.available)throw Error(a.message||"Product is out of stock");let s=t().items,n=r.price;"string"==typeof n&&(n=n.replace(/[₹$€£]/g,"").trim().replace(/,/g,""));let l={...r,price:n},c=s.findIndex(e=>e.productId===l.productId&&e.variationId===l.variationId);if(-1!==c){let t=[...s];t[c].quantity+=l.quantity,e({items:t,itemCount:t.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}else{let t={...l,id:o()};e({items:[...s,t],itemCount:s.reduce((e,t)=>e+t.quantity,0)+t.quantity,isLoading:!1})}console.log("Item added to cart successfully")}catch(t){console.error("Error adding item to cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:(r,a)=>{e({isLoading:!0,error:null});try{let s=t().items;if(a<=0)return t().removeCartItem(r);let o=s.map(e=>e.id===r?{...e,quantity:a}:e);e({items:o,itemCount:o.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}catch(t){console.error("Error updating cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:r=>{e({isLoading:!0,error:null});try{let a=t().items.filter(e=>e.id!==r);e({items:a,itemCount:a.reduce((e,t)=>e+t.quantity,0),isLoading:!1})}catch(t){console.error("Error removing cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},clearCart:()=>{e({items:[],itemCount:0,isLoading:!1,error:null})},setError:t=>{e({error:t})},setIsLoading:t=>{e({isLoading:t})},subtotal:()=>{let e=t().items;try{let t=e.reduce((e,t)=>{let r=0;if("string"==typeof t.price){let e=t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"");r=parseFloat(e)}else r=t.price;return isNaN(r)?(console.warn(`Invalid price for item ${t.id}: ${t.price}`),e):e+r*t.quantity},0);return isNaN(t)?0:t}catch(e){return console.error("Error calculating subtotal:",e),0}},total:()=>{let e=t().subtotal();return isNaN(e)?0:e},syncWithWooCommerce:async r=>{let{items:a}=t();if(0===a.length)throw Error("Cart is empty");try{if(console.log("Syncing cart with WooCommerce..."),console.log("Auth token provided:",!!r),e({isLoading:!0}),r){console.log("User is authenticated, using JWT-to-Cookie bridge");try{let t=await l(r,a);return e({isLoading:!1}),t}catch(e){console.error("JWT-to-Cookie bridge failed:",e),console.log("Falling back to guest checkout...")}}console.log("User is not authenticated, redirecting to WooCommerce checkout");let t="https://maroon-lapwing-781450.hostingersite.com/checkout/";return console.log("Guest checkout URL:",t),e({isLoading:!1}),t}catch(t){console.error("Error syncing cart with WooCommerce:",t),e({isLoading:!1});try{console.log("Attempting fallback method for cart sync...");let e="https://maroon-lapwing-781450.hostingersite.com/checkout/?guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1";return a.forEach((t,r)=>{0===r?e+=`&add-to-cart=${t.productId}&quantity=${t.quantity}`:e+=`&add-to-cart[${r}]=${t.productId}&quantity[${r}]=${t.quantity}`,t.variationId&&(e+=`&variation_id=${t.variationId}`)}),console.log("Fallback checkout URL:",e),e}catch(e){throw console.error("Fallback method failed:",e),Error("Failed to sync cart with WooCommerce. Please try again or contact support.")}}}}),{name:"ankkor-local-cart",version:1}));async function l(e,t){if(!e)throw Error("Authentication token is required");let r="https://maroon-lapwing-781450.hostingersite.com",a="https://maroon-lapwing-781450.hostingersite.com/checkout/";if(!r||!a)throw Error("WordPress or checkout URL not configured. Check your environment variables.");try{console.log("Creating WordPress session from JWT token..."),console.log("Using endpoint:",`${r}/wp-json/headless/v1/create-wp-session`),console.log("Token length:",e.length),console.log("Token preview:",e.substring(0,20)+"...");let t=await fetch(`${r}/wp-json/headless/v1/create-wp-session`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({token:e}),credentials:"include"});if(console.log("Response status:",t.status),console.log("Response headers:",Object.fromEntries(t.headers.entries())),!t.ok){let e=`HTTP ${t.status}: ${t.statusText}`;try{let r=await t.json();e=r.message||r.code||e,console.error("Error response data:",r)}catch(e){console.error("Could not parse error response:",e)}throw Error(`Failed to create WordPress session: ${e}`)}let s=await t.json();if(console.log("Response data:",s),!s.success)throw Error(s.message||"Failed to create WordPress session");return console.log("WordPress session created successfully"),console.log("Redirecting to checkout URL:",a),a}catch(e){if(console.error("Error creating WordPress session:",e),e instanceof TypeError&&e.message.includes("fetch"))throw Error("Network error: Could not connect to WordPress. Please check your internet connection.");throw Error(e instanceof Error?e.message:"Failed to prepare checkout")}}},96040:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Y:()=>u,x:()=>d});var s=r(60114),o=r(85251),i=r(15725),n=e([i]);i=(n.then?(await n)():n)[0];let l={getItem:e=>null,setItem:(e,t)=>{},removeItem:e=>{}},c=(e,t)=>{try{if(!t||!t.lines){console.error("Invalid normalized cart data",t);return}let r=t.lines.reduce((e,t)=>e+(t.quantity||0),0),a=t.lines.map(e=>({id:e.id,variantId:e.merchandise.id,productId:e.merchandise.product.id,title:e.merchandise.product.title,handle:e.merchandise.product.handle,image:e.merchandise.product.image?.url||"",price:e.merchandise.price,quantity:e.quantity,currencyCode:e.merchandise.currencyCode}));e({items:a,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,currencyCode:t.cost.totalAmount.currencyCode,itemCount:r,checkoutUrl:t.checkoutUrl,isLoading:!1})}catch(t){console.error("Error updating cart state:",t),e({items:[],subtotal:"0.00",total:"0.00",itemCount:0,isLoading:!1})}},d=(0,s.Ue)()((0,o.tJ)((e,t)=>({cartId:null,items:[],isOpen:!1,isLoading:!1,subtotal:"0.00",total:"0.00",currencyCode:"USD",itemCount:0,checkoutUrl:null,initializationInProgress:!1,initializationError:null,openCart:()=>e({isOpen:!0}),closeCart:()=>e({isOpen:!1}),toggleCart:()=>e(e=>({isOpen:!e.isOpen})),initCart:async()=>{let r=t();if(r.initializationInProgress)return console.log("Cart initialization already in progress, skipping"),null;e({isLoading:!0,initializationInProgress:!0,initializationError:null});try{if(r.cartId)try{let t=await (0,i.dv)();if(t)return e({isLoading:!1,initializationInProgress:!1}),t}catch(e){console.log("Existing cart validation failed, creating new cart")}let t=await (0,i.Bk)();if(t&&t.id)return e({cartId:t.id,checkoutUrl:t.checkoutUrl,isLoading:!1,initializationInProgress:!1}),console.log("Cart initialized with ID:",t.id),t;throw Error("Failed to create cart: No cart ID returned")}catch(t){return console.error("Failed to initialize cart:",t),e({isLoading:!1,initializationInProgress:!1,initializationError:t instanceof Error?t.message:"Unknown error initializing cart"}),null}},addItem:async r=>{e({isLoading:!0});try{if(!r.variantId)throw console.error("Cannot add item to cart: Missing variant ID",r),e({isLoading:!1}),Error("Missing variant ID for item");let a=t().cartId;if(!a){console.log("Cart not initialized, creating a new cart...");let e=await (0,i.Bk)();if(e&&e.id)console.log("New cart created:",e.id),a=e.id;else throw Error("Failed to initialize cart")}if(!a)throw Error("Failed to initialize cart: No cart ID available");console.log(`Adding item to cart: ${r.title} (${r.variantId}), quantity: ${r.quantity}`);try{let t=await (0,i.Xq)(a,[{merchandiseId:r.variantId,quantity:r.quantity||1}]);if(!t)throw Error("Failed to add item to cart: No cart returned");let s=(0,i.Id)(t);c(e,s),e({isOpen:!0}),console.log(`Item added to cart successfully. Cart now has ${s.lines.length} items.`)}catch(e){if(console.error("Shopify API error when adding to cart:",e),e instanceof Error)throw Error(`Failed to add item to cart: ${e.message}`);throw Error("Failed to add item to cart: Unknown API error")}}catch(t){throw console.error("Failed to add item to cart:",t),e({isLoading:!1}),t}},updateItem:async(r,a)=>{let s=t();e({isLoading:!0});try{if(!s.cartId)throw Error("Cart not initialized");if(console.log(`Updating item in cart: ${r}, new quantity: ${a}`),a<=0)return console.log(`Quantity is ${a}, removing item from cart`),t().removeItem(r);let o=await (0,i.xu)(s.cartId,[{id:r,quantity:a}]);if(!o)throw Error("Failed to update item: No cart returned");let n=(0,i.Id)(o);c(e,n),console.log(`Item updated successfully. Cart now has ${n.lines.length} items.`)}catch(t){throw console.error("Failed to update item in cart:",t),e({isLoading:!1}),t}},removeItem:async r=>{let a=t();e({isLoading:!0});try{if(!a.cartId)throw console.error("Cannot remove item: Cart not initialized"),Error("Cart not initialized");console.log(`Removing item from cart: ${r}`);let t=[...a.items],s=t.find(e=>e.id===r);s?console.log(`Removing "${s.title}" (${s.variantId}) from cart`):console.warn(`Item with ID ${r} not found in cart`);let o=await (0,i.h2)(a.cartId,[r]);if(!o)throw console.error("Failed to remove item: No cart returned from Shopify"),Error("Failed to remove item: No cart returned");let n=(0,i.Id)(o),l=n.lines.map(e=>({id:e.id,title:e.merchandise.product.title}));console.log("Cart before removal:",t.length,"items"),console.log("Cart after removal:",l.length,"items"),t.length===l.length&&console.warn("Item count did not change after removal operation"),c(e,n),console.log(`Item removed successfully. Cart now has ${n.lines.length} items.`)}catch(t){throw console.error("Failed to remove item from cart:",t),e({isLoading:!1}),t}},clearCart:async()=>{t(),e({isLoading:!0});try{console.log("Clearing cart and creating a new one");let t=await (0,i.Bk)();if(!t)throw Error("Failed to create new cart");e({cartId:t.id,items:[],subtotal:"0.00",total:"0.00",itemCount:0,checkoutUrl:t.checkoutUrl,isLoading:!1}),console.log("Cart cleared successfully. New cart ID:",t.id)}catch(t){throw console.error("Failed to clear cart:",t),e({isLoading:!1}),t}}}),{name:"ankkor-cart",storage:(0,o.FL)(()=>l),version:1,partialize:e=>({cartId:e.cartId,items:e.items,subtotal:e.subtotal,total:e.total,currencyCode:e.currencyCode,itemCount:e.itemCount,checkoutUrl:e.checkoutUrl})})),u=(0,s.Ue)()((0,o.tJ)((e,t)=>({items:[],isLoading:!1,addToWishlist:t=>{e(e=>e.items.some(e=>e.id===t.id)?e:{items:[...e.items,t]})},removeFromWishlist:t=>{e(e=>({items:e.items.filter(e=>e.id!==t)}))},clearWishlist:()=>{e({items:[]})},isInWishlist:e=>t().items.some(t=>t.id===e)}),{name:"ankkor-wishlist",storage:(0,o.FL)(()=>l),partialize:e=>({items:e.items})}));a()}catch(e){a(e)}})},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var a=r(41135),s=r(31009);function o(...e){return(0,s.m6)((0,a.W)(e))}},53248:(e,t,r)=>{"use strict";r.d(t,{xh:()=>c});let a=new(r(78578)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),s={},o={};function i(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function n(e){try{if(i())return await a.get(`woo:inventory:mapping:${e}`)||null;return s[e]||null}catch(t){console.error("Error getting product slug from Redis:",t);try{return s[e]||null}catch(e){return console.error("Error getting from memory fallback:",e),null}}}async function l(e){try{if(i())return await a.hget("shopify:to:woo:mapping",e)||null;return o[e]||null}catch(t){console.error(`Error getting WooCommerce ID for Shopify ID ${e}:`,t);try{return o[e]||null}catch(e){return console.error("Error getting from memory fallback:",e),null}}}async function c(e){if(!e||"undefined"===e||"null"===e)return console.warn("Invalid product ID received:",e),e;if(e.includes("gid://shopify/Product/")){console.log(`Detected Shopify ID: ${e}, attempting to map to WooCommerce ID`);let t=await l(e);return t?(console.log(`Mapped Shopify ID ${e} to WooCommerce ID ${t}`),t):(console.warn(`No mapping found for Shopify ID: ${e}, using original ID`),e)}return e.includes("=")?await n(e)||console.warn(`Product ID ${e} not found in inventory mapping, using as is`):/^\d+$/.test(e),e}},15725:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Bk:()=>m,CP:()=>u,Dg:()=>d,Id:()=>g,ML:()=>y,Op:()=>x,Xp:()=>n,Xq:()=>p,dv:()=>h,gk:()=>v,h2:()=>f,mJ:()=>U,wv:()=>w,xu:()=>j});var s=r(93690),o=r(53248),i=e([s]);s=(i.then?(await i)():i)[0];let N={storeUrl:"https://maroon-lapwing-781450.hostingersite.com",graphqlUrl:process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",apiVersion:"v1"},k=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",C=new s.GraphQLClient(k,{headers:{"Content-Type":"application/json",Accept:"application/json"}}),I=(0,s.gql)`
  fragment ProductFields on Product {
    id
    databaseId
    name
    slug
    description
    shortDescription
    type
    image {
      sourceUrl
      altText
    }
    galleryImages {
      nodes {
        sourceUrl
        altText
      }
    }
    ... on SimpleProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
    }
    ... on VariableProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      attributes {
        nodes {
          name
          options
        }
      }
    }
  }
`,P=(0,s.gql)`
  fragment VariableProductWithVariations on VariableProduct {
    attributes {
      nodes {
        name
        options
      }
    }
    variations {
      nodes {
        id
        databaseId
        name
        price
        regularPrice
        salePrice
        stockStatus
        stockQuantity
        attributes {
          nodes {
            name
            value
          }
        }
      }
    }
  }
`,S=(0,s.gql)`
  query GetProducts(
    $first: Int
    $after: String
    $where: RootQueryToProductConnectionWhereArgs
  ) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...ProductFields
        ... on VariableProduct {
          ...VariableProductWithVariations
        }
      }
    }
  }
  ${I}
  ${P}
`;async function n(e={}){try{return(await l(S,{first:e.first||12,after:e.after||null,where:e.where||{}},["products"],60)).products}catch(e){return console.error("Error fetching products:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function l(e,t={},r=[],a=60){try{{let s={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t}),next:{}};r&&r.length>0&&(s.next.tags=r),void 0!==a&&(s.next.revalidate=a);let o=await fetch(N.graphqlUrl,s);if(!o.ok)throw Error(`WooCommerce GraphQL API responded with status ${o.status}`);let{data:i,errors:n}=await o.json();if(n)throw console.error("GraphQL Errors:",n),Error(n[0].message);return i}}catch(e){throw console.error("Error fetching from WooCommerce:",e),e}}async function c({query:e,variables:t},r=3,a=1e3){let s=0,o=null;for(;s<r;)try{return await l(e,t,[],0)}catch(e){o=e,++s<r&&(console.log(`Retrying request (${s}/${r}) after ${a}ms`),await new Promise(e=>setTimeout(e,a)),a*=2)}throw console.error(`Failed after ${r} attempts:`,o),o}(0,s.gql)`
  query GetProductBySlug($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
    }
  }
  ${I}
  ${P}
`,(0,s.gql)`
  query GetProductBySlugWithTags($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
      productTags {
        nodes {
          id
          name
          slug
        }
      }
      productCategories {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${I}
  ${P}
`,(0,s.gql)`
  query GetCategories(
    $first: Int
    $after: String
    $where: RootQueryToProductCategoryConnectionWhereArgs
  ) {
    productCategories(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
      }
    }
  }
`;let $=(0,s.gql)`
  query GetAllProducts($first: Int = 20) {
    products(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        productCategories {
          nodes {
            id
            name
            slug
          }
        }
        ... on SimpleProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          variations {
            nodes {
              stockStatus
              stockQuantity
            }
          }
        }
        image {
          id
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            id
            sourceUrl
            altText
          }
        }
        ... on VariableProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
      }
    }
  }
`,E=((0,s.gql)`
  query GetProductsByCategory($slug: ID!, $first: Int = 20) {
    productCategory(id: $slug, idType: SLUG) {
      id
      name
      slug
      description
      products(first: $first) {
        nodes {
          id
          databaseId
          name
          slug
          ... on SimpleProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          ... on VariableProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          image {
            id
            sourceUrl
            altText
          }
        }
      }
    }
  }
`,(0,s.gql)`
  query GetAllCategories($first: Int = 20) {
    productCategories(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
        children {
          nodes {
            id
            name
            slug
          }
        }
      }
    }
  }
`);(0,s.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
    }
  }
`,(0,s.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
        nicename
        nickname
        username
      }
    }
  }
`;let T=(0,s.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`,q=(0,s.gql)`
  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {
    addToCart(
      input: {
        clientMutationId: "addToCart"
        productId: $productId
        variationId: $variationId
        quantity: $quantity
        extraData: $extraData
      }
    ) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
        contentsCount
      }
    }
  }
`,L=(0,s.gql)`
  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {
    removeItemsFromCart(input: { keys: $keys, all: $all }) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function d(e=20){try{let t=await c({query:$,variables:{first:e}});return t?.products?.nodes||[]}catch(e){return console.error("Error fetching all products:",e),[]}}async function u(e={}){try{let t=await c({query:E,variables:{first:e.first||20,after:e.after||null,where:e.where||{}}});return{nodes:t.productCategories.nodes,pageInfo:t.productCategories.pageInfo}}catch(e){return console.error("Error fetching categories:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function m(e=[]){try{if(0===e.length)return{contents:{nodes:[]},subtotal:"0",total:"0",totalTax:"0",isEmpty:!0,contentsCount:0};let t=e[0],r=await p("",[t]);if(e.length>1){for(let t=1;t<e.length;t++)await p("",[e[t]]);return await h()}return r}catch(e){throw console.error("Error creating cart:",e),e}}async function h(){try{let e=await c({query:T,variables:{}});return e?.cart||null}catch(e){return console.error("Error fetching cart:",e),null}}async function p(e,t){try{if(0===t.length)throw Error("No items provided to add to cart");let e=t[0],r={productId:parseInt(e.productId),quantity:e.quantity||1,variationId:e.variationId?parseInt(e.variationId):null,extraData:null};console.log("Adding to cart with variables:",r);let a=await c({query:q,variables:r});return console.log("Add to cart response:",a),a.addToCart.cart}catch(e){throw console.error("Error adding items to cart:",e),e}}async function f(e,t){try{let e=await c({query:L,variables:{keys:t,all:!1}});return e?.removeItemsFromCart?.cart||null}catch(e){throw console.error("Error removing items from cart:",e),e}}function x(e){if(!e)return null;let t=!!e.variations?.nodes?.length,r={minVariantPrice:{amount:e.price||"0",currencyCode:"INR"},maxVariantPrice:{amount:e.price||"0",currencyCode:"INR"}};if(t&&e.variations?.nodes?.length>0){let t=e.variations.nodes.map(e=>parseFloat(e.price||"0")).filter(e=>!isNaN(e));t.length>0&&(r={minVariantPrice:{amount:String(Math.min(...t)),currencyCode:"INR"},maxVariantPrice:{amount:String(Math.max(...t)),currencyCode:"INR"}})}let a=function(e){let t=[];return e.image&&t.push({url:e.image.sourceUrl,altText:e.image.altText||e.name||""}),e.galleryImages?.nodes?.length&&e.galleryImages.nodes.forEach(r=>{e.image&&r.sourceUrl===e.image.sourceUrl||t.push({url:r.sourceUrl,altText:r.altText||e.name||""})}),t}(e),s=e.variations?.nodes?.map(e=>({id:e.id,title:e.name,price:{amount:e.price||"0",currencyCode:"USD"},availableForSale:"IN_STOCK"===e.stockStatus,selectedOptions:e.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[],sku:e.sku||"",image:e.image?{url:e.image.sourceUrl,altText:e.image.altText||""}:null}))||[],o=e.attributes?.nodes?.map(e=>({name:e.name,values:e.options||[]}))||[],i=e.productCategories?.nodes?.map(e=>({handle:e.slug,title:e.name}))||[],n={};return e.metafields&&e.metafields.forEach(e=>{n[e.key]=e.value}),{id:e.id,handle:e.slug,title:e.name,description:e.description||"",descriptionHtml:e.description||"",priceRange:r,options:o,variants:s,images:a,collections:i,availableForSale:"OUT_OF_STOCK"!==e.stockStatus,metafields:n,_originalWooProduct:e}}(0,s.gql)`
  query GetShippingMethods {
    shippingMethods {
      nodes {
        id
        title
        description
        cost
      }
    }
  }
`,(0,s.gql)`
  query GetPaymentGateways {
    paymentGateways {
      nodes {
        id
        title
        description
        enabled
      }
    }
  }
`;let U=(e,t,r,a="")=>{if(!e||!e.metafields)return a;if(r){let s=`${r}:${t}`;return e.metafields[s]||a}return e.metafields[t]||a};function g(e){if(!e)return null;let t=e.contents?.nodes?.map(e=>{let t=e.product?.node,r=e.variation?.node;return{id:e.key,quantity:e.quantity,merchandise:{id:r?.id||t?.id,title:r?.name||t?.name,product:{id:t?.id,handle:t?.slug,title:t?.name,image:t?.image?{url:t?.image.sourceUrl,altText:t?.image.altText||""}:null},selectedOptions:r?.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[]},cost:{totalAmount:{amount:e.total||"0",currencyCode:"USD"}}}})||[],r=e.appliedCoupons?.nodes?.map(e=>({code:e.code,amount:e.discountAmount||"0"}))||[],a=t.reduce((e,t)=>e+t.quantity,0);return{id:e.id,checkoutUrl:"",totalQuantity:a,cost:{subtotalAmount:{amount:e.subtotal||"0",currencyCode:"USD"},totalAmount:{amount:e.total||"0",currencyCode:"USD"}},lines:t,discountCodes:r}}function y(e,t=!1){let r=`${N.storeUrl}/checkout`,a=e?`?cart=${e}`:"",s="";return t||(s=`${a?"&":"?"}guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`),`${r}${a}${s}`}async function v(e,t=60){try{if(!e||"undefined"===e||"null"===e)return console.warn(`Invalid product ID format: ${e}, returning fallback product`),b(e);let r=await (0,o.xh)(e),a=[`product-${r}`,"products","inventory"],i=(0,s.gql)`
      query GetProductById($id: ID!) {
        product(id: $id, idType: DATABASE_ID) {
          id
          databaseId
          name
          slug
          description
          shortDescription
          productCategories {
            nodes {
              id
              name
              slug
            }
          }
          ... on SimpleProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
            stockQuantity
          }
          ... on VariableProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
            variations {
              nodes {
                stockStatus
                stockQuantity
              }
            }
          }
          image {
            id
            sourceUrl
            altText
          }
        }
      }
    `;try{let s=await l(i,{id:r},a,t);if(!s?.product)return console.warn(`No product found with ID: ${e}, returning fallback product`),b(e);return s.product}catch(t){return console.error(`Error fetching product with ID ${e}:`,t),b(e)}}catch(t){return console.error(`Error in getProductById for ID ${e}:`,t),b(e)}}function b(e){return{id:e,databaseId:0,name:"Product Not Found",slug:"product-not-found",description:"This product is no longer available.",shortDescription:"Product not found",price:"0.00",regularPrice:"0.00",salePrice:null,onSale:!1,stockStatus:"OUT_OF_STOCK",stockQuantity:0,image:{id:null,sourceUrl:"/placeholder-product.jpg",altText:"Product not found"},productCategories:{nodes:[]}}}async function w(e){let t=(0,s.gql)`
    query GetProduct($id: ID!) {
      product(id: $id, idType: DATABASE_ID) {
        id
        databaseId
        name
        slug
        description
        shortDescription
        price
        regularPrice
        salePrice
        onSale
        stockStatus
        stockQuantity
        image {
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            sourceUrl
            altText
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
          price
          regularPrice
          salePrice
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          attributes {
            nodes {
              name
              options
            }
          }
          variations {
            nodes {
              id
              databaseId
              name
              price
              regularPrice
              salePrice
              stockStatus
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
        }
      }
    }
  `;try{return(await C.request(t,{id:e})).product}catch(e){throw console.error("Error fetching product:",e),Error("Failed to fetch product")}}(0,s.gql)`
  mutation CreateCustomer($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
      }
      authToken
      refreshToken
    }
  }
`,(0,s.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
      customerUserErrors {
        field
        message
      }
    }
  }
`,(0,s.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      displayName
      username
      role
      date
      modified
      isPayingCustomer
      orderCount
      totalSpent
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders(first: 50) {
        nodes {
          id
          databaseId
          date
          status
          total
          subtotal
          totalTax
          shippingTotal
          discountTotal
          paymentMethodTitle
          customerNote
          billing {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
            email
            phone
          }
          shipping {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
          }
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                  slug
                  image {
                    sourceUrl
                    altText
                  }
                }
              }
              variation {
                node {
                  id
                  name
                  attributes {
                    nodes {
                      name
                      value
                    }
                  }
                }
              }
              quantity
              total
              subtotal
              totalTax
            }
          }
          shippingLines {
            nodes {
              methodTitle
              total
            }
          }
          feeLines {
            nodes {
              name
              total
            }
          }
          couponLines {
            nodes {
              code
              discount
            }
          }
        }
      }
      downloadableItems {
        nodes {
          name
          downloadId
          downloadsRemaining
          accessExpires
          product {
            node {
              id
              name
            }
          }
        }
      }
      metaData {
        key
        value
      }
    }
  }
`,(0,s.gql)`
  mutation CreateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,s.gql)`
  mutation UpdateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,s.gql)`
  mutation DeleteAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,s.gql)`
  mutation SetDefaultAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let D=(0,s.gql)`
  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                name
                price
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function j(e){try{return(await c({query:D,variables:{input:{items:e}}})).updateItemQuantities.cart}catch(e){throw console.error("Error updating cart:",e),e}}a()}catch(e){a(e)}})},52617:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g,metadata:()=>x});var a=r(19510),s=r(10527),o=r.n(s),i=r(36822),n=r.n(i);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#useCart`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#CartProvider`);let c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\cart\CartProvider.tsx#default`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#useLoading`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#LoadingProvider`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LoadingProvider.tsx#default`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let u=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),m=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#useLaunchingSoonStore`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#useLaunchingSoon`),(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#LaunchingSoonProvider`);let h=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\LaunchingSoonProvider.tsx#default`),p=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\layout\NavbarWrapper.tsx#default`),f=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\layout\FooterWrapper.tsx#default`),x={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function g({children:e}){return a.jsx("html",{lang:"en",children:a.jsx("body",{className:`${o().variable} ${n().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:a.jsx(m,{children:a.jsx(u,{children:a.jsx(c,{children:a.jsx(d,{children:(0,a.jsxs)(h,{children:[a.jsx(p,{}),a.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e}),a.jsx(f,{})]})})})})})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};