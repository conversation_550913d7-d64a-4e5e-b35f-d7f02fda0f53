(()=>{var e={};e.id=1660,e.ids=[1660],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21273:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>m,originalPathname:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>l}),r(42399),r(31710),r(12523);var o=r(23191),n=r(88716),a=r(37922),s=r.n(a),i=r(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l=["",{children:["woocommerce-cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,42399)),"E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,31710)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],u=["E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-cart-test\\page.tsx"],d="/woocommerce-cart-test/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/woocommerce-cart-test/page",pathname:"/woocommerce-cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},87024:(e,t,r)=>{Promise.resolve().then(r.bind(r,32903))},32903:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E});var o=r(10326),n=r(17577),a=r(86806);class s{getCartToken(){return`cart_${Math.random().toString(36).substring(2)}_${Date.now()}`}headers(e){let t={"Content-Type":"application/json","Cart-Token":this.getCartToken()};return e&&(t["X-WC-Store-API-Nonce"]=e),t}fetchOptions(e="GET",t,r){let o={method:e,headers:this.headers(t),credentials:"include"};return r&&(o.body=JSON.stringify(r)),o}getWooCommerceSessionCookie(){if("undefined"==typeof document)return null;let e=document.cookie.split(";").find(e=>e.trim().startsWith("wp_woocommerce_session_"));return e?e.trim():null}debugToken(){console.log("CartSession: Running on server, no token available")}constructor(){this.CART_TOKEN_KEY="woo_cart_token",this.TOKEN_EXPIRY_KEY="woo_cart_token_expiry",this.TOKEN_EXPIRY_DAYS=30}}let i=new s,c={retries:3,initialDelay:500,maxDelay:1e4,jitter:.1,retryableError:()=>!0,onRetry:(e,t)=>console.warn(`Retry attempt ${t} after error:`,e)};function l(e,t={}){let r={...c,...t};return async function(...t){let o;for(let n=0;n<=r.retries;n++)try{return await e(...t)}catch(t){if(o=t,n>=r.retries||!r.retryableError(t))break;r.onRetry(t,n+1);let e=Math.min(r.initialDelay*Math.pow(2,n),r.maxDelay)*(1+r.jitter*(2*Math.random()-1));await new Promise(t=>setTimeout(t,e))}throw o}}function u(e){var t;return e instanceof TypeError||e.message?.includes("network")||e.message?.includes("Network")||e.message?.includes("fetch")||e.message?.includes("connection")||e.message?.includes("timeout")||e.message?.includes("abort")||(t=e).status>=500||t.response&&t.response.status>=500}let d="https://maroon-lapwing-781450.hostingersite.com",m={CART:"/wp-json/wc/store/v1/cart",CART_ITEMS:"/wp-json/wc/store/v1/cart/items",ADD_ITEM:"/wp-json/wc/store/v1/cart/add-item",CHECKOUT:"/wp-json/wc/store/v1/checkout"};function h(e){if("number"==typeof e)return e;if(/^[0-9]+$/.test(e))return Number(e);try{if(e.includes("=")){let t=Buffer.from(e,"base64").toString().match(/(\d+)$/);if(t)return Number(t[1])}}catch(e){console.warn("Error parsing product ID:",e)}return e}async function p(){try{let e=await f();if(e)return e;let t=await g();if(t)return t;let r=await w();if(r)return r;throw Error("Could not obtain a valid nonce from any source")}catch(e){throw console.error("Error fetching nonce:",e),e}}async function f(){try{if(!d)throw Error("WooCommerce URL not configured");let e=await fetch(`${d}${m.CART}`,i.fetchOptions("GET")),t=e.headers.get("x-wc-store-api-nonce")||e.headers.get("X-WC-Store-API-Nonce");if(t)return t;try{let t=await e.json();if(t.extensions&&t.extensions.store_api_nonce)return t.extensions.store_api_nonce}catch(e){console.warn("Error parsing cart response:",e)}return null}catch(e){return console.error("Error fetching nonce from cart:",e),null}}async function g(){try{let e=await fetch("/api/ankkor/v1/nonce",i.fetchOptions("GET"));if(!e.ok)return null;let t=await e.json();if(t&&t.nonce)return t.nonce;return null}catch(e){return console.error("Error fetching nonce from custom endpoint:",e),null}}async function w(){try{if(!d)throw Error("WooCommerce URL not configured");let e=await fetch(`${d}/wp-json/wc/store/v1/products?per_page=1`,i.fetchOptions("GET")),t=e.headers.get("x-wc-store-api-nonce")||e.headers.get("X-WC-Store-API-Nonce");if(t)return t;return null}catch(e){return console.error("Error fetching nonce from products:",e),null}}l(async e=>{if(!d)throw Error("WooCommerce URL not configured");let t=await fetch(`${d}${m.CART}`,i.fetchOptions("GET",e));if(!t.ok)throw Error(`Failed to get cart: ${t.status}`);return await t.json()},{retryableError:u});let x=l(async e=>{if(!d)throw Error("WooCommerce URL not configured");let t=await fetch(`${d}${m.CART_ITEMS}`,i.fetchOptions("DELETE",e));if(!t.ok)throw Error(`Failed to clear cart: ${t.status}`);return await t.json()},{retryableError:u}),y=l(async(e,t,r,o,n)=>{if(!d)throw Error("WooCommerce URL not configured");let a={id:h(t),quantity:r};o&&(a.variation_id=h(o)),n&&(a.variation=n);let s=await fetch(`${d}${m.ADD_ITEM}`,i.fetchOptions("POST",e,a));if(!s.ok)throw Error(`Failed to add item to cart: ${s.status}`);return await s.json()},{retryableError:u}),b=l(async(e,t)=>{if(!d)throw Error("WooCommerce URL not configured");if(0===t.length)throw Error("Cart is empty");await x(e);let r=null;for(let o of t){let t={};o.attributes&&o.attributes.length>0&&o.attributes.forEach(e=>{t[`attribute_${e.name.toLowerCase().replace(/\s+/g,"-")}`]=e.value}),r=await y(e,o.productId,o.quantity,o.variationId,Object.keys(t).length>0?t:void 0)}if(!r)throw Error("Failed to sync cart with WooCommerce");return r},{retryableError:u});function E(){let[e,t]=(0,n.useState)(!1),[r,s]=(0,n.useState)(null),[c,l]=(0,n.useState)(null),[u,d]=(0,n.useState)(null),m=(0,a.rY)(),h=async()=>{t(!0),s(null),l(null);try{i.debugToken();let e=await m.syncWithWooCommerce();if(e)l(`Cart synced successfully. Redirecting to: ${e}`),setTimeout(()=>{window.location.href=e},2e3);else throw Error("Failed to get checkout URL")}catch(e){console.error("Checkout error:",e),s(e instanceof Error?e.message:"An error occurred during checkout")}finally{t(!1)}},f=async()=>{t(!0),s(null),l(null);try{let e=await p();console.log("Fetched nonce:",e);let t=m.items;if(0===t.length)throw Error("Cart is empty");let r=await b(e,t);console.log("Cart sync response:",r);let o=i.getWooCommerceSessionCookie();d({nonce:e,cartItems:t,cartResponse:r,wooSessionCookie:o}),l("Direct API call successful")}catch(e){console.error("API error:",e),s(e instanceof Error?e.message:"An error occurred during API call")}finally{t(!1)}};return(0,o.jsxs)("div",{className:"container mx-auto p-4",children:[o.jsx("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Cart Test"}),(0,o.jsxs)("div",{className:"mb-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Cart Contents"}),0===m.items.length?o.jsx("p",{children:"Cart is empty"}):(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{children:["Items: ",m.items.length]}),o.jsx("ul",{className:"list-disc pl-5",children:m.items.map(e=>(0,o.jsxs)("li",{children:[e.name," - Quantity: ",e.quantity," - Price: $",e.price]},e.id))}),(0,o.jsxs)("p",{className:"mt-2",children:["Subtotal: $",m.subtotal().toFixed(2)]})]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[o.jsx("button",{onClick:()=>{m.addToCart({productId:"123",name:"Test Product",price:"99.99",quantity:1,image:{url:"/shirt.png",altText:"Test Product"}}),l("Test product added to cart")},className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Add Test Product"}),o.jsx("button",{onClick:h,disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-green-600 hover:bg-green-700"} text-white rounded`,children:e?"Processing...":"Proceed to Checkout"}),o.jsx("button",{onClick:f,disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-purple-600 hover:bg-purple-700"} text-white rounded`,children:"Test Direct API Call"}),o.jsx("button",{onClick:()=>{m.clearCart(),l("Cart cleared")},disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-red-600 hover:bg-red-700"} text-white rounded`,children:"Clear Cart"})]}),r&&(0,o.jsxs)("div",{className:"p-4 mb-4 bg-red-50 border border-red-200 text-red-700 rounded",children:[o.jsx("strong",{children:"Error:"})," ",r]}),c&&(0,o.jsxs)("div",{className:"p-4 mb-4 bg-green-50 border border-green-200 text-green-700 rounded",children:[o.jsx("strong",{children:"Success:"})," ",c]}),u&&(0,o.jsxs)("div",{className:"mt-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Debug Information"}),o.jsx("pre",{className:"p-4 bg-gray-100 rounded overflow-auto max-h-96",children:JSON.stringify(u,null,2)})]})]})}l(async(e,t)=>{if(!d)throw Error("WooCommerce URL not configured");let r=await fetch(`${d}${m.CHECKOUT}`,i.fetchOptions("POST",e,t));if(!r.ok){let e=await r.text();throw Error(`Checkout failed: ${r.status} - ${e}`)}return await r.json()},{retryableError:u})},42399:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\woocommerce-cart-test\page.tsx#default`)}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8948,8216,8888],()=>r(21273));module.exports=o})();