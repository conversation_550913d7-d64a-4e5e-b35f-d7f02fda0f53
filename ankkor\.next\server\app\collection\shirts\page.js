(()=>{var e={};e.id=9153,e.ids=[9153],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},34139:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>c.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>d,routeModule:()=>p,tree:()=>o}),s(5818),s(51806),s(12523);var i=s(23191),r=s(88716),a=s(37922),c=s.n(a),l=s(95231),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let o=["",{children:["collection",{children:["shirts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5818)),"E:\\ankkorwoo\\ankkor\\src\\app\\collection\\shirts\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\collection\\shirts\\page.tsx"],x="/collection/shirts/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/collection/shirts/page",pathname:"/collection/shirts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},37973:(e,t,s)=>{Promise.resolve().then(s.bind(s,41383))},41137:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});let i=(0,s(76557).Z)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},41383:(e,t,s)=>{"use strict";s.a(e,async(e,i)=>{try{s.r(t),s.d(t,{default:()=>h});var r=s(10326),a=s(17577),c=s(46226),l=s(92148),n=s(41137),o=s(94019),d=s(53471),x=s(99063),m=s(15725),p=s(45107),u=e([d,m]);function h(){let[e,t]=(0,a.useState)([]),[s,i]=(0,a.useState)(!1),[u,h]=(0,a.useState)([0,25e3]),[f,g]=(0,a.useState)("featured"),[j,b]=(0,a.useState)(!0),[v,N]=(0,a.useState)(null);(0,x.Z)(j,"fabric");let y=()=>{i(!s)},w=[...e.filter(e=>{let t=parseFloat(e.price)||0;return t>=u[0]&&t<=u[1]})].sort((e,t)=>{switch(f){case"price-asc":return parseFloat(e.price)-parseFloat(t.price);case"price-desc":return parseFloat(t.price)-parseFloat(e.price);case"rating":return e.title.localeCompare(t.title);case"newest":return t.id.localeCompare(e.id);default:return 0}}),k={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.5}},exit:{opacity:0,y:20,transition:{duration:.3}}};return(0,r.jsxs)("div",{className:"min-h-screen bg-[#f8f8f5] pt-8 pb-24",children:[r.jsx("div",{className:"container mx-auto px-4 mb-12",children:(0,r.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[r.jsx("h1",{className:"text-4xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Shirts Collection"}),r.jsx("p",{className:"text-[#5c5c52] mb-8",children:"Discover our meticulously crafted shirts, designed with premium fabrics and impeccable attention to detail."})]})}),(0,r.jsxs)("div",{className:"relative h-[300px] mb-16 overflow-hidden",children:[r.jsx(c.default,{src:"https://images.unsplash.com/photo-1552374196-1ab2a1c593e8?q=80",alt:"Ankkor Shirts Collection",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"}),r.jsx("div",{className:"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[r.jsx("h2",{className:"text-3xl font-serif font-bold mb-4",children:"Signature Shirts"}),r.jsx("p",{className:"text-lg max-w-xl mx-auto",children:"Impeccably tailored for the perfect fit"})]})})]}),(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[v&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 mb-8 rounded",children:[r.jsx("p",{children:v}),r.jsx("p",{className:"text-sm mt-2",children:"Please check your WooCommerce configuration in the .env.local file."})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8 md:hidden",children:[(0,r.jsxs)("button",{onClick:y,className:"flex items-center gap-2 text-[#2c2c27] border border-[#e5e2d9] px-4 py-2",children:[r.jsx(n.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Filter & Sort"})]}),(0,r.jsxs)("div",{className:"text-[#5c5c52] text-sm",children:[w.length," products"]})]}),s&&(0,r.jsxs)("div",{className:"fixed inset-0 z-50 md:hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:y}),(0,r.jsxs)("div",{className:"absolute right-0 top-0 bottom-0 w-80 bg-[#f8f8f5] p-6 overflow-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[r.jsx("h3",{className:"font-serif text-lg text-[#2c2c27]",children:"Filter & Sort"}),r.jsx("button",{onClick:y,children:r.jsx(o.Z,{className:"h-5 w-5 text-[#2c2c27]"})})]}),(0,r.jsxs)("div",{className:"mb-8",children:[r.jsx("h4",{className:"text-[#8a8778] text-xs uppercase tracking-wider mb-4",children:"Price Range"}),(0,r.jsxs)("div",{className:"px-2",children:[(0,r.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,r.jsxs)("span",{className:"text-[#5c5c52] text-sm",children:[(0,p.jK)("INR"),u[0]]}),(0,r.jsxs)("span",{className:"text-[#5c5c52] text-sm",children:[(0,p.jK)("INR"),u[1]]})]}),r.jsx("input",{type:"range",min:"0",max:"25000",value:u[1],onChange:e=>h([u[0],parseInt(e.target.value)]),className:"w-full h-2 bg-[#e5e2d9] rounded-lg appearance-none cursor-pointer"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"text-[#8a8778] text-xs uppercase tracking-wider mb-4",children:"Sort By"}),r.jsx("div",{className:"space-y-3",children:[{id:"featured",name:"Featured"},{id:"price-asc",name:"Price: Low to High"},{id:"price-desc",name:"Price: High to Low"},{id:"rating",name:"Alphabetical"},{id:"newest",name:"Newest"}].map(e=>r.jsx("button",{onClick:()=>g(e.id),className:`block w-full text-left py-1 ${f===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52]"}`,children:e.name},e.id))})]}),r.jsx("button",{onClick:y,className:"w-full bg-[#2c2c27] text-[#f4f3f0] py-3 mt-8 text-sm uppercase tracking-wider",children:"Apply Filters"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-10",children:[r.jsx("div",{className:"hidden md:block w-64 shrink-0",children:(0,r.jsxs)("div",{className:"sticky top-24",children:[(0,r.jsxs)("div",{className:"mb-10",children:[r.jsx("h3",{className:"text-[#2c2c27] font-serif text-lg mb-6",children:"Price Range"}),(0,r.jsxs)("div",{className:"px-2",children:[(0,r.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,r.jsxs)("span",{className:"text-[#5c5c52]",children:[(0,p.jK)("INR"),u[0]]}),(0,r.jsxs)("span",{className:"text-[#5c5c52]",children:[(0,p.jK)("INR"),u[1]]})]}),r.jsx("input",{type:"range",min:"0",max:"25000",value:u[1],onChange:e=>h([u[0],parseInt(e.target.value)]),className:"w-full h-2 bg-[#e5e2d9] rounded-lg appearance-none cursor-pointer"})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-[#2c2c27] font-serif text-lg mb-6",children:"Sort By"}),r.jsx("div",{className:"space-y-3",children:[{id:"featured",name:"Featured"},{id:"price-asc",name:"Price: Low to High"},{id:"price-desc",name:"Price: High to Low"},{id:"rating",name:"Alphabetical"},{id:"newest",name:"Newest"}].map(e=>r.jsx("button",{onClick:()=>g(e.id),className:`block w-full text-left py-1 ${f===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52] hover:text-[#2c2c27] transition-colors"}`,children:e.name},e.id))})]})]})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"hidden md:flex justify-between items-center mb-8",children:[r.jsx("h2",{className:"text-[#2c2c27] font-serif text-xl",children:"Shirts Collection"}),(0,r.jsxs)("div",{className:"text-[#5c5c52]",children:[w.length," products"]})]}),r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:w.map(e=>{let t="",s=!1;try{if(e.variants&&e.variants.length>0){let i=e.variants[0];if(i&&i.id){if(t=i.id,s=!0,!t.startsWith("gid://shopify/ProductVariant/")){let i=t.replace(/\D/g,"");i?t=`gid://shopify/ProductVariant/${i}`:(console.warn(`Cannot parse variant ID for product ${e.title}: ${t}`),s=!1)}console.log(`Product ${e.title} using variant ID: ${t}`)}}if(!s&&e.id&&e.id.includes("/")){let i=e.id.split("/"),r=i[i.length-1];r&&/^\d+$/.test(r)&&(t=`gid://shopify/ProductVariant/${r}`,console.warn(`Using fallback variant ID for ${e.title}: ${t}`),s=!0)}}catch(t){console.error(`Error processing variant for product ${e.title}:`,t),s=!1}return s||console.error(`No valid variant ID found for product: ${e.title}`),r.jsx(l.E.div,{variants:k,initial:"initial",animate:"animate",exit:"exit",layout:!0,children:r.jsx(d.Z,{id:e.id,name:e.title,price:e._originalWooProduct?.salePrice||e._originalWooProduct?.price||e.price,image:e.images[0]?.url||"",slug:e.handle,material:(0,m.mJ)(e,"custom_material",void 0,e.vendor||"Premium Fabric"),isNew:!0,stockStatus:e._originalWooProduct?.stockStatus||"IN_STOCK",currencySymbol:(0,p.jK)(e.currencyCode),currencyCode:e.currencyCode||"INR",compareAtPrice:e.compareAtPrice,regularPrice:e._originalWooProduct?.regularPrice,salePrice:e._originalWooProduct?.salePrice,onSale:e._originalWooProduct?.onSale||!1,shortDescription:e._originalWooProduct?.shortDescription,type:e._originalWooProduct?.type})},e.id)})}),0===w.length&&!j&&(0,r.jsxs)("div",{className:"text-center py-16",children:[r.jsx("p",{className:"text-[#5c5c52] mb-4",children:"No products found with the selected filters."}),r.jsx("button",{onClick:()=>{setSelectedMaterials([]),h([0,25e3])},className:"text-[#2c2c27] underline",children:"Reset filters"})]})]})]})]})]})}[d,m]=u.then?(await u)():u,i()}catch(e){i(e)}})},5818:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\collection\shirts\page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[8948,3373,9404,2481,2325,7207,8578,9998,3283,6806,7321,3471,6269],()=>s(34139));module.exports=i})();