"use strict";(()=>{var e={};e.id=3673,e.ids=[3673],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},73116:(e,t,o)=>{o.r(t),o.d(t,{originalPathname:()=>y,patchFetch:()=>S,requestAsyncStorage:()=>h,routeModule:()=>k,serverHooks:()=>w,staticGenerationAsyncStorage:()=>v});var r={};o.r(r),o.d(r,{POST:()=>d});var n=o(49303),a=o(88716),s=o(60670),c=o(87070),i=o(94868),p=o(92861);let u=new i.s({url:process.env.UPSTASH_REDIS_REST_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||""}),l={PRODUCTS:86400};async function d(e){try{let t=e.headers.get("x-wc-webhook-signature"),r=await e.text();if(!function(e,t){if(!t)return!1;let r=process.env.WOOCOMMERCE_WEBHOOK_SECRET;return!!r&&t===o(84770).createHmac("sha256",r).update(e).digest("base64")}(r,t))return c.NextResponse.json({error:"Invalid signature"},{status:401});let{action:n,arg:a}=JSON.parse(r);switch(console.log("Received inventory webhook:",{action:n,productId:a?.id}),n){case"woocommerce_product_stock_status_changed":case"woocommerce_variation_stock_status_changed":case"woocommerce_product_stock_reduced":case"woocommerce_product_stock_increased":await _(a);break;case"woocommerce_product_updated":case"woocommerce_variation_updated":await m(a);break;default:console.log("Unhandled webhook action:",n)}return c.NextResponse.json({success:!0,message:"Webhook processed"})}catch(e){return console.error("Webhook processing error:",e),c.NextResponse.json({error:"Webhook processing failed"},{status:500})}}async function _(e){try{let t=e.id.toString(),o=e.slug;await p.ho(t,o);let r=`product:${o}`,n=await u.get(r);if(n){let t={...n,stockStatus:e.stock_status,stockQuantity:e.stock_quantity,availableForSale:"instock"===e.stock_status,_lastInventoryUpdate:new Date().toISOString(),_stockUpdateSource:"webhook"};await u.set(r,t,l.PRODUCTS),console.log(`Updated stock for product ${o}: ${e.stock_status} (${e.stock_quantity})`)}if(e.variations&&e.variations.length>0)for(let t of e.variations)await _(t);await g(t,{stockStatus:e.stock_status,stockQuantity:e.stock_quantity,availableForSale:"instock"===e.stock_status})}catch(e){console.error("Error handling stock update:",e)}}async function m(e){try{await _(e)}catch(e){console.error("Error handling product update:",e)}}async function g(e,t){console.log("Broadcasting stock update:",{productId:e,stockData:t}),await u.set(`stock_update:${e}`,{...t,timestamp:Date.now()},60)}let k=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/webhooks/inventory/route",pathname:"/api/webhooks/inventory",filename:"route",bundlePath:"app/api/webhooks/inventory/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\webhooks\\inventory\\route.ts",nextConfigOutput:"standalone",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:v,serverHooks:w}=k,y="/api/webhooks/inventory/route";function S(){return(0,s.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:v})}},92861:(e,t,o)=>{o.d(t,{Ls:()=>u,ho:()=>i,p_:()=>p,wm:()=>l});var r=o(94868);let n="woo:inventory:mapping:",a=new r.s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),s={};function c(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function i(e,t){try{return c()?(await a.set(`${n}${e}`,t),console.log(`Added WooCommerce mapping to Redis: ${e} -> ${t}`)):(s[e]=t,console.log(`Added WooCommerce mapping to memory: ${e} -> ${t}`)),!0}catch(o){console.error("Error adding WooCommerce inventory mapping:",o);try{return s[e]=t,console.log(`Added WooCommerce mapping to memory fallback: ${e} -> ${t}`),!0}catch(e){return console.error("Error adding to memory fallback:",e),!1}}}async function p(e){try{if(c()){let t=a.pipeline();for(let{productId:o,productSlug:r}of e)t.set(`${n}${o}`,r);await t.exec(),console.log(`Updated ${e.length} WooCommerce inventory mappings in Redis`)}else{for(let{productId:t,productSlug:o}of e)s[t]=o;console.log(`Updated ${e.length} WooCommerce inventory mappings in memory`)}return!0}catch(e){return console.error("Error batch updating WooCommerce inventory mappings:",e),!1}}async function u(){try{if(c()){let e=await a.keys(`${n}*`);if(e.length>0){let t={},o=await a.mget(...e);return e.forEach((e,r)=>{let a=e.replace(n,""),s=o[r];t[a]={wooId:a,inventory:0,sku:"",title:s,lastUpdated:new Date().toISOString()}}),t}}return{}}catch(e){return console.error("Error getting inventory mapping:",e),{}}}async function l(e){try{if(c()){let t=await a.keys(`${n}*`);t.length>0&&await a.del(...t);let o=a.pipeline();for(let[t,r]of Object.entries(e))o.set(`${n}${t}`,r.title||t);return await o.exec(),!0}return!1}catch(e){return console.error("Error updating inventory mapping:",e),!1}}}};var t=require("../../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),r=t.X(0,[8948,5972,4766,4868],()=>o(73116));module.exports=r})();