(()=>{var e={};e.id=2797,e.ids=[2797],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},80247:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>d,routeModule:()=>x,tree:()=>l}),s(4856),s(31710),s(12523);var t=s(23191),a=s(88716),n=s(37922),i=s.n(n),o=s(95231),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);s.d(r,c);let l=["",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4856)),"E:\\ankkorwoo\\ankkor\\src\\app\\search\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,31710)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\search\\page.tsx"],p="/search/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/search/page",pathname:"/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},92842:(e,r,s)=>{Promise.resolve().then(s.bind(s,76519))},76519:(e,r,s)=>{"use strict";s.a(e,async(e,t)=>{try{s.r(r),s.d(r,{default:()=>u});var a=s(10326),n=s(17577),i=s(35047),o=s(15725),c=s(53471),l=s(75290),d=e([o,c]);function p(){let e=(0,i.useSearchParams)().get("q")||"",[r,s]=(0,n.useState)([]),[t,o]=(0,n.useState)(!0),[d,p]=(0,n.useState)(null);return(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[a.jsx("h1",{className:"text-3xl font-serif mb-2",children:"Search Results"}),e&&(0,a.jsxs)("p",{className:"text-gray-600 mb-8",children:['Showing results for "',e,'"']}),t?a.jsx("div",{className:"flex justify-center items-center py-16",children:a.jsx(l.Z,{className:"h-8 w-8 animate-spin text-gray-500"})}):d?a.jsx("div",{className:"bg-red-50 text-red-700 p-4 rounded-md",children:d}):0===r.length?(0,a.jsxs)("div",{className:"py-16 text-center",children:[a.jsx("p",{className:"text-xl text-gray-500 mb-4",children:"No products found"}),a.jsx("p",{className:"text-gray-500",children:"Try using different keywords or check for spelling errors"})]}):a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6",children:r.map(e=>a.jsx(c.Z,{id:e.id,name:e.name,price:e.salePrice||e.price,image:e.image?.sourceUrl||"",slug:e.slug,stockStatus:e.stockStatus,regularPrice:e.regularPrice,salePrice:e.salePrice,onSale:e.onSale||!1,shortDescription:e.shortDescription,type:e.type},e.id))})]})}function u(){return a.jsx(n.Suspense,{fallback:a.jsx("div",{className:"container mx-auto py-12 px-4",children:a.jsx("div",{className:"text-center",children:"Loading..."})}),children:a.jsx(p,{})})}[o,c]=d.then?(await d)():d,t()}catch(e){t(e)}})},4856:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\search\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[8948,8216,9404,2481,8578,5454,8888,5725,3471],()=>s(80247));module.exports=t})();