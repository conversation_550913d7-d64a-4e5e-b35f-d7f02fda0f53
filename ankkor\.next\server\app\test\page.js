(()=>{var e={};e.id=7928,e.ids=[7928],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},58188:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>g,tree:()=>d}),r(65413),r(52617),r(12523);var s=r(23191),a=r(88716),i=r(37922),o=r.n(i),n=r(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);r.d(t,c);let d=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65413)),"E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],l=["E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"],u="/test/page",m={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},68302:(e,t,r)=>{Promise.resolve().then(r.bind(r,75072))},75072:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{default:()=>g});var a=r(10326),i=r(17577),o=r(91664),n=r(15725),c=r(54337),d=r(32913),l=r(75290),u=e([n,c,d]);[n,c,d]=u.then?(await u)():u;let m=({title:e,children:t})=>(0,a.jsxs)("div",{className:"mb-8 border rounded-md p-4",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:e}),t]}),g=()=>{let[e,t]=(0,i.useState)([]),[r,s]=(0,i.useState)([]),[u,g]=(0,i.useState)(null),[p,h]=(0,i.useState)(null),[x,y]=(0,i.useState)({}),[f,w]=(0,i.useState)({}),j=(0,c.xS)(),b=(e,t)=>{y(r=>({...r,[e]:t}))},v=(e,t)=>{w(r=>({...r,[e]:t}))},N=async()=>{try{b("products",!0);let e=await (0,n.Xp)();t(e.nodes||[]),v("products",`Success! Fetched ${e.nodes?.length||0} products`)}catch(e){console.error("Error fetching products:",e),v("products",`Error: ${e.message}`)}finally{b("products",!1)}},E=async()=>{try{b("categories",!0);let e=await (0,n.CP)();s(e.nodes||[]),v("categories",`Success! Fetched ${e.nodes?.length||0} categories`)}catch(e){console.error("Error fetching categories:",e),v("categories",`Error: ${e.message}`)}finally{b("categories",!1)}},k=async()=>{if(!e.length){v("product","Error: No products available to test with");return}try{b("product",!0);let t=e[0].databaseId,r=await (0,n.wv)(t);g(r),v("product",`Success! Fetched product: ${r.name}`)}catch(e){console.error("Error fetching product:",e),v("product",`Error: ${e.message}`)}finally{b("product",!1)}},C=async()=>{if(!e.length){v("cart","Error: No products available to test with");return}try{b("cart",!0);let t=e[0];await j.addToCart({productId:t.databaseId.toString(),name:t.name,price:t.price,quantity:1,image:{url:t.image?.sourceUrl||"",altText:t.image?.altText||t.name}}),v("cart",`Success! Added ${t.name} to cart`)}catch(e){console.error("Error adding to cart:",e),v("cart",`Error: ${e.message}`)}finally{b("cart",!1)}},I=async()=>{try{b("login",!0);let e=await (0,d.x4)("<EMAIL>","password123");e&&(h(e),v("login",`Success! Logged in as ${e.email}`))}catch(e){console.error("Error logging in:",e),v("login",`Error: ${e.message}`)}finally{b("login",!1)}},$=async()=>{try{b("register",!0);let e=`test${Math.floor(1e4*Math.random())}@example.com`;await (0,d.z2)({email:e,firstName:"Test",lastName:"User",password:"password123",username:`testuser${Math.floor(1e4*Math.random())}`}),v("register",`Success! Registered user: ${e}`)}catch(e){console.error("Error registering:",e),v("register",`Error: ${e.message}`)}finally{b("register",!1)}},S=async()=>{try{b("currentUser",!0);let e=await (0,d.ts)();e?(h(e),v("currentUser",`Success! Current user: ${e.email}`)):v("currentUser","No user is currently logged in")}catch(e){console.error("Error getting current user:",e),v("currentUser",`Error: ${e.message}`)}finally{b("currentUser",!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Integration Test"}),a.jsx(m,{title:"Products",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o.z,{onClick:N,disabled:x.products,children:[x.products&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Products"]}),f.products&&a.jsx("div",{className:`p-3 rounded-md ${f.products.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.products}),e.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"First 5 Products:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:e.slice(0,5).map(e=>(0,a.jsxs)("li",{children:[e.name," - $",e.price]},e.id))})]})]})}),a.jsx(m,{title:"Categories",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o.z,{onClick:E,disabled:x.categories,children:[x.categories&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Categories"]}),f.categories&&a.jsx("div",{className:`p-3 rounded-md ${f.categories.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.categories}),r.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"Categories:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:r.map(e=>a.jsx("li",{children:e.name},e.id))})]})]})}),a.jsx(m,{title:"Single Product",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o.z,{onClick:k,disabled:x.product||!e.length,children:[x.product&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Single Product"]}),f.product&&a.jsx("div",{className:`p-3 rounded-md ${f.product.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.product}),u&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium text-lg",children:u.name}),(0,a.jsxs)("p",{className:"text-gray-500 mt-1",children:["$",u.price]}),u.image&&a.jsx("div",{className:"mt-2 w-32 h-32 relative",children:a.jsx("img",{src:u.image.sourceUrl,alt:u.image.altText||u.name,className:"object-cover w-full h-full"})})]})]})}),a.jsx(m,{title:"Cart",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(o.z,{onClick:C,disabled:x.cart||!e.length,children:[x.cart&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Add to Cart"]}),f.cart&&a.jsx("div",{className:`p-3 rounded-md ${f.cart.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.cart}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"font-medium mb-2",children:["Cart Items: ",j.items.length]}),j.items.length>0&&a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:j.items.map(e=>(0,a.jsxs)("li",{children:[e.name," - Qty: ",e.quantity]},e.id))})]})]})}),a.jsx(m,{title:"Authentication",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(o.z,{onClick:I,disabled:x.login,children:[x.login&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Login"]}),(0,a.jsxs)(o.z,{onClick:$,disabled:x.register,variant:"outline",children:[x.register&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Register"]}),(0,a.jsxs)(o.z,{onClick:S,disabled:x.currentUser,variant:"secondary",children:[x.currentUser&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Get Current User"]})]}),f.login&&a.jsx("div",{className:`p-3 rounded-md ${f.login.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.login}),f.register&&a.jsx("div",{className:`p-3 rounded-md ${f.register.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.register}),f.currentUser&&a.jsx("div",{className:`p-3 rounded-md ${f.currentUser.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:f.currentUser}),p&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium",children:"Current User:"}),(0,a.jsxs)("p",{children:["Email: ",p.email]}),(0,a.jsxs)("p",{children:["Name: ",p.firstName," ",p.lastName]})]})]})})]})};s()}catch(e){s(e)}})},61296:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{ts:()=>c,x4:()=>o,z2:()=>n});var a=r(93690);r(18201);var i=e([a]);a=(i.then?(await i)():i)[0],(0,a.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RegisterUser($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      clientMutationId
      authToken
      refreshToken
      customer {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`,(0,a.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`,(0,a.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let d="https://maroon-lapwing-781450.hostingersite.com/graphql",l=d&&!d.startsWith("http")?`https://${d}`:d;async function o(e,t){try{let r=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e,password:t}),credentials:"include"});if(!r.ok){let e=await r.json();throw Error(e.message||"Login failed")}let s=await r.json();if(s.success&&s.user)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Login successful, user data received"),{success:!0,user:s.user,token:s.token};throw console.error("Login response missing user data"),Error("Login failed: Invalid response from server")}catch(e){return console.error("Login error:",e),{success:!1,message:e.message||"Login failed"}}}async function n(e,t,r,s){try{let a=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e,firstName:t,lastName:r,password:s}),credentials:"include"});if(!a.ok){let e=await a.json();throw Error(e.message||"Registration failed")}let i=await a.json();if(i.success&&i.customer)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Registration successful, user data received"),{success:!0,customer:i.customer,token:i.token};throw console.error("Registration response missing customer data"),Error("Registration failed: Invalid response from server")}catch(e){return console.error("Registration error:",e),{success:!1,message:e.message||"Registration failed"}}}async function c(){try{let e=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===e.status)return null;let t=await e.json();if(!e.ok||!t.success)return null;return t.user}catch(e){return console.error("Get user error:",e),null}}new a.GraphQLClient(l,{headers:{"Content-Type":"application/json"}}),s()}catch(e){s(e)}})},32913:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{ts:()=>a.ts,x4:()=>a.x4,z2:()=>a.z2});var a=r(61296),i=e([a]);a=(i.then?(await i)():i)[0],s()}catch(e){s(e)}})},54337:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{xS:()=>c});var a=r(60114),i=r(85251),o=r(15725),n=e([o]);o=(n.then?(await n)():n)[0];let c=(0,a.Ue)()((0,i.tJ)((e,t)=>({id:null,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1,error:null,initializeCart:async()=>{e({isLoading:!0,error:null});try{let t=await o.dv();if(t){let r=o.Id(t);e({id:r.id,items:r.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1});return}let r=await o.Bk();if(r){let t=o.Id(r);e({id:t.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(t){console.error("Error initializing cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},addToCart:async r=>{e({isLoading:!0,error:null});try{t().id||await t().initializeCart();let s=[{productId:r.productId,quantity:r.quantity,variationId:r.variationId}],a=await o.Xq("",s);if(a){let t=o.Id(a);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to add item to cart")}catch(t){console.error("Error adding item to cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:async(r,s)=>{e({isLoading:!0,error:null});try{if(!t().id)throw Error("Cart not initialized");if(s<=0)return t().removeCartItem(r);let a=await o.xu([{key:r,quantity:s}]);if(a){let t=o.Id(a);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to update cart item")}catch(t){console.error("Error updating cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:async r=>{e({isLoading:!0,error:null});try{if(!t().id)throw Error("Cart not initialized");let s=await o.h2("",[r]);if(s){let t=o.Id(s);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to remove item from cart")}catch(t){console.error("Error removing cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},clearCart:async()=>{e({isLoading:!0,error:null});try{let t=await o.Bk();if(t){let r=o.Id(t);e({id:r.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(t){console.error("Error clearing cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},setError:t=>e({error:t}),setIsLoading:t=>e({isLoading:t})}),{name:"woo-cart-storage",version:1,partialize:e=>({id:e.id}),onRehydrateStorage:()=>e=>{e&&e.id&&e.initializeCart()}}));s()}catch(e){s(e)}})},65413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(19510);let a=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\test\WooCommerceTest.tsx#default`);function i(){return s.jsx(a,{})}},18201:(e,t,r)=>{"use strict";class s extends Error{}s.prototype.name="InvalidTokenError"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,805,1067],()=>r(58188));module.exports=s})();