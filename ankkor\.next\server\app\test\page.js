(()=>{var e={};e.id=7928,e.ids=[7928],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},58188:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>g,tree:()=>c}),r(65413),r(51806),r(12523);var s=r(23191),a=r(88716),i=r(37922),n=r.n(i),o=r(95231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c=["",{children:["test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65413)),"E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],l=["E:\\ankkorwoo\\ankkor\\src\\app\\test\\page.tsx"],u="/test/page",m={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test/page",pathname:"/test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},68302:(e,t,r)=>{Promise.resolve().then(r.bind(r,75072))},75290:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},75072:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{default:()=>g});var a=r(10326),i=r(17577),n=r(91664),o=r(15725),d=r(54337),c=r(32913),l=r(75290),u=e([o,d,c]);[o,d,c]=u.then?(await u)():u;let m=({title:e,children:t})=>(0,a.jsxs)("div",{className:"mb-8 border rounded-md p-4",children:[a.jsx("h2",{className:"text-lg font-medium mb-4",children:e}),t]}),g=()=>{let[e,t]=(0,i.useState)([]),[r,s]=(0,i.useState)([]),[u,g]=(0,i.useState)(null),[p,h]=(0,i.useState)(null),[x,f]=(0,i.useState)({}),[y,v]=(0,i.useState)({}),b=(0,d.xS)(),w=(e,t)=>{f(r=>({...r,[e]:t}))},j=(e,t)=>{v(r=>({...r,[e]:t}))},N=async()=>{try{w("products",!0);let e=await (0,o.Xp)();t(e.nodes||[]),j("products",`Success! Fetched ${e.nodes?.length||0} products`)}catch(e){console.error("Error fetching products:",e),j("products",`Error: ${e.message}`)}finally{w("products",!1)}},k=async()=>{try{w("categories",!0);let e=await (0,o.CP)();s(e.nodes||[]),j("categories",`Success! Fetched ${e.nodes?.length||0} categories`)}catch(e){console.error("Error fetching categories:",e),j("categories",`Error: ${e.message}`)}finally{w("categories",!1)}},E=async()=>{if(!e.length){j("product","Error: No products available to test with");return}try{w("product",!0);let t=e[0].databaseId,r=await (0,o.wv)(t);g(r),j("product",`Success! Fetched product: ${r.name}`)}catch(e){console.error("Error fetching product:",e),j("product",`Error: ${e.message}`)}finally{w("product",!1)}},C=async()=>{if(!e.length){j("cart","Error: No products available to test with");return}try{w("cart",!0);let t=e[0];await b.addToCart({productId:t.databaseId.toString(),name:t.name,price:t.price,quantity:1,image:{url:t.image?.sourceUrl||"",altText:t.image?.altText||t.name}}),j("cart",`Success! Added ${t.name} to cart`)}catch(e){console.error("Error adding to cart:",e),j("cart",`Error: ${e.message}`)}finally{w("cart",!1)}},I=async()=>{try{w("login",!0);let e=await (0,c.x4)("<EMAIL>","password123");e&&(h(e),j("login",`Success! Logged in as ${e.email}`))}catch(e){console.error("Error logging in:",e),j("login",`Error: ${e.message}`)}finally{w("login",!1)}},$=async()=>{try{w("register",!0);let e=`test${Math.floor(1e4*Math.random())}@example.com`;await (0,c.z2)({email:e,firstName:"Test",lastName:"User",password:"password123",username:`testuser${Math.floor(1e4*Math.random())}`}),j("register",`Success! Registered user: ${e}`)}catch(e){console.error("Error registering:",e),j("register",`Error: ${e.message}`)}finally{w("register",!1)}},S=async()=>{try{w("currentUser",!0);let e=await (0,c.ts)();e?(h(e),j("currentUser",`Success! Current user: ${e.email}`)):j("currentUser","No user is currently logged in")}catch(e){console.error("Error getting current user:",e),j("currentUser",`Error: ${e.message}`)}finally{w("currentUser",!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Integration Test"}),a.jsx(m,{title:"Products",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:N,disabled:x.products,children:[x.products&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Products"]}),y.products&&a.jsx("div",{className:`p-3 rounded-md ${y.products.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:y.products}),e.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"First 5 Products:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:e.slice(0,5).map(e=>(0,a.jsxs)("li",{children:[e.name," - $",e.price]},e.id))})]})]})}),a.jsx(m,{title:"Categories",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:k,disabled:x.categories,children:[x.categories&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Categories"]}),y.categories&&a.jsx("div",{className:`p-3 rounded-md ${y.categories.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:y.categories}),r.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[a.jsx("h3",{className:"font-medium mb-2",children:"Categories:"}),a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:r.map(e=>a.jsx("li",{children:e.name},e.id))})]})]})}),a.jsx(m,{title:"Single Product",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:E,disabled:x.product||!e.length,children:[x.product&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Single Product"]}),y.product&&a.jsx("div",{className:`p-3 rounded-md ${y.product.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:y.product}),u&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium text-lg",children:u.name}),(0,a.jsxs)("p",{className:"text-gray-500 mt-1",children:["$",u.price]}),u.image&&a.jsx("div",{className:"mt-2 w-32 h-32 relative",children:a.jsx("img",{src:u.image.sourceUrl,alt:u.image.altText||u.name,className:"object-cover w-full h-full"})})]})]})}),a.jsx(m,{title:"Cart",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(n.z,{onClick:C,disabled:x.cart||!e.length,children:[x.cart&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Add to Cart"]}),y.cart&&a.jsx("div",{className:`p-3 rounded-md ${y.cart.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:y.cart}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"font-medium mb-2",children:["Cart Items: ",b.items.length]}),b.items.length>0&&a.jsx("ul",{className:"list-disc pl-5 space-y-1",children:b.items.map(e=>(0,a.jsxs)("li",{children:[e.name," - Qty: ",e.quantity]},e.id))})]})]})}),a.jsx(m,{title:"Authentication",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(n.z,{onClick:I,disabled:x.login,children:[x.login&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Login"]}),(0,a.jsxs)(n.z,{onClick:$,disabled:x.register,variant:"outline",children:[x.register&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Register"]}),(0,a.jsxs)(n.z,{onClick:S,disabled:x.currentUser,variant:"secondary",children:[x.currentUser&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Get Current User"]})]}),y.login&&a.jsx("div",{className:`p-3 rounded-md ${y.login.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:y.login}),y.register&&a.jsx("div",{className:`p-3 rounded-md ${y.register.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:y.register}),y.currentUser&&a.jsx("div",{className:`p-3 rounded-md ${y.currentUser.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"}`,children:y.currentUser}),p&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[a.jsx("h3",{className:"font-medium",children:"Current User:"}),(0,a.jsxs)("p",{children:["Email: ",p.email]}),(0,a.jsxs)("p",{children:["Name: ",p.firstName," ",p.lastName]})]})]})})]})};s()}catch(e){s(e)}})},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(10326);r(17577);var a=r(34214),i=r(79360),n=r(51223);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:i=!1,...d}){let c=i?a.g7:"button";return s.jsx(c,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...d})}},61296:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{ts:()=>d,x4:()=>n,z2:()=>o});var a=r(93690);r(18201);var i=e([a]);a=(i.then?(await i)():i)[0],(0,a.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RegisterUser($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      clientMutationId
      authToken
      refreshToken
      customer {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`,(0,a.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`,(0,a.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let c="https://maroon-lapwing-781450.hostingersite.com/graphql",l=c&&!c.startsWith("http")?`https://${c}`:c;async function n(e,t){try{let r=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e,password:t}),credentials:"include"});if(!r.ok){let e=await r.json();throw Error(e.message||"Login failed")}let s=await r.json();if(s.success&&s.user)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Login successful, user data received"),{success:!0,user:s.user,token:s.token};throw console.error("Login response missing user data"),Error("Login failed: Invalid response from server")}catch(e){return console.error("Login error:",e),{success:!1,message:e.message||"Login failed"}}}async function o(e,t,r,s){try{let a=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e,firstName:t,lastName:r,password:s}),credentials:"include"});if(!a.ok){let e=await a.json();throw Error(e.message||"Registration failed")}let i=await a.json();if(i.success&&i.customer)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Registration successful, user data received"),{success:!0,customer:i.customer,token:i.token};throw console.error("Registration response missing customer data"),Error("Registration failed: Invalid response from server")}catch(e){return console.error("Registration error:",e),{success:!1,message:e.message||"Registration failed"}}}async function d(){try{let e=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===e.status)return null;let t=await e.json();if(!e.ok||!t.success)return null;return t.user}catch(e){return console.error("Get user error:",e),null}}new a.GraphQLClient(l,{headers:{"Content-Type":"application/json"}}),s()}catch(e){s(e)}})},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var s=r(41135),a=r(31009);function i(...e){return(0,a.m6)((0,s.W)(e))}},32913:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{ts:()=>a.ts,x4:()=>a.x4,z2:()=>a.z2});var a=r(61296),i=e([a]);a=(i.then?(await i)():i)[0],s()}catch(e){s(e)}})},54337:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{xS:()=>d});var a=r(60114),i=r(85251),n=r(15725),o=e([n]);n=(o.then?(await o)():o)[0];let d=(0,a.Ue)()((0,i.tJ)((e,t)=>({id:null,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1,error:null,initializeCart:async()=>{e({isLoading:!0,error:null});try{let t=await n.dv();if(t){let r=n.Id(t);e({id:r.id,items:r.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1});return}let r=await n.Bk();if(r){let t=n.Id(r);e({id:t.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(t){console.error("Error initializing cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},addToCart:async r=>{e({isLoading:!0,error:null});try{t().id||await t().initializeCart();let s=[{productId:r.productId,quantity:r.quantity,variationId:r.variationId}],a=await n.Xq("",s);if(a){let t=n.Id(a);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to add item to cart")}catch(t){console.error("Error adding item to cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:async(r,s)=>{e({isLoading:!0,error:null});try{if(!t().id)throw Error("Cart not initialized");if(s<=0)return t().removeCartItem(r);let a=await n.xu([{key:r,quantity:s}]);if(a){let t=n.Id(a);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to update cart item")}catch(t){console.error("Error updating cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:async r=>{e({isLoading:!0,error:null});try{if(!t().id)throw Error("Cart not initialized");let s=await n.h2("",[r]);if(s){let t=n.Id(s);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to remove item from cart")}catch(t){console.error("Error removing cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},clearCart:async()=>{e({isLoading:!0,error:null});try{let t=await n.Bk();if(t){let r=n.Id(t);e({id:r.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(t){console.error("Error clearing cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},setError:t=>e({error:t}),setIsLoading:t=>e({isLoading:t})}),{name:"woo-cart-storage",version:1,partialize:e=>({id:e.id}),onRehydrateStorage:()=>e=>{e&&e.id&&e.initializeCart()}}));s()}catch(e){s(e)}})},65413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(19510);let a=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\test\WooCommerceTest.tsx#default`);function i(){return s.jsx(a,{})}},18201:(e,t,r)=>{"use strict";class s extends Error{}s.prototype.name="InvalidTokenError"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,3373,2325,7207,8578,3283],()=>r(58188));module.exports=s})();