(()=>{var e={};e.id=1253,e.ids=[1253],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},32222:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(52618),r(5052),r(31710),r(12523);var t=r(23191),a=r(88716),i=r(37922),n=r.n(i),o=r(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(s,l);let d=["",{children:["sign-in",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,52618)),"E:\\ankkorwoo\\ankkor\\src\\app\\sign-in\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,5052)),"E:\\ankkorwoo\\ankkor\\src\\app\\sign-in\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,31710)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\sign-in\\page.tsx"],m="/sign-in/page",u={require:r,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/sign-in/page",pathname:"/sign-in",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},12271:(e,s,r)=>{Promise.resolve().then(r.bind(r,33840))},35303:()=>{},33840:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>l});var t=r(10326),a=r(17577),i=r(35047),n=r(32457);function o(){let e=(0,i.useSearchParams)().get("redirect")||"/";return t.jsx("div",{className:"container mx-auto py-12 px-4",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[t.jsx("h1",{className:"text-3xl font-serif mb-8 text-center",children:"Sign In"}),t.jsx(n.default,{mode:"login",redirectUrl:e}),t.jsx("div",{className:"mt-8 text-center",children:(0,t.jsxs)("p",{className:"text-gray-600",children:["Don't have an account?"," ",t.jsx("a",{href:"/sign-up",className:"text-[#2c2c27] underline hover:text-[#8a8778]",children:"Create one here"})]})})]})})}function l(){return t.jsx(a.Suspense,{fallback:t.jsx("div",{className:"container mx-auto py-12 px-4",children:t.jsx("div",{className:"text-center",children:"Loading..."})}),children:t.jsx(o,{})})}},32457:(e,s,r)=>{"use strict";r.d(s,{default:()=>d});var t=r(10326),a=r(17577),i=r(35047),n=r(74723),o=r(75290),l=r(68897);let d=({mode:e,redirectUrl:s="/"})=>{let r=(0,i.useRouter)(),{refreshCustomer:d}=(0,l.O)(),[c,m]=(0,a.useState)(!1),[u,x]=(0,a.useState)(null),[p,g]=(0,a.useState)(null),[h,f]=(0,a.useState)(null),b="login"===e,{register:w,handleSubmit:j,watch:N,formState:{errors:y}}=(0,n.cI)({mode:"onBlur"}),k=N("password",""),v=async e=>{m(!0),x(null),g(null),f(null);try{if(b){console.log("Attempting login with:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e.email,password:e.password})}),a=await t.json();a.success?(g("Login successful! Redirecting..."),setTimeout(async()=>{await d(),r.push(s),r.refresh()},500)):x(a.message||"Login failed. Please check your credentials.")}else{console.log("Attempting registration for:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password})}),a=await t.json();a.success?(g("Registration successful! Redirecting..."),await d(),setTimeout(()=>{r.push(s),r.refresh()},1e3)):x(a.message||"Registration failed. Please try again.")}}catch(e){console.error("Authentication error:",e),x(e.message||"An error occurred during authentication"),g(null)}finally{m(!1)}};return(0,t.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[t.jsx("h2",{className:"text-2xl font-serif mb-6 text-center",children:b?"Sign In to Your Account":"Create an Account"}),u&&t.jsx("div",{className:"mb-4 p-3 bg-red-50 text-red-700 text-sm border border-red-200",children:u}),p&&t.jsx("div",{className:"mb-4 p-3 bg-green-50 text-green-700 text-sm border border-green-200",children:p}),h&&!1,(0,t.jsxs)("form",{onSubmit:j(v),className:"space-y-4",children:[!b&&t.jsx(t.Fragment,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),t.jsx("input",{id:"firstName",type:"text",className:`w-full p-2 border ${y.firstName?"border-red-500":"border-gray-300"}`,...w("firstName",{required:"First name is required"})}),y.firstName&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.firstName.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),t.jsx("input",{id:"lastName",type:"text",className:`w-full p-2 border ${y.lastName?"border-red-500":"border-gray-300"}`,...w("lastName",{required:"Last name is required"})}),y.lastName&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.lastName.message})]})]})}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),t.jsx("input",{id:"email",type:"email",className:`w-full p-2 border ${y.email?"border-red-500":"border-gray-300"}`,...w("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})}),y.email&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.email.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),t.jsx("input",{id:"password",type:"password",className:`w-full p-2 border ${y.password?"border-red-500":"border-gray-300"}`,...w("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}})}),y.password&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.password.message})]}),!b&&(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),t.jsx("input",{id:"confirmPassword",type:"password",className:`w-full p-2 border ${y.confirmPassword?"border-red-500":"border-gray-300"}`,...w("confirmPassword",{required:"Please confirm your password",validate:e=>e===k||"Passwords do not match"})}),y.confirmPassword&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:y.confirmPassword.message})]}),t.jsx("button",{type:"submit",disabled:c,className:"w-full bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#4c4c47] transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed",children:c?(0,t.jsxs)("span",{className:"flex items-center justify-center",children:[t.jsx(o.Z,{className:"animate-spin mr-2 h-4 w-4"}),b?"Signing in...":"Creating account..."]}):b?"Sign In":"Create Account"})]}),b?t.jsx("div",{className:"mt-4 text-center",children:t.jsx("a",{href:"/forgot-password",className:"text-sm text-[#2c2c27] hover:text-[#8a8778] underline",children:"Forgot your password?"})}):null]})}},5052:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>i,metadata:()=>a});var t=r(19510);let a={title:"Sign In | Ankkor",description:"Sign in to your Ankkor account to access your orders, wishlist, and more."};function i({children:e}){return t.jsx(t.Fragment,{children:e})}},52618:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\sign-in\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,8216,5010,8888],()=>r(32222));module.exports=t})();