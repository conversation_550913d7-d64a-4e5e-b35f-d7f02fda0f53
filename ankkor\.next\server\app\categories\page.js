(()=>{var e={};e.id=4457,e.ids=[4457],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},45188:(e,t,r)=>{"use strict";r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>g,originalPathname:()=>p,pages:()=>f,routeModule:()=>h,tree:()=>m});var s=r(86514);r(52617),r(12523);var o=r(23191),n=r(88716),a=r(37922),l=r.n(a),c=r(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);r.d(t,d);var u=e([s]);s=(u.then?(await u)():u)[0];let m=["",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86514)),"E:\\ankkorwoo\\ankkor\\src\\app\\categories\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],f=["E:\\ankkorwoo\\ankkor\\src\\app\\categories\\page.tsx"],p="/categories/page",g={require:r,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/categories/page",pathname:"/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}});i()}catch(e){i(e)}})},14504:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,92481,23)),Promise.resolve().then(r.t.bind(r,79404,23))},17710:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var i=r(66794),s=r.n(i)},57371:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var i=r(670),s=r.n(i)},10221:(e,t,r)=>{"use strict";let{createProxy:i}=r(68570);e.exports=i("E:\\ankkorwoo\\ankkor\\node_modules\\next\\dist\\client\\image-component.js")},670:(e,t,r)=>{"use strict";let{createProxy:i}=r(68570);e.exports=i("E:\\ankkorwoo\\ankkor\\node_modules\\next\\dist\\client\\link.js")},79241:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(96501);let i=r(95728),s=r(29472);function o(e){return void 0!==e.default}function n(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let l,c,d,{src:u,sizes:m,unoptimized:f=!1,priority:p=!1,loading:g,className:h,quality:x,width:v,height:b,fill:w=!1,style:y,overrideSrc:j,onLoad:_,onLoadingComplete:k,placeholder:P="empty",blurDataURL:E,fetchPriority:S,decoding:C="async",layout:N,objectFit:M,objectPosition:z,lazyBoundary:O,lazyRoot:q,...I}=e,{imgConf:A,showAltText:R,blurComplete:G,defaultLoader:D}=t,B=A||s.imageConfigDefault;if("allSizes"in B)l=B;else{let e=[...B.deviceSizes,...B.imageSizes].sort((e,t)=>e-t),t=B.deviceSizes.sort((e,t)=>e-t),i=null==(r=B.qualities)?void 0:r.sort((e,t)=>e-t);l={...B,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===D)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let F=I.loader||D;delete I.loader,delete I.srcSet;let L="__next_img_default"in F;if(L){if("custom"===l.loader)throw Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=F;F=t=>{let{config:r,...i}=t;return e(i)}}if(N){"fill"===N&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[N];e&&(y={...y,...e});let t={responsive:"100vw",fill:"100vw"}[N];t&&!m&&(m=t)}let W="",T=n(v),U=n(b);if("object"==typeof(a=u)&&(o(a)||void 0!==a.src)){let e=o(u)?u.default:u;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(c=e.blurWidth,d=e.blurHeight,E=E||e.blurDataURL,W=e.src,!w){if(T||U){if(T&&!U){let t=T/e.width;U=Math.round(e.height*t)}else if(!T&&U){let t=U/e.height;T=Math.round(e.width*t)}}else T=e.width,U=e.height}}let V=!p&&("lazy"===g||void 0===g);(!(u="string"==typeof u?u:W)||u.startsWith("data:")||u.startsWith("blob:"))&&(f=!0,V=!1),l.unoptimized&&(f=!0),L&&u.endsWith(".svg")&&!l.dangerouslyAllowSVG&&(f=!0),p&&(S="high");let J=n(x),Y=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:z}:{},R?{}:{color:"transparent"},y),$=G||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:T,heightInt:U,blurWidth:c,blurHeight:d,blurDataURL:E||"",objectFit:Y.objectFit})+'")':'url("'+P+'")',H=$?{backgroundSize:Y.objectFit||"cover",backgroundPosition:Y.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:$}:{},X=function(e){let{config:t,src:r,unoptimized:i,width:s,quality:o,sizes:n,loader:a}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:c}=function(e,t,r){let{deviceSizes:i,allSizes:s}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);i)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:s.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:s,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>s.find(t=>t>=e)||s[s.length-1]))],kind:"x"}}(t,s,n),d=l.length-1;return{sizes:n||"w"!==c?n:"100vw",srcSet:l.map((e,i)=>a({config:t,src:r,quality:o,width:e})+" "+("w"===c?e:i+1)+c).join(", "),src:a({config:t,src:r,quality:o,width:l[d]})}}({config:l,src:u,unoptimized:f,width:T,quality:J,sizes:m,loader:F});return{props:{...I,loading:V?"lazy":g,fetchPriority:S,width:T,height:U,decoding:C,className:h,style:{...Y,...H},sizes:X.sizes,srcSet:X.srcSet,src:j||X.src},meta:{unoptimized:f,priority:p,placeholder:P,fill:w}}}},95728:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:s,blurDataURL:o,objectFit:n}=e,a=i?40*i:t,l=s?40*s:r,c=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},29472:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},66794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return a}});let i=r(53370),s=r(79241),o=r(10221),n=i._(r(52049));function a(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=o.Image},52049:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:s,quality:o}=e,n=o||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+s+"&q="+n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},96501:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},86514:(e,t,r)=>{"use strict";r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(19510);r(71159);var o=r(57371),n=r(17710),a=r(19910),l=e([a]);a=(l.then?(await l)():l)[0];let d={title:"Product Categories | Ankkor",description:"Browse our collection of luxury menswear categories"};async function c(){let e=((await (0,a.CP)()).nodes||[]).filter(e=>e.count>0);return(0,s.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[s.jsx("h1",{className:"text-3xl font-serif text-center mb-12",children:"Product Categories"}),0===e.length?s.jsx("div",{className:"text-center py-12",children:s.jsx("p",{className:"text-gray-500",children:"No categories found."})}):s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,s.jsxs)(o.default,{href:`/category/${e.slug}`,className:"group block",children:[s.jsx("div",{className:"relative aspect-[4/3] bg-[#f4f3f0] overflow-hidden mb-4",children:e.image?s.jsx(n.default,{src:e.image.sourceUrl,alt:e.name,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover transition-transform group-hover:scale-105"}):s.jsx("div",{className:"w-full h-full flex items-center justify-center bg-[#f4f3f0]",children:s.jsx("span",{className:"text-[#8a8778] text-lg",children:e.name})})}),(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("h2",{className:"text-xl font-medium text-[#2c2c27]",children:e.name}),(0,s.jsxs)("p",{className:"text-[#8a8778] mt-1",children:[e.count," products"]}),e.description&&s.jsx("p",{className:"text-sm text-[#5c5c52] mt-2 line-clamp-2",children:e.description})]})]},e.id))})]})}i()}catch(e){i(e)}})},53370:(e,t,r)=>{"use strict";function i(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>i,_interop_require_default:()=>i})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,805,4766,4868,1067,9910],()=>r(45188));module.exports=i})();