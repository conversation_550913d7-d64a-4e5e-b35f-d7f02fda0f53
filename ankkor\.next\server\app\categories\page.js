(()=>{var e={};e.id=4457,e.ids=[4457],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},45188:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>f,pages:()=>g,routeModule:()=>h,tree:()=>m});var i=r(86514);r(51806),r(12523);var o=r(23191),n=r(88716),a=r(37922),l=r.n(a),c=r(95231),d={};for(let e in c)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>c[e]);r.d(t,d);var u=e([i]);i=(u.then?(await u)():u)[0];let m=["",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,86514)),"E:\\ankkorwoo\\ankkor\\src\\app\\categories\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],g=["E:\\ankkorwoo\\ankkor\\src\\app\\categories\\page.tsx"],f="/categories/page",p={require:r,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/categories/page",pathname:"/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}});s()}catch(e){s(e)}})},13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},14504:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,92481,23)),Promise.resolve().then(r.t.bind(r,79404,23))},96799:(e,t,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(10326);r(17577);var i=r(33265);let o=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),n=(0,i.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(o,{})});function a(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(n,{})})}},68897:(e,t,r)=>{"use strict";r.d(t,{CustomerProvider:()=>a,O:()=>n});var s=r(10326),i=r(17577);let o=(0,i.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),n=()=>(0,i.useContext)(o),a=({children:e})=>{let[t,r]=(0,i.useState)(null),[n,a]=(0,i.useState)(!1),[l,c]=(0,i.useState)(null),[d,u]=(0,i.useState)(null),m=async e=>{console.log("Login function called - minimal implementation")},g=async e=>{console.log("Register function called - minimal implementation")},f=async e=>(console.log("Update profile function called - minimal implementation"),{}),p=async()=>{console.log("Refresh customer function called - minimal implementation")};return s.jsx(o.Provider,{value:{customer:t,isLoading:n,isAuthenticated:!!t&&!!d,token:d,login:m,register:g,logout:()=>{console.log("Logout function called - minimal implementation"),r(null),u(null)},updateProfile:f,error:l,refreshCustomer:p},children:e})}},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>m});var s=r(10326),i=r(17577),o=r(92148),n=r(86462),a=r(54659),l=r(87888),c=r(18019),d=r(94019);let u=(0,i.createContext)(void 0);function m({children:e}){let[t,r]=(0,i.useState)([]);return(0,s.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",s=3e3)=>{let i=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:i,message:e,type:t,duration:s}])},removeToast:e=>{r(t=>t.filter(t=>t.id!==e))}},children:[e,s.jsx(f,{})]})}function g({toast:e,onRemove:t}){return(0,s.jsxs)(o.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(a.Z,{className:"h-5 w-5"});case"error":return s.jsx(l.Z,{className:"h-5 w-5"});default:return s.jsx(c.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(d.Z,{className:"h-4 w-4"})})]})}function f(){let{toasts:e,removeToast:t}=function(){let e=(0,i.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(n.M,{children:e.map(e=>s.jsx(g,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},17710:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var s=r(66794),i=r.n(s)},57371:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var s=r(670),i=r.n(s)},10221:(e,t,r)=>{"use strict";let{createProxy:s}=r(68570);e.exports=s("E:\\ankkorwoo\\ankkor\\node_modules\\next\\dist\\client\\image-component.js")},670:(e,t,r)=>{"use strict";let{createProxy:s}=r(68570);e.exports=s("E:\\ankkorwoo\\ankkor\\node_modules\\next\\dist\\client\\link.js")},79241:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(96501);let s=r(95728),i=r(29472);function o(e){return void 0!==e.default}function n(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let l,c,d,{src:u,sizes:m,unoptimized:g=!1,priority:f=!1,loading:p,className:h,quality:x,width:v,height:b,fill:y=!1,style:w,overrideSrc:j,onLoad:P,onLoadingComplete:k,placeholder:_="empty",blurDataURL:N,fetchPriority:S,decoding:C="async",layout:E,objectFit:M,objectPosition:z,lazyBoundary:O,lazyRoot:q,...A}=e,{imgConf:G,showAltText:I,blurComplete:R,defaultLoader:T}=t,D=G||i.imageConfigDefault;if("allSizes"in D)l=D;else{let e=[...D.deviceSizes,...D.imageSizes].sort((e,t)=>e-t),t=D.deviceSizes.sort((e,t)=>e-t),s=null==(r=D.qualities)?void 0:r.sort((e,t)=>e-t);l={...D,allSizes:e,deviceSizes:t,qualities:s}}if(void 0===T)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let L=A.loader||T;delete A.loader,delete A.srcSet;let B="__next_img_default"in L;if(B){if("custom"===l.loader)throw Error('Image with src "'+u+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=L;L=t=>{let{config:r,...s}=t;return e(s)}}if(E){"fill"===E&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[E];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[E];t&&!m&&(m=t)}let F="",U=n(v),W=n(b);if("object"==typeof(a=u)&&(o(a)||void 0!==a.src)){let e=o(u)?u.default:u;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(c=e.blurWidth,d=e.blurHeight,N=N||e.blurDataURL,F=e.src,!y){if(U||W){if(U&&!W){let t=U/e.width;W=Math.round(e.height*t)}else if(!U&&W){let t=W/e.height;U=Math.round(e.width*t)}}else U=e.width,W=e.height}}let $=!f&&("lazy"===p||void 0===p);(!(u="string"==typeof u?u:F)||u.startsWith("data:")||u.startsWith("blob:"))&&(g=!0,$=!1),l.unoptimized&&(g=!0),B&&u.endsWith(".svg")&&!l.dangerouslyAllowSVG&&(g=!0),f&&(S="high");let V=n(x),Z=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:M,objectPosition:z}:{},I?{}:{color:"transparent"},w),J=R||"empty"===_?null:"blur"===_?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:U,heightInt:W,blurWidth:c,blurHeight:d,blurDataURL:N||"",objectFit:Z.objectFit})+'")':'url("'+_+'")',Y=J?{backgroundSize:Z.objectFit||"cover",backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:J}:{},H=function(e){let{config:t,src:r,unoptimized:s,width:i,quality:o,sizes:n,loader:a}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:c}=function(e,t,r){let{deviceSizes:s,allSizes:i}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);s)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:i.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:i,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>i.find(t=>t>=e)||i[i.length-1]))],kind:"x"}}(t,i,n),d=l.length-1;return{sizes:n||"w"!==c?n:"100vw",srcSet:l.map((e,s)=>a({config:t,src:r,quality:o,width:e})+" "+("w"===c?e:s+1)+c).join(", "),src:a({config:t,src:r,quality:o,width:l[d]})}}({config:l,src:u,unoptimized:g,width:U,quality:V,sizes:m,loader:L});return{props:{...A,loading:$?"lazy":p,fetchPriority:S,width:U,height:W,decoding:C,className:h,style:{...Z,...Y},sizes:H.sizes,srcSet:H.srcSet,src:j||H.src},meta:{unoptimized:g,priority:f,placeholder:_,fill:y}}}},95728:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:i,blurDataURL:o,objectFit:n}=e,a=s?40*s:t,l=i?40*i:r,c=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+c+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(c?"none":"contain"===n?"xMidYMid":"cover"===n?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},29472:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},66794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return a}});let s=r(53370),i=r(79241),o=r(10221),n=s._(r(52049));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=o.Image},52049:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:s,width:i,quality:o}=e,n=o||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(s)+"&w="+i+"&q="+n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},96501:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},86514:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var i=r(19510);r(71159);var o=r(57371),n=r(17710),a=r(19910),l=e([a]);a=(l.then?(await l)():l)[0];let d={title:"Product Categories | Ankkor",description:"Browse our collection of luxury menswear categories"};async function c(){let e=((await (0,a.CP)()).nodes||[]).filter(e=>e.count>0);return(0,i.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[i.jsx("h1",{className:"text-3xl font-serif text-center mb-12",children:"Product Categories"}),0===e.length?i.jsx("div",{className:"text-center py-12",children:i.jsx("p",{className:"text-gray-500",children:"No categories found."})}):i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:e.map(e=>(0,i.jsxs)(o.default,{href:`/category/${e.slug}`,className:"group block",children:[i.jsx("div",{className:"relative aspect-[4/3] bg-[#f4f3f0] overflow-hidden mb-4",children:e.image?i.jsx(n.default,{src:e.image.sourceUrl,alt:e.name,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover transition-transform group-hover:scale-105"}):i.jsx("div",{className:"w-full h-full flex items-center justify-center bg-[#f4f3f0]",children:i.jsx("span",{className:"text-[#8a8778] text-lg",children:e.name})})}),(0,i.jsxs)("div",{className:"text-center",children:[i.jsx("h2",{className:"text-xl font-medium text-[#2c2c27]",children:e.name}),(0,i.jsxs)("p",{className:"text-[#8a8778] mt-1",children:[e.count," products"]}),e.description&&i.jsx("p",{className:"text-sm text-[#5c5c52] mt-2 line-clamp-2",children:e.description})]})]},e.id))})]})}s()}catch(e){s(e)}})},51806:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var s=r(19510),i=r(10527),o=r.n(i),n=r(36822),a=r.n(n);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${o().variable} ${a().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(d,{children:s.jsx(c,{children:s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{},53370:(e,t,r)=>{"use strict";function s(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>s,_interop_require_default:()=>s})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,3373,9404,4766,4868,2481,9910],()=>r(45188));module.exports=s})();