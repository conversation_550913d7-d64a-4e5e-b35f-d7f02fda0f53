(()=>{var e={};e.id=1660,e.ids=[1660],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},21273:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,originalPathname:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>l}),t(42399),t(52617),t(12523);var o=t(23191),n=t(88716),s=t(37922),a=t.n(s),i=t(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(r,c);let l=["",{children:["woocommerce-cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42399)),"E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],u=["E:\\ankkorwoo\\ankkor\\src\\app\\woocommerce-cart-test\\page.tsx"],d="/woocommerce-cart-test/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new o.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/woocommerce-cart-test/page",pathname:"/woocommerce-cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},87024:(e,r,t)=>{Promise.resolve().then(t.bind(t,32903))},32903:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>E});var o=t(10326),n=t(17577),s=t(86806);class a{getCartToken(){return`cart_${Math.random().toString(36).substring(2)}_${Date.now()}`}headers(e){let r={"Content-Type":"application/json","Cart-Token":this.getCartToken()};return e&&(r["X-WC-Store-API-Nonce"]=e),r}fetchOptions(e="GET",r,t){let o={method:e,headers:this.headers(r),credentials:"include"};return t&&(o.body=JSON.stringify(t)),o}getWooCommerceSessionCookie(){if("undefined"==typeof document)return null;let e=document.cookie.split(";").find(e=>e.trim().startsWith("wp_woocommerce_session_"));return e?e.trim():null}debugToken(){console.log("CartSession: Running on server, no token available")}constructor(){this.CART_TOKEN_KEY="woo_cart_token",this.TOKEN_EXPIRY_KEY="woo_cart_token_expiry",this.TOKEN_EXPIRY_DAYS=30}}let i=new a,c={retries:3,initialDelay:500,maxDelay:1e4,jitter:.1,retryableError:()=>!0,onRetry:(e,r)=>console.warn(`Retry attempt ${r} after error:`,e)};function l(e,r={}){let t={...c,...r};return async function(...r){let o;for(let n=0;n<=t.retries;n++)try{return await e(...r)}catch(r){if(o=r,n>=t.retries||!t.retryableError(r))break;t.onRetry(r,n+1);let e=Math.min(t.initialDelay*Math.pow(2,n),t.maxDelay)*(1+t.jitter*(2*Math.random()-1));await new Promise(r=>setTimeout(r,e))}throw o}}function u(e){var r;return e instanceof TypeError||e.message?.includes("network")||e.message?.includes("Network")||e.message?.includes("fetch")||e.message?.includes("connection")||e.message?.includes("timeout")||e.message?.includes("abort")||(r=e).status>=500||r.response&&r.response.status>=500}let d="https://maroon-lapwing-781450.hostingersite.com",m={CART:"/wp-json/wc/store/v1/cart",CART_ITEMS:"/wp-json/wc/store/v1/cart/items",ADD_ITEM:"/wp-json/wc/store/v1/cart/add-item",CHECKOUT:"/wp-json/wc/store/v1/checkout"};function h(e){if("number"==typeof e)return e;if(/^[0-9]+$/.test(e))return Number(e);try{if(e.includes("=")){let r=Buffer.from(e,"base64").toString().match(/(\d+)$/);if(r)return Number(r[1])}}catch(e){console.warn("Error parsing product ID:",e)}return e}async function p(){try{let e=await g();if(e)return e;let r=await f();if(r)return r;let t=await w();if(t)return t;throw Error("Could not obtain a valid nonce from any source")}catch(e){throw console.error("Error fetching nonce:",e),e}}async function g(){try{if(!d)throw Error("WooCommerce URL not configured");let e=await fetch(`${d}${m.CART}`,i.fetchOptions("GET")),r=e.headers.get("x-wc-store-api-nonce")||e.headers.get("X-WC-Store-API-Nonce");if(r)return r;try{let r=await e.json();if(r.extensions&&r.extensions.store_api_nonce)return r.extensions.store_api_nonce}catch(e){console.warn("Error parsing cart response:",e)}return null}catch(e){return console.error("Error fetching nonce from cart:",e),null}}async function f(){try{let e=await fetch("/api/ankkor/v1/nonce",i.fetchOptions("GET"));if(!e.ok)return null;let r=await e.json();if(r&&r.nonce)return r.nonce;return null}catch(e){return console.error("Error fetching nonce from custom endpoint:",e),null}}async function w(){try{if(!d)throw Error("WooCommerce URL not configured");let e=await fetch(`${d}/wp-json/wc/store/v1/products?per_page=1`,i.fetchOptions("GET")),r=e.headers.get("x-wc-store-api-nonce")||e.headers.get("X-WC-Store-API-Nonce");if(r)return r;return null}catch(e){return console.error("Error fetching nonce from products:",e),null}}l(async e=>{if(!d)throw Error("WooCommerce URL not configured");let r=await fetch(`${d}${m.CART}`,i.fetchOptions("GET",e));if(!r.ok)throw Error(`Failed to get cart: ${r.status}`);return await r.json()},{retryableError:u});let x=l(async e=>{if(!d)throw Error("WooCommerce URL not configured");let r=await fetch(`${d}${m.CART_ITEMS}`,i.fetchOptions("DELETE",e));if(!r.ok)throw Error(`Failed to clear cart: ${r.status}`);return await r.json()},{retryableError:u}),y=l(async(e,r,t,o,n)=>{if(!d)throw Error("WooCommerce URL not configured");let s={id:h(r),quantity:t};o&&(s.variation_id=h(o)),n&&(s.variation=n);let a=await fetch(`${d}${m.ADD_ITEM}`,i.fetchOptions("POST",e,s));if(!a.ok)throw Error(`Failed to add item to cart: ${a.status}`);return await a.json()},{retryableError:u}),b=l(async(e,r)=>{if(!d)throw Error("WooCommerce URL not configured");if(0===r.length)throw Error("Cart is empty");await x(e);let t=null;for(let o of r){let r={};o.attributes&&o.attributes.length>0&&o.attributes.forEach(e=>{r[`attribute_${e.name.toLowerCase().replace(/\s+/g,"-")}`]=e.value}),t=await y(e,o.productId,o.quantity,o.variationId,Object.keys(r).length>0?r:void 0)}if(!t)throw Error("Failed to sync cart with WooCommerce");return t},{retryableError:u});function E(){let[e,r]=(0,n.useState)(!1),[t,a]=(0,n.useState)(null),[c,l]=(0,n.useState)(null),[u,d]=(0,n.useState)(null),m=(0,s.rY)(),h=async()=>{r(!0),a(null),l(null);try{i.debugToken();let e=await m.syncWithWooCommerce();if(e)l(`Cart synced successfully. Redirecting to: ${e}`),setTimeout(()=>{window.location.href=e},2e3);else throw Error("Failed to get checkout URL")}catch(e){console.error("Checkout error:",e),a(e instanceof Error?e.message:"An error occurred during checkout")}finally{r(!1)}},g=async()=>{r(!0),a(null),l(null);try{let e=await p();console.log("Fetched nonce:",e);let r=m.items;if(0===r.length)throw Error("Cart is empty");let t=await b(e,r);console.log("Cart sync response:",t);let o=i.getWooCommerceSessionCookie();d({nonce:e,cartItems:r,cartResponse:t,wooSessionCookie:o}),l("Direct API call successful")}catch(e){console.error("API error:",e),a(e instanceof Error?e.message:"An error occurred during API call")}finally{r(!1)}};return(0,o.jsxs)("div",{className:"container mx-auto p-4",children:[o.jsx("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Cart Test"}),(0,o.jsxs)("div",{className:"mb-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Cart Contents"}),0===m.items.length?o.jsx("p",{children:"Cart is empty"}):(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{children:["Items: ",m.items.length]}),o.jsx("ul",{className:"list-disc pl-5",children:m.items.map(e=>(0,o.jsxs)("li",{children:[e.name," - Quantity: ",e.quantity," - Price: $",e.price]},e.id))}),(0,o.jsxs)("p",{className:"mt-2",children:["Subtotal: $",m.subtotal().toFixed(2)]})]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[o.jsx("button",{onClick:()=>{m.addToCart({productId:"123",name:"Test Product",price:"99.99",quantity:1,image:{url:"/shirt.png",altText:"Test Product"}}),l("Test product added to cart")},className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Add Test Product"}),o.jsx("button",{onClick:h,disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-green-600 hover:bg-green-700"} text-white rounded`,children:e?"Processing...":"Proceed to Checkout"}),o.jsx("button",{onClick:g,disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-purple-600 hover:bg-purple-700"} text-white rounded`,children:"Test Direct API Call"}),o.jsx("button",{onClick:()=>{m.clearCart(),l("Cart cleared")},disabled:e||0===m.items.length,className:`px-4 py-2 ${e||0===m.items.length?"bg-gray-400":"bg-red-600 hover:bg-red-700"} text-white rounded`,children:"Clear Cart"})]}),t&&(0,o.jsxs)("div",{className:"p-4 mb-4 bg-red-50 border border-red-200 text-red-700 rounded",children:[o.jsx("strong",{children:"Error:"})," ",t]}),c&&(0,o.jsxs)("div",{className:"p-4 mb-4 bg-green-50 border border-green-200 text-green-700 rounded",children:[o.jsx("strong",{children:"Success:"})," ",c]}),u&&(0,o.jsxs)("div",{className:"mt-8",children:[o.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Debug Information"}),o.jsx("pre",{className:"p-4 bg-gray-100 rounded overflow-auto max-h-96",children:JSON.stringify(u,null,2)})]})]})}l(async(e,r)=>{if(!d)throw Error("WooCommerce URL not configured");let t=await fetch(`${d}${m.CHECKOUT}`,i.fetchOptions("POST",e,r));if(!t.ok){let e=await t.text();throw Error(`Checkout failed: ${t.status} - ${e}`)}return await t.json()},{retryableError:u})},42399:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});let o=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\woocommerce-cart-test\page.tsx#default`)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,805,1067],()=>t(21273));module.exports=o})();