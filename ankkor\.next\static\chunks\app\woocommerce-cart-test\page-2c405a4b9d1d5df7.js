(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1660],{36750:function(e,t,r){Promise.resolve().then(r.bind(r,34047))},34047:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return x}});var o=r(57437),n=r(2265),a=r(87758);class c{getCartToken(){try{let e=localStorage.getItem(this.CART_TOKEN_KEY),t=localStorage.getItem(this.TOKEN_EXPIRY_KEY);if(e&&t&&new Date(t)>new Date)return e;let r="cart_".concat(Math.random().toString(36).substring(2),"_").concat(Date.now()),o=new Date;return o.setDate(o.getDate()+this.TOKEN_EXPIRY_DAYS),localStorage.setItem(this.CART_TOKEN_KEY,r),localStorage.setItem(this.TOKEN_EXPIRY_KEY,o.toISOString()),r}catch(e){return console.error("Error managing cart token:",e),"cart_".concat(Math.random().toString(36).substring(2),"_").concat(Date.now())}}headers(e){let t={"Content-Type":"application/json","Cart-Token":this.getCartToken()};return e&&(t["X-WC-Store-API-Nonce"]=e),t}fetchOptions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"GET",t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,o={method:e,headers:this.headers(t),credentials:"include"};return r&&(o.body=JSON.stringify(r)),o}getWooCommerceSessionCookie(){if("undefined"==typeof document)return null;let e=document.cookie.split(";").find(e=>e.trim().startsWith("wp_woocommerce_session_"));return e?e.trim():null}debugToken(){let e=localStorage.getItem(this.CART_TOKEN_KEY),t=localStorage.getItem(this.TOKEN_EXPIRY_KEY);if(console.log("CartSession Debug:"),console.log("- Token:",e||"Not set"),console.log("- Expiry:",t||"Not set"),t){let e=new Date(t),r=new Date,o=e<r;console.log("- Status:",o?"EXPIRED":"Valid"),o||console.log("- Days remaining:",Math.round((e.getTime()-r.getTime())/864e5))}console.log("- WooCommerce Session Cookie:",this.getWooCommerceSessionCookie()||"Not set")}constructor(){this.CART_TOKEN_KEY="woo_cart_token",this.TOKEN_EXPIRY_KEY="woo_cart_token_expiry",this.TOKEN_EXPIRY_DAYS=30}}let s=new c,i={retries:3,initialDelay:500,maxDelay:1e4,jitter:.1,retryableError:()=>!0,onRetry:(e,t)=>console.warn("Retry attempt ".concat(t," after error:"),e)};function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r={...i,...t};return async function(){let t;for(var o=arguments.length,n=Array(o),a=0;a<o;a++)n[a]=arguments[a];for(let o=0;o<=r.retries;o++)try{return await e(...n)}catch(n){if(t=n,o>=r.retries||!r.retryableError(n))break;r.onRetry(n,o+1);let e=Math.min(r.initialDelay*Math.pow(2,o),r.maxDelay)*(1+r.jitter*(2*Math.random()-1));await new Promise(t=>setTimeout(t,e))}throw t}}function u(e){var t,r,o,n,a,c,s;return e instanceof TypeError||(null===(t=e.message)||void 0===t?void 0:t.includes("network"))||(null===(r=e.message)||void 0===r?void 0:r.includes("Network"))||(null===(o=e.message)||void 0===o?void 0:o.includes("fetch"))||(null===(n=e.message)||void 0===n?void 0:n.includes("connection"))||(null===(a=e.message)||void 0===a?void 0:a.includes("timeout"))||(null===(c=e.message)||void 0===c?void 0:c.includes("abort"))||(s=e).status>=500||s.response&&s.response.status>=500}var d=r(96434).Buffer;let h="https://maroon-lapwing-781450.hostingersite.com",m={CART:"/wp-json/wc/store/v1/cart",CART_ITEMS:"/wp-json/wc/store/v1/cart/items",ADD_ITEM:"/wp-json/wc/store/v1/cart/add-item",CHECKOUT:"/wp-json/wc/store/v1/checkout"};function g(e){if("number"==typeof e)return e;if(/^[0-9]+$/.test(e))return Number(e);try{if(e.includes("=")){let t=d.from(e,"base64").toString().match(/(\d+)$/);if(t)return Number(t[1])}}catch(e){console.warn("Error parsing product ID:",e)}return e}async function f(){try{let e=await w();if(e)return e;let t=await p();if(t)return t;let r=await E();if(r)return r;throw Error("Could not obtain a valid nonce from any source")}catch(e){throw console.error("Error fetching nonce:",e),e}}async function w(){try{if(!h)throw Error("WooCommerce URL not configured");let e=await fetch("".concat(h).concat(m.CART),s.fetchOptions("GET")),t=e.headers.get("x-wc-store-api-nonce")||e.headers.get("X-WC-Store-API-Nonce");if(t)return t;try{let t=await e.json();if(t.extensions&&t.extensions.store_api_nonce)return t.extensions.store_api_nonce}catch(e){console.warn("Error parsing cart response:",e)}return null}catch(e){return console.error("Error fetching nonce from cart:",e),null}}async function p(){try{let e=await fetch("/api/ankkor/v1/nonce",s.fetchOptions("GET"));if(!e.ok)return null;let t=await e.json();if(t&&t.nonce)return t.nonce;return null}catch(e){return console.error("Error fetching nonce from custom endpoint:",e),null}}async function E(){try{if(!h)throw Error("WooCommerce URL not configured");let e=await fetch("".concat(h,"/wp-json/wc/store/v1/products?per_page=1"),s.fetchOptions("GET")),t=e.headers.get("x-wc-store-api-nonce")||e.headers.get("X-WC-Store-API-Nonce");if(t)return t;return null}catch(e){return console.error("Error fetching nonce from products:",e),null}}l(async e=>{if(!h)throw Error("WooCommerce URL not configured");let t=await fetch("".concat(h).concat(m.CART),s.fetchOptions("GET",e));if(!t.ok)throw Error("Failed to get cart: ".concat(t.status));return await t.json()},{retryableError:u});let y=l(async e=>{if(!h)throw Error("WooCommerce URL not configured");let t=await fetch("".concat(h).concat(m.CART_ITEMS),s.fetchOptions("DELETE",e));if(!t.ok)throw Error("Failed to clear cart: ".concat(t.status));return await t.json()},{retryableError:u}),b=l(async(e,t,r,o,n)=>{if(!h)throw Error("WooCommerce URL not configured");let a={id:g(t),quantity:r};o&&(a.variation_id=g(o)),n&&(a.variation=n);let c=await fetch("".concat(h).concat(m.ADD_ITEM),s.fetchOptions("POST",e,a));if(!c.ok)throw Error("Failed to add item to cart: ".concat(c.status));return await c.json()},{retryableError:u}),C=l(async(e,t)=>{if(!h)throw Error("WooCommerce URL not configured");if(0===t.length)throw Error("Cart is empty");await y(e);let r=null;for(let o of t){let t={};o.attributes&&o.attributes.length>0&&o.attributes.forEach(e=>{t["attribute_".concat(e.name.toLowerCase().replace(/\s+/g,"-"))]=e.value}),r=await b(e,o.productId,o.quantity,o.variationId,Object.keys(t).length>0?t:void 0)}if(!r)throw Error("Failed to sync cart with WooCommerce");return r},{retryableError:u});function x(){let[e,t]=(0,n.useState)(!1),[r,c]=(0,n.useState)(null),[i,l]=(0,n.useState)(null),[u,d]=(0,n.useState)(null),h=(0,a.rY)(),m=async()=>{t(!0),c(null),l(null);try{s.debugToken();let e=await h.syncWithWooCommerce();if(e)l("Cart synced successfully. Redirecting to: ".concat(e)),setTimeout(()=>{window.location.href=e},2e3);else throw Error("Failed to get checkout URL")}catch(e){console.error("Checkout error:",e),c(e instanceof Error?e.message:"An error occurred during checkout")}finally{t(!1)}},g=async()=>{t(!0),c(null),l(null);try{let e=await f();console.log("Fetched nonce:",e);let t=h.items;if(0===t.length)throw Error("Cart is empty");let r=await C(e,t);console.log("Cart sync response:",r);let o=s.getWooCommerceSessionCookie();d({nonce:e,cartItems:t,cartResponse:r,wooSessionCookie:o}),l("Direct API call successful")}catch(e){console.error("API error:",e),c(e instanceof Error?e.message:"An error occurred during API call")}finally{t(!1)}};return(0,o.jsxs)("div",{className:"container mx-auto p-4",children:[(0,o.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Cart Test"}),(0,o.jsxs)("div",{className:"mb-8",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Cart Contents"}),0===h.items.length?(0,o.jsx)("p",{children:"Cart is empty"}):(0,o.jsxs)("div",{children:[(0,o.jsxs)("p",{children:["Items: ",h.items.length]}),(0,o.jsx)("ul",{className:"list-disc pl-5",children:h.items.map(e=>(0,o.jsxs)("li",{children:[e.name," - Quantity: ",e.quantity," - Price: $",e.price]},e.id))}),(0,o.jsxs)("p",{className:"mt-2",children:["Subtotal: $",h.subtotal().toFixed(2)]})]})]}),(0,o.jsxs)("div",{className:"flex flex-wrap gap-4 mb-6",children:[(0,o.jsx)("button",{onClick:()=>{h.addToCart({productId:"123",name:"Test Product",price:"99.99",quantity:1,image:{url:"/shirt.png",altText:"Test Product"}}),l("Test product added to cart")},className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Add Test Product"}),(0,o.jsx)("button",{onClick:m,disabled:e||0===h.items.length,className:"px-4 py-2 ".concat(e||0===h.items.length?"bg-gray-400":"bg-green-600 hover:bg-green-700"," text-white rounded"),children:e?"Processing...":"Proceed to Checkout"}),(0,o.jsx)("button",{onClick:g,disabled:e||0===h.items.length,className:"px-4 py-2 ".concat(e||0===h.items.length?"bg-gray-400":"bg-purple-600 hover:bg-purple-700"," text-white rounded"),children:"Test Direct API Call"}),(0,o.jsx)("button",{onClick:()=>{h.clearCart(),l("Cart cleared")},disabled:e||0===h.items.length,className:"px-4 py-2 ".concat(e||0===h.items.length?"bg-gray-400":"bg-red-600 hover:bg-red-700"," text-white rounded"),children:"Clear Cart"})]}),r&&(0,o.jsxs)("div",{className:"p-4 mb-4 bg-red-50 border border-red-200 text-red-700 rounded",children:[(0,o.jsx)("strong",{children:"Error:"})," ",r]}),i&&(0,o.jsxs)("div",{className:"p-4 mb-4 bg-green-50 border border-green-200 text-green-700 rounded",children:[(0,o.jsx)("strong",{children:"Success:"})," ",i]}),u&&(0,o.jsxs)("div",{className:"mt-8",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Debug Information"}),(0,o.jsx)("pre",{className:"p-4 bg-gray-100 rounded overflow-auto max-h-96",children:JSON.stringify(u,null,2)})]})]})}l(async(e,t)=>{if(!h)throw Error("WooCommerce URL not configured");let r=await fetch("".concat(h).concat(m.CHECKOUT),s.fetchOptions("POST",e,t));if(!r.ok){let e=await r.text();throw Error("Checkout failed: ".concat(r.status," - ").concat(e))}return await r.json()},{retryableError:u})}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,4595,3280,8790,1104,7158,7044,9482,986,4754,1744],function(){return e(e.s=36750)}),_N_E=e.O()}]);