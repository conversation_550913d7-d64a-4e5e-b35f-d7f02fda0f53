{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "yK1D33qbJ7rMX13lwOk5e", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "R9JjNhtd9n1IFDbMYtFIbNdOq2RnJLU4tCkhuVHl1no=", "__NEXT_PREVIEW_MODE_ID": "380e6596338fdd17f3fd9d0d8aa03121", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b3091b42a41848a28da2b87c7915fc2907eb7339970da64c5b9bc1904fd61585", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ba7f1b79a20e4ea7ad641cfc2e51fa36569be96e0017170e0f2590ff00986f7b"}}}, "functions": {}, "sortedMiddleware": ["/"]}