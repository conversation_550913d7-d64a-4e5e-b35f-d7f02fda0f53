(()=>{var e={};e.id=1599,e.ids=[1599],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},37523:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GlobalError:()=>c.a,__next_app__:()=>m,originalPathname:()=>g,pages:()=>f,routeModule:()=>x,tree:()=>p});var s=r(8179);r(31710),r(12523);var a=r(23191),o=r(88716),i=r(37922),c=r.n(i),d=r(95231),l={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(t,l);var u=e([s]);s=(u.then?(await u)():u)[0];let p=["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8179)),"E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,31710)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],f=["E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"],g="/product/[slug]/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});n()}catch(e){n(e)}})},72996:(e,t,r)=>{Promise.resolve().then(r.bind(r,387))},34565:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var n=r(69029),s=r.n(n)},50131:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.RouterContext},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return c},getImageProps:function(){return i}});let n=r(91174),s=r(23078),a=r(92481),o=n._(r(86820));function i(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let c=a.Image},387:(e,t,r)=>{"use strict";r.d(t,{default:()=>g});var n=r(10326),s=r(17577),a=r(46226),o=r(92148),i=r(86806),c=r(91664),d=r(77321),l=r(76557);let u=(0,l.Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]),p=(0,l.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var f=r(34565);let g=({product:e})=>{let[t,r]=(0,s.useState)(0),[l,g]=(0,s.useState)(1),[m,x]=(0,s.useState)(null),[h,v]=(0,s.useState)({}),[y,b]=(0,s.useState)(!1),j=(0,i.rY)(),{openCart:_}=(0,d.j)(),{id:k,databaseId:w,name:N,description:S,shortDescription:P,price:R,regularPrice:E,onSale:O,stockStatus:M,image:T,galleryImages:C,attributes:A,type:q,variations:F}=e,U="VARIABLE"===q,z=[T?.sourceUrl?{sourceUrl:T.sourceUrl,altText:T.altText||N}:null,...C?.nodes||[]].filter(Boolean),I=(e,t)=>{if(v(r=>({...r,[e]:t})),U&&F?.nodes){let r={...h,[e]:t};if(A?.nodes?.every(e=>r[e.name])){let e=F.nodes.find(e=>e.attributes.nodes.every(e=>{let t=r[e.name];return e.value===t}));e?x(e):x(null)}}},L=async()=>{b(!0);try{let e={productId:w.toString(),quantity:l,name:N,price:m?.price||R,image:{url:z[0]?.sourceUrl||"",altText:z[0]?.altText||N}};await j.addToCart(e),_()}catch(e){console.error("Error adding product to cart:",e)}finally{b(!1)}},$="IN_STOCK"!==M,Z=!U||U&&m;return n.jsx("div",{className:"container mx-auto px-4 py-12",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:z[t]?.sourceUrl&&n.jsx(a.default,{src:z[t].sourceUrl,alt:z[t].altText||N,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),z.length>1&&n.jsx("div",{className:"grid grid-cols-5 gap-2",children:z.map((e,s)=>n.jsx("button",{onClick:()=>r(s),className:`relative aspect-square bg-[#f4f3f0] ${t===s?"ring-2 ring-[#2c2c27]":""}`,children:n.jsx(a.default,{src:e.sourceUrl,alt:e.altText||`${N} - Image ${s+1}`,fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},s))})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[n.jsx("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:N}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx("span",{className:"text-xl font-medium text-[#2c2c27]",children:(m?.price||R).toString().includes("₹")||(m?.price||R).toString().includes("$")||(m?.price||R).toString().includes("€")||(m?.price||R).toString().includes("\xa3")?m?.price||R:`₹${m?.price||R}`}),O&&E&&n.jsx("span",{className:"text-sm line-through text-[#8a8778]",children:E.toString().includes("₹")||E.toString().includes("$")||E.toString().includes("€")||E.toString().includes("\xa3")?E:`₹${E}`})]}),P&&n.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:P}}),U&&A?.nodes&&n.jsx("div",{className:"space-y-4",children:A.nodes.map(e=>(0,n.jsxs)("div",{className:"space-y-2",children:[n.jsx("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),n.jsx("div",{className:"flex flex-wrap gap-2",children:e.options.map(t=>n.jsx("button",{onClick:()=>I(e.name,t),className:`px-4 py-2 border ${h[e.name]===t?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"}`,children:t},t))})]},e.name))}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[n.jsx("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,n.jsxs)("div",{className:"flex items-center border border-gray-300",children:[n.jsx("button",{onClick:()=>g(e=>e>1?e-1:1),disabled:l<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:n.jsx(u,{className:"h-4 w-4"})}),n.jsx("span",{className:"px-4 py-2 border-x border-gray-300",children:l}),n.jsx("button",{onClick:()=>g(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:n.jsx(p,{className:"h-4 w-4"})})]})]}),(0,n.jsxs)("div",{className:"text-sm",children:[n.jsx("span",{className:"font-medium",children:"Availability: "}),n.jsx("span",{className:$?"text-red-600":"text-green-600",children:$?"Out of Stock":"In Stock"})]}),(0,n.jsxs)(o.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,n.jsxs)(c.z,{onClick:L,disabled:$||y||!Z,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[n.jsx(f.Z,{className:"h-5 w-5"}),y?"Adding...":"Add to Cart"]}),U&&!Z&&!$&&n.jsx("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),S&&(0,n.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[n.jsx("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),n.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:S}})]})]})]})})}},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var n=r(10326);r(17577);var s=r(34214),a=r(79360),o=r(51223);let i=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:t,size:r,asChild:a=!1,...c}){let d=a?s.g7:"button";return n.jsx(d,{"data-slot":"button",className:(0,o.cn)(i({variant:t,size:r,className:e})),...c})}},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var n=r(41135),s=r(31009);function a(...e){return(0,s.m6)((0,n.W)(e))}},58585:(e,t,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return o},RedirectType:function(){return n.RedirectType},notFound:function(){return s.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),s=r(16399);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class o extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return s},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return c},getRedirectStatusCodeFromError:function(){return g},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return d}});let s=r(54580),a=r(72934),o=r(8586),i="NEXT_REDIRECT";function c(e,t,r){void 0===r&&(r=o.RedirectStatusCode.TemporaryRedirect);let n=Error(i);n.digest=i+";"+t+";"+e+";"+r+";";let a=s.requestAsyncStorage.getStore();return a&&(n.mutableCookies=a.mutableCookies),n}function d(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.TemporaryRedirect)}function l(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,s]=e.digest.split(";",4),a=Number(s);return t===i&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(a)&&a in o.RedirectStatusCode}function p(e){return u(e)?e.digest.split(";",3)[2]:null}function f(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function g(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8179:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>l,generateMetadata:()=>d});var s=r(19510),a=r(58585),o=r(19910),i=r(80151),c=e([o]);async function d({params:e}){let{slug:t}=e;try{let e=await (0,o.gF)(t);if(!e)return{title:"Product Not Found | Ankkor",description:"The requested product could not be found."};return{title:`${e.name} | Ankkor`,description:e.shortDescription||e.description||"Luxury menswear from Ankkor.",openGraph:{images:e.image?[{url:e.image.sourceUrl,alt:e.name}]:[]}}}catch(e){return console.error("Error generating product metadata:",e),{title:"Product | Ankkor",description:"Luxury menswear from Ankkor."}}}async function l({params:e}){let{slug:t}=e;try{let e=await (0,o.gF)(t);return e||(0,a.notFound)(),s.jsx(i.Z,{product:e})}catch(e){console.error("Error fetching product:",e),(0,a.notFound)()}}o=(c.then?(await c)():c)[0],n()}catch(e){n(e)}})},80151:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\product\ProductDetail.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,8216,4766,4868,2481,2325,8888,9910],()=>r(37523));module.exports=n})();