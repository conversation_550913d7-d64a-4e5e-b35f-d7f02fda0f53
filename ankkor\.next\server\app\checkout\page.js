(()=>{var e={};e.id=285,e.ids=[285],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},70117:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>c,routeModule:()=>h,tree:()=>l}),r(51324),r(52617),r(12523);var t=r(23191),i=r(88716),a=r(37922),n=r.n(a),o=r(95231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(s,d);let l=["",{children:["checkout",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,51324)),"E:\\ankkorwoo\\ankkor\\src\\app\\checkout\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\checkout\\page.tsx"],p="/checkout/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/checkout/page",pathname:"/checkout",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},91860:(e,s,r)=>{Promise.resolve().then(r.bind(r,33612))},28916:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},14228:(e,s,r)=>{"use strict";r.d(s,{Z:()=>t});let t=(0,r(76557).Z)("Truck",[["path",{d:"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11",key:"hs4xqm"}],["path",{d:"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2",key:"11fp61"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}]])},33612:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>k});var t=r(10326),i=r(17577),a=r(35047),n=r(74723),o=r(86806),d=r(60114),l=r(85251);let c=async(e,s,r={})=>{try{if(!e||e<=0)throw Error("Invalid amount");if(e<1)throw Error("Minimum order amount is ₹1");console.log("Creating Razorpay order:",{amount:e,receipt:s,notes:r});let t=await fetch("/api/razorpay/create-order",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:Math.round(100*e),receipt:s,notes:r})});if(!t.ok){let e=await t.json();if(console.error("Razorpay order creation failed:",e),400===t.status)throw Error(e.error||"Invalid order data");if(500===t.status)throw Error("Payment gateway error. Please try again.");throw Error(e.error||"Failed to create payment order")}let i=await t.json();return console.log("Razorpay order created successfully:",i.id),i}catch(e){if(console.error("Error creating Razorpay order:",e),e instanceof Error)throw e;throw Error("Failed to create payment order")}},p=async(e,s)=>{try{let r=await fetch("/api/razorpay/verify-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({razorpay_payment_id:e.razorpay_payment_id,razorpay_order_id:e.razorpay_order_id,razorpay_signature:e.razorpay_signature,address:s.address,cartItems:s.cartItems,shipping:s.shipping})});if(!r.ok){let e=await r.json();throw Error(e.message||"Payment verification failed")}return await r.json()}catch(e){throw console.error("Error verifying payment:",e),e}},m=e=>new Promise((e,s)=>{try{s(Error("Razorpay SDK not loaded"));return}catch(e){console.error("Error initializing Razorpay:",e),s(e)}}),h=async(e,s)=>{try{let r=await fetch("/api/shipping-rates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pincode:e,cartItems:s})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to get shipping rates")}return await r.json()}catch(e){throw console.error("Error getting shipping rates:",e),e}},g=(0,d.Ue)()((0,l.tJ)((e,s)=>({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null,setCart:r=>{let t=r.reduce((e,s)=>e+("string"==typeof s.price?parseFloat(s.price):s.price)*s.quantity,0),{shippingCost:i}=s();e({cart:r,subtotal:t,finalAmount:t+i})},setShippingAddress:s=>{e({shippingAddress:s})},fetchShippingRates:async r=>{let{cart:t,subtotal:i}=s();if(!r||r.length<6){e({error:"Please enter a valid pincode"});return}e({isLoadingShipping:!0,error:null});try{let s=await h(r,t);e({shippingOptions:s,isLoadingShipping:!1,selectedShipping:null,shippingCost:0,finalAmount:i+0})}catch(s){console.error("Error fetching shipping rates:",s),e({error:s instanceof Error?s.message:"Failed to fetch shipping rates",isLoadingShipping:!1,shippingOptions:[]})}},setSelectedShipping:r=>{let{subtotal:t}=s(),i=t+r.cost;e({selectedShipping:r,shippingCost:r.cost,finalAmount:i})},calculateFinalAmount:()=>{let{subtotal:r,shippingCost:t,finalAmount:i}=s(),a=r+t;a!==i&&e({finalAmount:a})},setError:s=>{e({error:s})},setProcessingPayment:s=>{e({isProcessingPayment:s})},clearCheckout:()=>{e({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null})}}),{name:"checkout-storage",partialize:e=>({shippingAddress:e.shippingAddress,selectedShipping:e.selectedShipping})}));var u=r(68897),x=r(91664),y=r(41190),f=r(45226),j=i.forwardRef((e,s)=>(0,t.jsx)(f.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));j.displayName="Label";var N=r(51223);function v({className:e,...s}){return t.jsx(j,{"data-slot":"label",className:(0,N.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}var b=r(75290),w=r(14228),P=r(28916);function k(){let e=(0,a.useRouter)(),{isAuthenticated:s,isLoading:r}=(0,u.O)(),d=(0,o.rY)(),l=g(),[h,f]=(0,i.useState)(!1),{register:j,handleSubmit:N,watch:k,formState:{errors:S}}=(0,n.cI)(),E=k("pincode"),A=async e=>{let s={firstName:e.firstName,lastName:e.lastName,address1:e.address1,address2:e.address2,city:e.city,state:e.state,pincode:e.pincode,phone:e.phone};l.setShippingAddress(s)},_=async()=>{if(!l.shippingAddress){l.setError("Please fill in your shipping address");return}if(!l.selectedShipping){l.setError("Please select a shipping method");return}if(0===l.cart.length){l.setError("Your cart is empty");return}if(l.finalAmount<=0){l.setError("Invalid order amount");return}f(!0),l.setProcessingPayment(!0),l.setError(null);try{let s="rzp_live_H1Iyl4j48eSFYj";if(!s)throw Error("Payment gateway not configured. Please contact support.");console.log("Creating Razorpay order for amount:",l.finalAmount);let r=await c(l.finalAmount,`order_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,{customer_phone:l.shippingAddress.phone,customer_name:`${l.shippingAddress.firstName} ${l.shippingAddress.lastName}`,shipping_method:l.selectedShipping.name});console.log("Razorpay order created:",r.id),await m({key:s,amount:r.amount,currency:r.currency,name:"Ankkor",description:`Order Payment - ${l.cart.length} item(s)`,order_id:r.id,handler:async s=>{console.log("Payment successful, verifying...",s),l.setError(null);try{let r=await p(s,{address:l.shippingAddress,cartItems:l.cart,shipping:l.selectedShipping});if(console.log("Payment verification result:",r),r.success)d.clearCart(),l.clearCheckout(),e.push(`/order-confirmed?id=${r.orderId}`);else throw Error(r.message||"Payment verification failed")}catch(e){console.error("Payment verification error:",e),l.setError(e instanceof Error?e.message:"Payment verification failed. Please contact support if amount was deducted.")}finally{f(!1),l.setProcessingPayment(!1)}},prefill:{name:`${l.shippingAddress.firstName} ${l.shippingAddress.lastName}`,contact:l.shippingAddress.phone},theme:{color:"#2c2c27"},modal:{ondismiss:()=>{console.log("Payment modal dismissed"),f(!1),l.setProcessingPayment(!1)}}})}catch(s){console.error("Payment error:",s);let e="Payment failed. Please try again.";s.message?.includes("not configured")?e=s.message:s.message?.includes("network")||s.message?.includes("fetch")?e="Network error. Please check your connection and try again.":s.message?.includes("amount")?e="Invalid amount. Please refresh and try again.":s.message&&(e=s.message),l.setError(e)}finally{f(!1),l.setProcessingPayment(!1)}};return r?t.jsx("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[t.jsx(b.Z,{className:"h-8 w-8 animate-spin"}),t.jsx("span",{className:"ml-2",children:"Loading..."})]})}):s&&0!==d.items.length?(0,t.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[t.jsx("h1",{className:"text-3xl font-serif mb-8",children:"Checkout"}),l.error&&t.jsx("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded",children:l.error}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,t.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[t.jsx(w.Z,{className:"mr-2 h-5 w-5"}),"Shipping Address"]}),(0,t.jsxs)("form",{onSubmit:N(A),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx(v,{htmlFor:"firstName",children:"First Name"}),t.jsx(y.I,{id:"firstName",...j("firstName",{required:"First name is required"}),className:S.firstName?"border-red-300":""}),S.firstName&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:S.firstName.message})]}),(0,t.jsxs)("div",{children:[t.jsx(v,{htmlFor:"lastName",children:"Last Name"}),t.jsx(y.I,{id:"lastName",...j("lastName",{required:"Last name is required"}),className:S.lastName?"border-red-300":""}),S.lastName&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:S.lastName.message})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[t.jsx(v,{htmlFor:"address1",children:"Address Line 1"}),t.jsx(y.I,{id:"address1",...j("address1",{required:"Address is required"}),className:S.address1?"border-red-300":""}),S.address1&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:S.address1.message})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[t.jsx(v,{htmlFor:"address2",children:"Address Line 2 (Optional)"}),t.jsx(y.I,{id:"address2",...j("address2")})]}),(0,t.jsxs)("div",{children:[t.jsx(v,{htmlFor:"city",children:"City"}),t.jsx(y.I,{id:"city",...j("city",{required:"City is required"}),className:S.city?"border-red-300":""}),S.city&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:S.city.message})]}),(0,t.jsxs)("div",{children:[t.jsx(v,{htmlFor:"state",children:"State"}),t.jsx(y.I,{id:"state",...j("state",{required:"State is required"}),className:S.state?"border-red-300":""}),S.state&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:S.state.message})]}),(0,t.jsxs)("div",{children:[t.jsx(v,{htmlFor:"pincode",children:"Pincode"}),t.jsx(y.I,{id:"pincode",...j("pincode",{required:"Pincode is required",pattern:{value:/^[0-9]{6}$/,message:"Please enter a valid 6-digit pincode"}}),className:S.pincode?"border-red-300":"",placeholder:"Enter 6-digit pincode"}),S.pincode&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:S.pincode.message})]}),(0,t.jsxs)("div",{children:[t.jsx(v,{htmlFor:"phone",children:"Phone Number"}),t.jsx(y.I,{id:"phone",...j("phone",{required:"Phone number is required"}),className:S.phone?"border-red-300":""}),S.phone&&t.jsx("p",{className:"text-sm text-red-500 mt-1",children:S.phone.message})]})]}),t.jsx(x.z,{type:"submit",className:"mt-4 w-full bg-[#2c2c27] hover:bg-[#3c3c37] text-white",children:"Save Address & Continue"})]})]}),(0,t.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,t.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[t.jsx(w.Z,{className:"mr-2 h-5 w-5"}),"Shipping Options"]}),l.isLoadingShipping?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[t.jsx(b.Z,{className:"h-6 w-6 animate-spin mr-2"}),t.jsx("span",{children:"Loading shipping options..."})]}):0===l.shippingOptions.length?t.jsx("div",{className:"text-gray-500 py-4",children:E&&6===E.length?"No shipping options available for this pincode":"Enter a valid pincode to see shipping options"}):t.jsx("div",{className:"space-y-3",children:l.shippingOptions.map(e=>t.jsx("div",{className:`border rounded-lg p-4 cursor-pointer transition-colors ${l.selectedShipping?.id===e.id?"border-[#2c2c27] bg-gray-50":"border-gray-200 hover:border-gray-300"}`,onClick:()=>l.setSelectedShipping(e),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx("div",{children:(0,t.jsxs)("div",{className:"flex items-center",children:[t.jsx("input",{type:"radio",name:"shipping",checked:l.selectedShipping?.id===e.id,onChange:()=>l.setSelectedShipping(e),className:"mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-medium",children:e.name}),e.description&&t.jsx("p",{className:"text-sm text-gray-600",children:e.description}),e.estimatedDays&&(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Estimated delivery: ",e.estimatedDays]})]})]})}),t.jsx("div",{className:"text-lg font-medium",children:0===e.cost?"Free":`₹${e.cost.toFixed(2)}`})]})},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,t.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[t.jsx(P.Z,{className:"mr-2 h-5 w-5"}),"Payment"]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center p-4 border rounded-lg",children:[t.jsx("input",{type:"radio",id:"razorpay",name:"payment",checked:!0,readOnly:!0,className:"mr-3"}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"razorpay",className:"font-medium",children:"Razorpay"}),t.jsx("p",{className:"text-sm text-gray-600",children:"Pay securely with credit card, debit card, UPI, or net banking"})]})]}),t.jsx(x.z,{onClick:_,className:"w-full py-6 bg-[#2c2c27] hover:bg-[#3c3c37] text-white",disabled:h||!l.shippingAddress||!l.selectedShipping||l.isProcessingPayment,children:h||l.isProcessingPayment?(0,t.jsxs)(t.Fragment,{children:[t.jsx(b.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing Payment..."]}):`Proceed to Pay - ₹${l.finalAmount.toFixed(2)}`})]})]})]}),t.jsx("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm sticky top-8",children:[t.jsx("h2",{className:"text-xl font-medium mb-4",children:"Order Summary"}),(0,t.jsxs)("div",{className:"space-y-4",children:[l.cart.map(e=>(0,t.jsxs)("div",{className:"flex gap-4 py-2 border-b",children:[e.image?.url&&t.jsx("div",{className:"relative h-16 w-16 bg-gray-100 flex-shrink-0",children:t.jsx("img",{src:e.image.url,alt:e.name,className:"h-full w-full object-cover rounded"})}),(0,t.jsxs)("div",{className:"flex-1",children:[t.jsx("h3",{className:"text-sm font-medium",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["₹","string"==typeof e.price?parseFloat(e.price).toFixed(2):e.price.toFixed(2)," \xd7 ",e.quantity]})]}),(0,t.jsxs)("div",{className:"text-right",children:["₹",("string"==typeof e.price?parseFloat(e.price)*e.quantity:e.price*e.quantity).toFixed(2)]})]},e.id)),(0,t.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Subtotal"}),(0,t.jsxs)("span",{children:["₹",l.subtotal.toFixed(2)]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Shipping"}),t.jsx("span",{children:l.selectedShipping?0===l.selectedShipping.cost?"Free":`₹${l.selectedShipping.cost.toFixed(2)}`:"TBD"})]}),(0,t.jsxs)("div",{className:"flex justify-between text-lg font-medium pt-2 border-t",children:[t.jsx("span",{children:"Total"}),(0,t.jsxs)("span",{children:["₹",l.finalAmount.toFixed(2)]})]})]})]})]})})]})]}):null}},41190:(e,s,r)=>{"use strict";r.d(s,{I:()=>n});var t=r(10326),i=r(17577),a=r(51223);let n=i.forwardRef(({className:e,type:s,...r},i)=>t.jsx("input",{type:s,"data-slot":"input",className:(0,a.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",e),ref:i,...r}));n.displayName="Input"},51324:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\checkout\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[8948,805,4723,1067],()=>r(70117));module.exports=t})();