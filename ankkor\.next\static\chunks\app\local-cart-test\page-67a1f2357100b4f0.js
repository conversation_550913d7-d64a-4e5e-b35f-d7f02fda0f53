(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5046],{3556:function(e,s,a){Promise.resolve().then(a.bind(a,38204))},38204:function(e,s,a){"use strict";a.r(s),a.d(s,{default:function(){return d}});var i=a(57437),t=a(2265),n=a(87758),l=a(12381),r=a(15863),c=a(82372);function d(){let[e,s]=(0,t.useState)(!1),[a,d]=(0,t.useState)([]),[o,m]=(0,t.useState)(null),{items:u,itemCount:x,addToCart:h,updateCartItem:j,removeCartItem:p,clearCart:f,subtotal:N,total:v}=(0,n.rY)();(0,t.useEffect)(()=>{(async()=>{try{s(!0);let e=await c.Xp();d(e.nodes||[]),s(!1)}catch(e){console.error("Error loading products:",e),m(e instanceof Error?e.message:"Failed to load products"),s(!1)}})()},[]);let y=e=>{var s,a;h({productId:e.databaseId.toString(),name:e.name,price:e.price||"0",quantity:1,image:{url:(null===(s=e.image)||void 0===s?void 0:s.sourceUrl)||"",altText:(null===(a=e.image)||void 0===a?void 0:a.altText)||e.name}})},b=(e,s)=>{j(e,s)},g=e=>{p(e)},k=async()=>{try{for(let e of(s(!0),await c.Bk(),u))await c.Xq("",[{productId:parseInt(e.productId),variationId:e.variationId?parseInt(e.variationId):void 0,quantity:e.quantity}]);window.location.href="/checkout"}catch(e){console.error("Error during checkout:",e),m(e instanceof Error?e.message:"Failed to proceed to checkout")}finally{s(!1)}};return(0,i.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"Local Cart Test"}),o&&(0,i.jsxs)("div",{className:"mb-6 p-4 bg-red-50 text-red-700 rounded-md",children:[o,(0,i.jsx)(l.z,{variant:"outline",size:"sm",className:"ml-4",onClick:()=>m(null),children:"Dismiss"})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,i.jsxs)("div",{className:"border rounded-md p-4",children:[(0,i.jsx)("h2",{className:"text-lg font-medium mb-4",children:"Products"}),e&&(0,i.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,i.jsx)(r.Z,{className:"h-8 w-8 animate-spin text-gray-400"})}),!e&&0===a.length&&(0,i.jsx)("p",{className:"text-gray-500",children:"No products available"}),(0,i.jsx)("ul",{className:"space-y-4",children:a.slice(0,5).map(e=>(0,i.jsxs)("li",{className:"flex justify-between items-center border-b pb-2",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium",children:e.name}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price||"0"]})]}),(0,i.jsx)(l.z,{onClick:()=>y(e),size:"sm",children:"Add to Cart"})]},e.id))})]}),(0,i.jsxs)("div",{className:"border rounded-md p-4",children:[(0,i.jsxs)("h2",{className:"text-lg font-medium mb-4",children:["Cart (",x," items)"]}),0===u.length?(0,i.jsx)("p",{className:"text-gray-500",children:"Your cart is empty"}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("ul",{className:"space-y-4 mb-4",children:u.map(e=>(0,i.jsxs)("li",{className:"flex justify-between items-center border-b pb-2",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium",children:e.name}),(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",parseFloat(e.price).toFixed(2)," \xd7 ",e.quantity]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)(l.z,{variant:"outline",size:"sm",onClick:()=>b(e.id,e.quantity-1),disabled:e.quantity<=1,children:"-"}),(0,i.jsx)("span",{className:"w-8 text-center",children:e.quantity}),(0,i.jsx)(l.z,{variant:"outline",size:"sm",onClick:()=>b(e.id,e.quantity+1),children:"+"}),(0,i.jsx)(l.z,{variant:"ghost",size:"sm",onClick:()=>g(e.id),children:"\xd7"})]})]},e.id))}),(0,i.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{children:"Subtotal:"}),(0,i.jsxs)("span",{children:["$",N().toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,i.jsx)("span",{children:"Total:"}),(0,i.jsxs)("span",{children:["$",v().toFixed(2)]})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)(l.z,{className:"w-full",onClick:k,disabled:e,children:[e&&(0,i.jsx)(r.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Proceed to Checkout"]}),(0,i.jsx)(l.z,{variant:"outline",className:"w-full",onClick:()=>{f()},children:"Clear Cart"})]})]})]})]})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,4595,3280,8790,1104,7158,7044,9482,986,4754,1744],function(){return e(e.s=3556)}),_N_E=e.O()}]);