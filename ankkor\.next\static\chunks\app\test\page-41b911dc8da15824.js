(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7928],{96458:function(e,t,r){Promise.resolve().then(r.bind(r,37167))},37167:function(e,t,r){"use strict";r.d(t,{default:function(){return g}});var a=r(57437),i=r(2265),s=r(12381),n=r(82372),o=r(59625),c=r(89134);let d=(0,o.Ue)()((0,c.tJ)((e,t)=>({id:null,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1,error:null,initializeCart:async()=>{e({isLoading:!0,error:null});try{let t=await n.dv();if(t){let r=n.Id(t);e({id:r.id,items:r.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:r.totalQuantity,subtotal:r.cost.subtotalAmount.amount,total:r.cost.totalAmount.amount,isLoading:!1});return}let r=await n.Bk();if(r){let t=n.Id(r);e({id:t.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(t){console.error("Error initializing cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},addToCart:async r=>{e({isLoading:!0,error:null});try{t().id||await t().initializeCart();let a=[{productId:r.productId,quantity:r.quantity,variationId:r.variationId}],i=await n.Xq("",a);if(i){let t=n.Id(i);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to add item to cart")}catch(t){console.error("Error adding item to cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},updateCartItem:async(r,a)=>{e({isLoading:!0,error:null});try{if(!t().id)throw Error("Cart not initialized");if(a<=0)return t().removeCartItem(r);let i=await n.xu([{key:r,quantity:a}]);if(i){let t=n.Id(i);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to update cart item")}catch(t){console.error("Error updating cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},removeCartItem:async r=>{e({isLoading:!0,error:null});try{if(!t().id)throw Error("Cart not initialized");let a=await n.h2("",[r]);if(a){let t=n.Id(a);e({items:t.lines.map(e=>({id:e.id,productId:e.merchandise.product.id,variationId:e.merchandise.id!==e.merchandise.product.id?e.merchandise.id:void 0,quantity:e.quantity,name:e.merchandise.title,price:e.cost.totalAmount.amount,image:e.merchandise.product.image,attributes:e.merchandise.selectedOptions})),itemCount:t.totalQuantity,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,isLoading:!1})}else throw Error("Failed to remove item from cart")}catch(t){console.error("Error removing cart item:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},clearCart:async()=>{e({isLoading:!0,error:null});try{let t=await n.Bk();if(t){let r=n.Id(t);e({id:r.id,items:[],itemCount:0,subtotal:"0",total:"0",isLoading:!1})}else throw Error("Failed to create a new cart")}catch(t){console.error("Error clearing cart:",t),e({error:t instanceof Error?t.message:"An unknown error occurred",isLoading:!1})}},setError:t=>e({error:t}),setIsLoading:t=>e({isLoading:t})}),{name:"woo-cart-storage",version:1,partialize:e=>({id:e.id}),onRehydrateStorage:()=>e=>{e&&e.id&&e.initializeCart()}}));var l=r(77690),m=r(15863);let u=e=>{let{title:t,children:r}=e;return(0,a.jsxs)("div",{className:"mb-8 border rounded-md p-4",children:[(0,a.jsx)("h2",{className:"text-lg font-medium mb-4",children:t}),r]})};var g=()=>{let[e,t]=(0,i.useState)([]),[r,o]=(0,i.useState)([]),[c,g]=(0,i.useState)(null),[h,p]=(0,i.useState)(null),[x,y]=(0,i.useState)({}),[v,j]=(0,i.useState)({}),b=d(),N=(e,t)=>{y(r=>({...r,[e]:t}))},f=(e,t)=>{j(r=>({...r,[e]:t}))},w=async()=>{try{var e;N("products",!0);let r=await (0,n.Xp)();t(r.nodes||[]),f("products","Success! Fetched ".concat((null===(e=r.nodes)||void 0===e?void 0:e.length)||0," products"))}catch(e){console.error("Error fetching products:",e),f("products","Error: ".concat(e.message))}finally{N("products",!1)}},E=async()=>{try{var e;N("categories",!0);let t=await (0,n.CP)();o(t.nodes||[]),f("categories","Success! Fetched ".concat((null===(e=t.nodes)||void 0===e?void 0:e.length)||0," categories"))}catch(e){console.error("Error fetching categories:",e),f("categories","Error: ".concat(e.message))}finally{N("categories",!1)}},C=async()=>{if(!e.length){f("product","Error: No products available to test with");return}try{N("product",!0);let t=e[0].databaseId,r=await (0,n.wv)(t);g(r),f("product","Success! Fetched product: ".concat(r.name))}catch(e){console.error("Error fetching product:",e),f("product","Error: ".concat(e.message))}finally{N("product",!1)}},I=async()=>{if(!e.length){f("cart","Error: No products available to test with");return}try{var t,r;N("cart",!0);let a=e[0];await b.addToCart({productId:a.databaseId.toString(),name:a.name,price:a.price,quantity:1,image:{url:(null===(t=a.image)||void 0===t?void 0:t.sourceUrl)||"",altText:(null===(r=a.image)||void 0===r?void 0:r.altText)||a.name}}),f("cart","Success! Added ".concat(a.name," to cart"))}catch(e){console.error("Error adding to cart:",e),f("cart","Error: ".concat(e.message))}finally{N("cart",!1)}},A=async()=>{try{N("login",!0);let e=await (0,l.x4)("<EMAIL>","password123");e&&(p(e),f("login","Success! Logged in as ".concat(e.email)))}catch(e){console.error("Error logging in:",e),f("login","Error: ".concat(e.message))}finally{N("login",!1)}},L=async()=>{try{N("register",!0);let e="test".concat(Math.floor(1e4*Math.random()),"@example.com");await (0,l.z2)({email:e,firstName:"Test",lastName:"User",password:"password123",username:"testuser".concat(Math.floor(1e4*Math.random()))}),f("register","Success! Registered user: ".concat(e))}catch(e){console.error("Error registering:",e),f("register","Error: ".concat(e.message))}finally{N("register",!1)}},k=async()=>{try{N("currentUser",!0);let e=await (0,l.ts)();e?(p(e),f("currentUser","Success! Current user: ".concat(e.email))):f("currentUser","No user is currently logged in")}catch(e){console.error("Error getting current user:",e),f("currentUser","Error: ".concat(e.message))}finally{N("currentUser",!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"WooCommerce Integration Test"}),(0,a.jsx)(u,{title:"Products",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(s.z,{onClick:w,disabled:x.products,children:[x.products&&(0,a.jsx)(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Products"]}),v.products&&(0,a.jsx)("div",{className:"p-3 rounded-md ".concat(v.products.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:v.products}),e.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"First 5 Products:"}),(0,a.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:e.slice(0,5).map(e=>(0,a.jsxs)("li",{children:[e.name," - $",e.price]},e.id))})]})]})}),(0,a.jsx)(u,{title:"Categories",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(s.z,{onClick:E,disabled:x.categories,children:[x.categories&&(0,a.jsx)(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Categories"]}),v.categories&&(0,a.jsx)("div",{className:"p-3 rounded-md ".concat(v.categories.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:v.categories}),r.length>0&&(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("h3",{className:"font-medium mb-2",children:"Categories:"}),(0,a.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:r.map(e=>(0,a.jsx)("li",{children:e.name},e.id))})]})]})}),(0,a.jsx)(u,{title:"Single Product",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(s.z,{onClick:C,disabled:x.product||!e.length,children:[x.product&&(0,a.jsx)(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Fetch Single Product"]}),v.product&&(0,a.jsx)("div",{className:"p-3 rounded-md ".concat(v.product.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:v.product}),c&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[(0,a.jsx)("h3",{className:"font-medium text-lg",children:c.name}),(0,a.jsxs)("p",{className:"text-gray-500 mt-1",children:["$",c.price]}),c.image&&(0,a.jsx)("div",{className:"mt-2 w-32 h-32 relative",children:(0,a.jsx)("img",{src:c.image.sourceUrl,alt:c.image.altText||c.name,className:"object-cover w-full h-full"})})]})]})}),(0,a.jsx)(u,{title:"Cart",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(s.z,{onClick:I,disabled:x.cart||!e.length,children:[x.cart&&(0,a.jsx)(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Add to Cart"]}),v.cart&&(0,a.jsx)("div",{className:"p-3 rounded-md ".concat(v.cart.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:v.cart}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsxs)("h3",{className:"font-medium mb-2",children:["Cart Items: ",b.items.length]}),b.items.length>0&&(0,a.jsx)("ul",{className:"list-disc pl-5 space-y-1",children:b.items.map(e=>(0,a.jsxs)("li",{children:[e.name," - Qty: ",e.quantity]},e.id))})]})]})}),(0,a.jsx)(u,{title:"Authentication",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,a.jsxs)(s.z,{onClick:A,disabled:x.login,children:[x.login&&(0,a.jsx)(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Login"]}),(0,a.jsxs)(s.z,{onClick:L,disabled:x.register,variant:"outline",children:[x.register&&(0,a.jsx)(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Test Register"]}),(0,a.jsxs)(s.z,{onClick:k,disabled:x.currentUser,variant:"secondary",children:[x.currentUser&&(0,a.jsx)(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Get Current User"]})]}),v.login&&(0,a.jsx)("div",{className:"p-3 rounded-md ".concat(v.login.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:v.login}),v.register&&(0,a.jsx)("div",{className:"p-3 rounded-md ".concat(v.register.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:v.register}),v.currentUser&&(0,a.jsx)("div",{className:"p-3 rounded-md ".concat(v.currentUser.includes("Error")?"bg-red-50 text-red-700":"bg-green-50 text-green-700"),children:v.currentUser}),h&&(0,a.jsxs)("div",{className:"mt-4 p-4 border rounded-md",children:[(0,a.jsx)("h3",{className:"font-medium",children:"Current User:"}),(0,a.jsxs)("p",{children:["Email: ",h.email]}),(0,a.jsxs)("p",{children:["Name: ",h.firstName," ",h.lastName]})]})]})})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,4595,3280,8790,1104,7158,7044,9482,986,4754,1744],function(){return e(e.s=96458)}),_N_E=e.O()}]);