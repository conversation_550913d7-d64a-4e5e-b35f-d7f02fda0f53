(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5838],{71047:function(e,s,a){"use strict";var l=a(57437),i=a(2265),t=a(99376),d=a(43886),n=a(17168),c=a(79820),r=a(12381),o=a(40279),m=a(92369),x=a(44794),h=a(94630),p=a(3371);s.default=()=>{var e,s;let a=(0,t.useRouter)(),{customer:j,updateProfile:u,refreshCustomer:N}=(0,p.O)();if(!j)return(0,l.jsx)("div",{className:"flex justify-center items-center py-12",children:(0,l.jsx)("div",{className:"text-[#8a8778]",children:"Loading account information..."})});let[g,b]=(0,i.useState)("profile"),[f,v]=(0,i.useState)(!1),[y,w]=(0,i.useState)(!1),[F,S]=(0,i.useState)(null),[I,k]=(0,i.useState)(null),[P,T]=(0,i.useState)({firstName:j.firstName||"",lastName:j.lastName||"",email:j.email||"",phone:(null===(e=j.billing)||void 0===e?void 0:e.phone)||""});(0,i.useEffect)(()=>{var e;T({firstName:j.firstName||"",lastName:j.lastName||"",email:j.email||"",phone:(null===(e=j.billing)||void 0===e?void 0:e.phone)||""})},[j]);let C=e=>{let{name:s,value:a}=e.target;T(e=>({...e,[s]:a}))},D=async e=>{e.preventDefault(),S(null),k(null),w(!0);try{let e={id:j.id,firstName:P.firstName,lastName:P.lastName,billing:{...j.billing,firstName:P.firstName,lastName:P.lastName,phone:P.phone}};await u(e),v(!1),k("Profile updated successfully"),setTimeout(()=>{k(null)},3e3)}catch(e){console.error("Error updating profile:",e),S(e.message||"An error occurred while updating your profile")}finally{w(!1)}};return(0,l.jsxs)(d.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,l.jsxs)("p",{className:"text-[#8a8778] mb-8",children:["Welcome back, ",j.firstName," ",j.lastName]}),(0,l.jsxs)(n.mQ,{defaultValue:g,onValueChange:b,className:"w-full",children:[(0,l.jsxs)(n.dr,{className:"grid ".concat(j.downloadableItems&&j.downloadableItems.nodes.length>0?"grid-cols-3":"grid-cols-2"," mb-8"),children:[(0,l.jsxs)(n.SP,{value:"profile",className:"flex items-center gap-2",children:[(0,l.jsx)(m.Z,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Profile"})]}),(0,l.jsxs)(n.SP,{value:"orders",className:"flex items-center gap-2",children:[(0,l.jsx)(x.Z,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Orders"})]}),j.downloadableItems&&j.downloadableItems.nodes.length>0&&(0,l.jsxs)(n.SP,{value:"downloads",className:"flex items-center gap-2",children:[(0,l.jsx)(x.Z,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"hidden sm:inline",children:"Downloads"})]})]}),(0,l.jsx)(n.nU,{value:"profile",children:(0,l.jsxs)(c.Zb,{children:[(0,l.jsxs)(c.Ol,{children:[(0,l.jsx)(c.ll,{children:"Profile Information"}),(0,l.jsx)(c.SZ,{children:"Manage your personal information"})]}),(0,l.jsxs)(c.aY,{className:"space-y-4",children:[F&&(0,l.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4",children:F}),I&&(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4",children:I}),f?(0,l.jsxs)("form",{onSubmit:D,className:"space-y-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"First Name"}),(0,l.jsx)(o.I,{id:"firstName",name:"firstName",type:"text",value:P.firstName,onChange:C,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",required:!0})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Last Name"}),(0,l.jsx)(o.I,{id:"lastName",name:"lastName",type:"text",value:P.lastName,onChange:C,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",required:!0})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Email"}),(0,l.jsx)(o.I,{id:"email",name:"email",type:"email",value:P.email,onChange:C,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778] bg-[#f4f3f0]",disabled:!0}),(0,l.jsx)("p",{className:"mt-1 text-xs text-[#8a8778]",children:"Email cannot be changed. Please contact support if you need to change your email."})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Phone"}),(0,l.jsx)(o.I,{id:"phone",name:"phone",type:"tel",value:P.phone,onChange:C,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",placeholder:"(*************"})]}),(0,l.jsxs)("div",{className:"flex gap-2 pt-2",children:[(0,l.jsx)(r.z,{type:"submit",disabled:y,className:"bg-[#2c2c27]",children:y?"Saving...":"Save Changes"}),(0,l.jsx)(r.z,{type:"button",variant:"outline",onClick:()=>{var e;v(!1),T({firstName:j.firstName||"",lastName:j.lastName||"",email:j.email||"",phone:(null===(e=j.billing)||void 0===e?void 0:e.phone)||""})},children:"Cancel"})]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"First Name"}),(0,l.jsx)("p",{className:"text-[#2c2c27]",children:j.firstName||"Not provided"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Last Name"}),(0,l.jsx)("p",{className:"text-[#2c2c27]",children:j.lastName||"Not provided"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Email"}),(0,l.jsx)("p",{className:"text-[#2c2c27]",children:j.email||"Not provided"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Phone"}),(0,l.jsx)("p",{className:"text-[#2c2c27]",children:(null===(s=j.billing)||void 0===s?void 0:s.phone)||"Not provided"})]})]})]})]}),(0,l.jsx)(c.eW,{children:!f&&(0,l.jsxs)(r.z,{variant:"outline",onClick:()=>v(!0),className:"flex items-center gap-2",children:[(0,l.jsx)(h.Z,{className:"h-4 w-4"}),"Edit Profile"]})})]})}),(0,l.jsx)(n.nU,{value:"orders",children:(0,l.jsxs)(c.Zb,{children:[(0,l.jsxs)(c.Ol,{children:[(0,l.jsx)(c.ll,{children:"Order History"}),(0,l.jsx)(c.SZ,{children:"View and track your orders"})]}),(0,l.jsx)(c.aY,{children:j.orders&&j.orders.nodes.length>0?(0,l.jsx)("div",{className:"space-y-6",children:j.orders.nodes.map(e=>(0,l.jsxs)("div",{className:"border border-[#e5e2d9] p-6 rounded-md",children:[(0,l.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h3",{className:"font-medium text-[#2c2c27]",children:["Order #",e.databaseId]}),(0,l.jsxs)("p",{className:"text-sm text-[#8a8778]",children:["Placed on ",new Date(e.date).toLocaleDateString()]}),e.paymentMethodTitle&&(0,l.jsxs)("p",{className:"text-xs text-[#8a8778]",children:["Payment: ",e.paymentMethodTitle]})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("p",{className:"font-medium text-[#2c2c27]",children:["$",parseFloat(e.total||"0").toFixed(2)]}),(0,l.jsx)("span",{className:"text-xs px-2 py-1 rounded ".concat("completed"===e.status?"bg-green-100 text-green-800":"processing"===e.status?"bg-blue-100 text-blue-800":"cancelled"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"),children:e.status.toUpperCase()})]})]}),(0,l.jsxs)("div",{className:"space-y-3 mb-4",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-[#5c5c52]",children:"Items"}),e.lineItems.nodes.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center gap-4 p-3 bg-gray-50 rounded",children:[e.product.node.image&&(0,l.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded overflow-hidden flex-shrink-0",children:(0,l.jsx)("img",{src:e.product.node.image.sourceUrl,alt:e.product.node.image.altText||e.product.node.name,className:"w-full h-full object-cover"})}),(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("p",{className:"text-[#2c2c27] font-medium",children:e.product.node.name}),e.variation&&e.variation.node.attributes&&(0,l.jsx)("div",{className:"text-xs text-[#8a8778]",children:e.variation.node.attributes.nodes.map((s,a)=>(0,l.jsxs)("span",{children:[s.name,": ",s.value,a<e.variation.node.attributes.nodes.length-1&&", "]},a))}),(0,l.jsxs)("p",{className:"text-xs text-[#8a8778]",children:["Qty: ",e.quantity," \xd7 $",(parseFloat(e.total||"0")/e.quantity).toFixed(2)]})]}),(0,l.jsx)("div",{className:"text-right",children:(0,l.jsxs)("p",{className:"font-medium text-[#2c2c27]",children:["$",parseFloat(e.total||"0").toFixed(2)]})})]},s))]}),(0,l.jsxs)("div",{className:"border-t border-[#e5e2d9] pt-4",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-[#8a8778]",children:"Subtotal:"}),(0,l.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.subtotal||"0").toFixed(2)]})]}),e.shippingTotal&&parseFloat(e.shippingTotal)>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-[#8a8778]",children:"Shipping:"}),(0,l.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.shippingTotal).toFixed(2)]})]}),e.totalTax&&parseFloat(e.totalTax)>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-[#8a8778]",children:"Tax:"}),(0,l.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.totalTax).toFixed(2)]})]}),e.discountTotal&&parseFloat(e.discountTotal)>0&&(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-[#8a8778]",children:"Discount:"}),(0,l.jsxs)("p",{className:"font-medium text-green-600",children:["-$",parseFloat(e.discountTotal).toFixed(2)]})]})]}),(e.shipping||e.billing)&&(0,l.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.billing&&(0,l.jsxs)("div",{children:[(0,l.jsx)("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Billing Address"}),(0,l.jsxs)("div",{className:"text-xs text-[#8a8778]",children:[(0,l.jsxs)("p",{children:[e.billing.firstName," ",e.billing.lastName]}),e.billing.company&&(0,l.jsx)("p",{children:e.billing.company}),(0,l.jsx)("p",{children:e.billing.address1}),e.billing.address2&&(0,l.jsx)("p",{children:e.billing.address2}),(0,l.jsxs)("p",{children:[e.billing.city,", ",e.billing.state," ",e.billing.postcode]}),(0,l.jsx)("p",{children:e.billing.country}),e.billing.phone&&(0,l.jsxs)("p",{children:["Phone: ",e.billing.phone]})]})]}),e.shipping&&e.shipping.address1&&(0,l.jsxs)("div",{children:[(0,l.jsx)("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Shipping Address"}),(0,l.jsxs)("div",{className:"text-xs text-[#8a8778]",children:[(0,l.jsxs)("p",{children:[e.shipping.firstName," ",e.shipping.lastName]}),e.shipping.company&&(0,l.jsx)("p",{children:e.shipping.company}),(0,l.jsx)("p",{children:e.shipping.address1}),e.shipping.address2&&(0,l.jsx)("p",{children:e.shipping.address2}),(0,l.jsxs)("p",{children:[e.shipping.city,", ",e.shipping.state," ",e.shipping.postcode]}),(0,l.jsx)("p",{children:e.shipping.country})]})]})]}),e.customerNote&&(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Order Notes"}),(0,l.jsx)("p",{className:"text-xs text-[#8a8778] bg-gray-50 p-2 rounded",children:e.customerNote})]}),(0,l.jsx)("div",{className:"mt-4 flex justify-end",children:(0,l.jsx)(r.z,{variant:"outline",size:"sm",children:"View Full Details"})})]})]},e.id))}):(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("p",{className:"text-[#8a8778] mb-4",children:"You haven't placed any orders yet."}),(0,l.jsx)(r.z,{onClick:()=>a.push("/collection"),children:"Start Shopping"})]})})]})}),j.downloadableItems&&j.downloadableItems.nodes.length>0&&(0,l.jsx)(n.nU,{value:"downloads",children:(0,l.jsxs)(c.Zb,{children:[(0,l.jsxs)(c.Ol,{children:[(0,l.jsx)(c.ll,{children:"Downloadable Items"}),(0,l.jsx)(c.SZ,{children:"Access your digital downloads and products"})]}),(0,l.jsx)(c.aY,{children:(0,l.jsx)("div",{className:"space-y-4",children:j.downloadableItems.nodes.map((e,s)=>(0,l.jsx)("div",{className:"border border-[#e5e2d9] p-4 rounded-md",children:(0,l.jsxs)("div",{className:"flex justify-between items-start",children:[(0,l.jsxs)("div",{className:"flex-1",children:[(0,l.jsx)("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),(0,l.jsxs)("p",{className:"text-sm text-[#8a8778] mb-2",children:["Product: ",e.product.node.name]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"text-xs text-[#8a8778]",children:"Download ID"}),(0,l.jsx)("p",{className:"text-[#2c2c27]",children:e.downloadId})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"text-xs text-[#8a8778]",children:"Downloads Remaining"}),(0,l.jsx)("p",{className:"text-[#2c2c27]",children:null!==e.downloadsRemaining?e.downloadsRemaining:"Unlimited"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"text-xs text-[#8a8778]",children:"Access Expires"}),(0,l.jsx)("p",{className:"text-[#2c2c27]",children:e.accessExpires?new Date(e.accessExpires).toLocaleDateString():"Never"})]})]})]}),(0,l.jsx)("div",{className:"ml-4",children:(0,l.jsx)(r.z,{variant:"outline",size:"sm",disabled:0===e.downloadsRemaining,children:"Download"})})]})},s))})})]})})]})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,986,4754,2877,447,1744],function(){return e(e.s=48742)}),_N_E=e.O()}]);