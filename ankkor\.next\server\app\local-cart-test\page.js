(()=>{var e={};e.id=5046,e.ids=[5046],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},8933:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),t(27854),t(51806),t(12523);var a=t(23191),r=t(88716),i=t(37922),n=t.n(i),l=t(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c=["",{children:["local-cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,27854)),"E:\\ankkorwoo\\ankkor\\src\\app\\local-cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\local-cart-test\\page.tsx"],u="/local-cart-test/page",x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/local-cart-test/page",pathname:"/local-cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},59893:(e,s,t)=>{Promise.resolve().then(t.bind(t,29012))},75290:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});let a=(0,t(76557).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},29012:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{default:()=>u});var r=t(10326),i=t(17577),n=t(86806),l=t(91664),o=t(75290),c=t(15725),d=e([c]);function u(){let[e,s]=(0,i.useState)(!1),[t,a]=(0,i.useState)([]),[d,u]=(0,i.useState)(null),{items:x,itemCount:m,addToCart:p,updateCartItem:h,removeCartItem:f,clearCart:g,subtotal:v,total:b}=(0,n.rY)(),j=e=>{p({productId:e.databaseId.toString(),name:e.name,price:e.price||"0",quantity:1,image:{url:e.image?.sourceUrl||"",altText:e.image?.altText||e.name}})},y=(e,s)=>{h(e,s)},k=e=>{f(e)},N=async()=>{try{for(let e of(s(!0),await c.Bk(),x))await c.Xq("",[{productId:parseInt(e.productId),variationId:e.variationId?parseInt(e.variationId):void 0,quantity:e.quantity}]);window.location.href="/checkout"}catch(e){console.error("Error during checkout:",e),u(e instanceof Error?e.message:"Failed to proceed to checkout")}finally{s(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Local Cart Test"}),d&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 text-red-700 rounded-md",children:[d,r.jsx(l.z,{variant:"outline",size:"sm",className:"ml-4",onClick:()=>u(null),children:"Dismiss"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:"Products"}),e&&r.jsx("div",{className:"flex items-center justify-center py-8",children:r.jsx(o.Z,{className:"h-8 w-8 animate-spin text-gray-400"})}),!e&&0===t.length&&r.jsx("p",{className:"text-gray-500",children:"No products available"}),r.jsx("ul",{className:"space-y-4",children:t.slice(0,5).map(e=>(0,r.jsxs)("li",{className:"flex justify-between items-center border-b pb-2",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price||"0"]})]}),r.jsx(l.z,{onClick:()=>j(e),size:"sm",children:"Add to Cart"})]},e.id))})]}),(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[(0,r.jsxs)("h2",{className:"text-lg font-medium mb-4",children:["Cart (",m," items)"]}),0===x.length?r.jsx("p",{className:"text-gray-500",children:"Your cart is empty"}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("ul",{className:"space-y-4 mb-4",children:x.map(e=>(0,r.jsxs)("li",{className:"flex justify-between items-center border-b pb-2",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",parseFloat(e.price).toFixed(2)," \xd7 ",e.quantity]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>y(e.id,e.quantity-1),disabled:e.quantity<=1,children:"-"}),r.jsx("span",{className:"w-8 text-center",children:e.quantity}),r.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>y(e.id,e.quantity+1),children:"+"}),r.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>k(e.id),children:"\xd7"})]})]},e.id))}),(0,r.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Subtotal:"}),(0,r.jsxs)("span",{children:["$",v().toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between font-medium",children:[r.jsx("span",{children:"Total:"}),(0,r.jsxs)("span",{children:["$",b().toFixed(2)]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(l.z,{className:"w-full",onClick:N,disabled:e,children:[e&&r.jsx(o.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Proceed to Checkout"]}),r.jsx(l.z,{variant:"outline",className:"w-full",onClick:()=>{g()},children:"Clear Cart"})]})]})]})]})]})}c=(d.then?(await d)():d)[0],a()}catch(e){a(e)}})},91664:(e,s,t)=>{"use strict";t.d(s,{z:()=>o});var a=t(10326);t(17577);var r=t(34214),i=t(79360),n=t(51223);let l=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o({className:e,variant:s,size:t,asChild:i=!1,...o}){let c=i?r.g7:"button";return a.jsx(c,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:t,className:e})),...o})}},51223:(e,s,t)=>{"use strict";t.d(s,{cn:()=>i});var a=t(41135),r=t(31009);function i(...e){return(0,r.m6)((0,a.W)(e))}},27854:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\local-cart-test\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,3373,2325,7207,8578,3283,6806],()=>t(8933));module.exports=a})();