(()=>{var e={};e.id=5046,e.ids=[5046],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},8933:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>o}),t(27854),t(52617),t(12523);var a=t(23191),r=t(88716),i=t(37922),n=t.n(i),l=t(95231),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let o=["",{children:["local-cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,27854)),"E:\\ankkorwoo\\ankkor\\src\\app\\local-cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\local-cart-test\\page.tsx"],m="/local-cart-test/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/local-cart-test/page",pathname:"/local-cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},59893:(e,s,t)=>{Promise.resolve().then(t.bind(t,29012))},29012:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{default:()=>m});var r=t(10326),i=t(17577),n=t(86806),l=t(91664),c=t(75290),o=t(15725),d=e([o]);function m(){let[e,s]=(0,i.useState)(!1),[t,a]=(0,i.useState)([]),[d,m]=(0,i.useState)(null),{items:x,itemCount:u,addToCart:p,updateCartItem:h,removeCartItem:j,clearCart:g,subtotal:y,total:f}=(0,n.rY)(),k=e=>{p({productId:e.databaseId.toString(),name:e.name,price:e.price||"0",quantity:1,image:{url:e.image?.sourceUrl||"",altText:e.image?.altText||e.name}})},v=(e,s)=>{h(e,s)},b=e=>{j(e)},N=async()=>{try{for(let e of(s(!0),await o.Bk(),x))await o.Xq("",[{productId:parseInt(e.productId),variationId:e.variationId?parseInt(e.variationId):void 0,quantity:e.quantity}]);window.location.href="/checkout"}catch(e){console.error("Error during checkout:",e),m(e instanceof Error?e.message:"Failed to proceed to checkout")}finally{s(!1)}};return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Local Cart Test"}),d&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 text-red-700 rounded-md",children:[d,r.jsx(l.z,{variant:"outline",size:"sm",className:"ml-4",onClick:()=>m(null),children:"Dismiss"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[r.jsx("h2",{className:"text-lg font-medium mb-4",children:"Products"}),e&&r.jsx("div",{className:"flex items-center justify-center py-8",children:r.jsx(c.Z,{className:"h-8 w-8 animate-spin text-gray-400"})}),!e&&0===t.length&&r.jsx("p",{className:"text-gray-500",children:"No products available"}),r.jsx("ul",{className:"space-y-4",children:t.slice(0,5).map(e=>(0,r.jsxs)("li",{className:"flex justify-between items-center border-b pb-2",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",e.price||"0"]})]}),r.jsx(l.z,{onClick:()=>k(e),size:"sm",children:"Add to Cart"})]},e.id))})]}),(0,r.jsxs)("div",{className:"border rounded-md p-4",children:[(0,r.jsxs)("h2",{className:"text-lg font-medium mb-4",children:["Cart (",u," items)"]}),0===x.length?r.jsx("p",{className:"text-gray-500",children:"Your cart is empty"}):(0,r.jsxs)(r.Fragment,{children:[r.jsx("ul",{className:"space-y-4 mb-4",children:x.map(e=>(0,r.jsxs)("li",{className:"flex justify-between items-center border-b pb-2",children:[(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"font-medium",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["₹",parseFloat(e.price).toFixed(2)," \xd7 ",e.quantity]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>v(e.id,e.quantity-1),disabled:e.quantity<=1,children:"-"}),r.jsx("span",{className:"w-8 text-center",children:e.quantity}),r.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>v(e.id,e.quantity+1),children:"+"}),r.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>b(e.id),children:"\xd7"})]})]},e.id))}),(0,r.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Subtotal:"}),(0,r.jsxs)("span",{children:["$",y().toFixed(2)]})]}),(0,r.jsxs)("div",{className:"flex justify-between font-medium",children:[r.jsx("span",{children:"Total:"}),(0,r.jsxs)("span",{children:["$",f().toFixed(2)]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)(l.z,{className:"w-full",onClick:N,disabled:e,children:[e&&r.jsx(c.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Proceed to Checkout"]}),r.jsx(l.z,{variant:"outline",className:"w-full",onClick:()=>{g()},children:"Clear Cart"})]})]})]})]})]})}o=(d.then?(await d)():d)[0],a()}catch(e){a(e)}})},27854:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\local-cart-test\page.tsx#default`)}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[8948,805,1067],()=>t(8933));module.exports=a})();