(()=>{var e={};e.id=4456,e.ids=[4456],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},51507:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(99106),r(51806),r(12523);var a=r(23191),i=r(88716),s=r(37922),o=r.n(s),n=r(95231),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let d=["",{children:["wishlist",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,99106)),"E:\\ankkorwoo\\ankkor\\src\\app\\wishlist\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\wishlist\\page.tsx"],u="/wishlist/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/wishlist/page",pathname:"/wishlist",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94373:(e,t,r)=>{Promise.resolve().then(r.bind(r,79626))},32933:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},67427:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},34565:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},98091:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});let a=(0,r(76557).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},46226:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var a=r(69029),i=r.n(a)},90434:(e,t,r)=>{"use strict";r.d(t,{default:()=>i.a});var a=r(79404),i=r.n(a)},69029:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return n}});let a=r(91174),i=r(23078),s=r(92481),o=a._(r(86820));function n(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!0,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},79626:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>b});var i=r(10326),s=r(17577),o=r(90434),n=r(46226),l=r(67427),d=r(94019),c=r(98091),u=r(34565),m=r(32933),p=r(96040),h=r(68897),f=r(68211),g=r(92148),x=r(40381),y=r(91664),v=e([p]);p=(v.then?(await v)():v)[0];let w=e=>{if("number"==typeof e)return e;if(!e)return 0;let t=e.toString().replace(/[^\d.-]/g,""),r=parseFloat(t);return isNaN(r)?0:r},j=e=>w(e).toFixed(2);function b(){let e=(0,p.x)(),{items:t,removeFromWishlist:r,clearWishlist:a}=(0,p.Y)(),{isAuthenticated:v,isLoading:b}=(0,h.O)(),[w,k]=(0,s.useState)(!0),[N,I]=(0,s.useState)({}),[C,E]=(0,s.useState)(!1),$=(t,a=!1)=>{try{if(!t.variantId||"string"!=typeof t.variantId||""===t.variantId.trim()){console.error("Invalid variant ID:",t.variantId),x.Am.error("Unable to add this item to your cart. Invalid product variant.");return}let i=t.variantId;if(!i.startsWith("gid://"))try{let e=i.replace(/\D/g,"");if(!e)throw Error(`Could not extract a valid numeric ID from "${i}"`);i=`gid://shopify/ProductVariant/${e}`}catch(e){console.error("Failed to format variant ID:",e),x.Am.error("This product has an invalid variant ID format.");return}console.log(`Adding item to cart: ${t.name||"Unnamed Product"} with variant ID: ${i}`),e.addItem({productId:t.id,variantId:i,title:t.name||"Unnamed Product",handle:t.handle||"#",image:t.image||"/placeholder-image.jpg",price:t.price?j(t.price):"0.00",quantity:1,currencyCode:"INR"}).then(()=>{x.Am.success(`${t.name||"Product"} added to your cart!`),I(e=>({...e,[t.id]:!0})),setTimeout(()=>{I(e=>({...e,[t.id]:!1}))},2e3),a&&r(t.id)}).catch(e=>{console.error("Error from cart.addItem:",e),e.message?.includes("variant is no longer available")?x.Am.error("This product is no longer available in the store."):e.message?.includes("Invalid variant ID")?x.Am.error("This product has an invalid variant format. Please try another item."):x.Am.error("Unable to add this item to your cart. Please try again later.")})}catch(e){console.error("Error in handleAddToCart:",e),x.Am.error("An unexpected error occurred. Please try again later.")}};return(0,i.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[i.jsx("h1",{className:"text-3xl font-serif",children:"My Wishlist"}),t.length>0&&i.jsx(y.z,{variant:"outline",onClick:a,className:"text-sm",children:"Clear All"})]}),w?i.jsx("div",{className:"flex items-center justify-center py-24",children:i.jsx(f.Z,{size:"lg",color:"#8a8778"})}):(0,i.jsxs)(i.Fragment,{children:[!v&&t.length>0&&!C&&i.jsx("div",{className:"mb-6 p-3 bg-blue-50 border border-blue-200 rounded-md",children:(0,i.jsxs)("div",{className:"flex items-center",children:[i.jsx(l.Z,{className:"h-4 w-4 text-blue-600 mr-2"}),(0,i.jsxs)("p",{className:"text-sm text-blue-800",children:["Your wishlist is saved locally on this device.",i.jsx(o.default,{href:"/sign-up",className:"ml-1 font-medium underline hover:no-underline",children:"Create an account"})," to access it from anywhere."]})]})}),!v&&C&&t.length>0&&i.jsx(g.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"mb-8 p-4 border border-[#e5e2d9] bg-[#f8f8f5] rounded-md",children:(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"flex items-start",children:[i.jsx(l.Z,{className:"h-5 w-5 text-[#8a8778] mt-1 mr-3"}),(0,i.jsxs)("div",{children:[i.jsx("h3",{className:"font-serif font-medium text-[#2c2c27]",children:"Sync your wishlist across devices"}),i.jsx("p",{className:"text-sm text-[#5c5c52] mt-1",children:"Your wishlist works without an account and is saved locally. Sign in to sync it across all your devices."})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[i.jsx(o.default,{href:"/sign-up",className:"text-sm text-[#2c2c27] font-medium hover:text-[#8a8778] transition-colors",children:"Sign Up"}),i.jsx("button",{onClick:()=>{E(!1)},className:"text-[#8a8778] hover:text-[#2c2c27] transition-colors","aria-label":"Dismiss",children:i.jsx(d.Z,{className:"h-4 w-4"})})]})]})}),0===t.length?(0,i.jsxs)("div",{className:"text-center py-16",children:[i.jsx("div",{className:"inline-flex justify-center items-center w-16 h-16 bg-gray-100 rounded-full mb-4",children:i.jsx(l.Z,{className:"h-8 w-8 text-gray-400"})}),i.jsx("h2",{className:"text-xl font-medium mb-2",children:"Your wishlist is empty"}),i.jsx("p",{className:"text-gray-500 mb-2",children:"Add items you love to your wishlist. Review them anytime and easily move them to the cart."}),!v&&i.jsx("p",{className:"text-sm text-gray-400 mb-6",children:"No account needed - your wishlist is saved locally on this device."}),i.jsx(o.default,{href:"/categories",children:i.jsx(y.z,{children:"Continue Shopping"})})]}):(0,i.jsxs)(i.Fragment,{children:[i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:t.map(e=>(0,i.jsxs)("div",{className:"border rounded-md overflow-hidden",children:[(0,i.jsxs)("div",{className:"relative",children:[i.jsx(o.default,{href:`/product/${e.handle||"#"}`,children:i.jsx("div",{className:"aspect-square relative bg-gray-100",children:i.jsx(n.default,{src:e.image||"/placeholder-image.jpg",alt:e.name||"Product image",fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover"})})}),i.jsx("button",{onClick:()=>r(e.id),className:"absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:bg-gray-100","aria-label":"Remove from wishlist",children:i.jsx(c.Z,{className:"h-4 w-4 text-gray-600"})})]}),(0,i.jsxs)("div",{className:"p-4",children:[i.jsx(o.default,{href:`/product/${e.handle||"#"}`,children:i.jsx("h2",{className:"font-medium text-lg hover:underline",children:e.name||"Unnamed Product"})}),(0,i.jsxs)("p",{className:"text-gray-700 my-2",children:["₹",e.price?j(e.price):"0.00"]}),(0,i.jsxs)(y.z,{onClick:()=>$(e),className:"w-full mt-2 flex items-center justify-center gap-2",children:[i.jsx(u.Z,{className:"h-4 w-4"}),"Add to Cart"]})]})]},e.id))}),i.jsx("div",{className:"mt-12 text-center",children:i.jsx(o.default,{href:"/categories",children:i.jsx(y.z,{variant:"outline",children:"Continue Shopping"})})}),i.jsx("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full border-collapse",children:[i.jsx("thead",{className:"border-b border-[#e5e2d9]",children:(0,i.jsxs)("tr",{children:[i.jsx("th",{className:"py-4 text-left font-serif text-[#2c2c27]",children:"Product"}),i.jsx("th",{className:"py-4 text-left font-serif text-[#2c2c27]",children:"Price"}),i.jsx("th",{className:"py-4 text-center font-serif text-[#2c2c27]",children:"Actions"})]})}),i.jsx("tbody",{className:"divide-y divide-[#e5e2d9]",children:t.map(e=>(0,i.jsxs)("tr",{className:"group",children:[i.jsx("td",{className:"py-6",children:(0,i.jsxs)("div",{className:"flex items-center",children:[i.jsx("div",{className:"relative mr-4 h-24 w-20 overflow-hidden bg-[#f4f3f0]",children:i.jsx(o.default,{href:`/product/${e.handle||"#"}`,children:i.jsx(n.default,{src:e.image||"/placeholder-image.jpg",alt:e.name||"Product image",fill:!0,sizes:"(max-width: 768px) 80px, 120px",className:"object-cover object-center transition-transform duration-500 group-hover:scale-105"})})}),(0,i.jsxs)("div",{children:[i.jsx(o.default,{href:`/product/${e.handle||"#"}`,className:"font-serif text-lg text-[#2c2c27] hover:text-[#8a8778] transition-colors",children:e.name||"Unnamed Product"}),i.jsx("p",{className:"text-sm text-[#8a8778]",children:e.material||"Material not specified"})]})]})}),(0,i.jsxs)("td",{className:"py-6 font-medium text-[#2c2c27]",children:["₹",e.price?j(e.price):"0.00"]}),i.jsx("td",{className:"py-6",children:(0,i.jsxs)("div",{className:"flex items-center justify-center space-x-4",children:[i.jsx(g.E.button,{onClick:()=>$(e),className:`${N[e.id]?"bg-[#2c2c27] text-[#f4f3f0]":"text-[#2c2c27]"} p-2 rounded-full transition-colors hover:text-[#8a8778]`,"aria-label":"Add to cart",whileTap:{scale:.95},children:N[e.id]?i.jsx(m.Z,{className:"h-5 w-5"}):i.jsx(u.Z,{className:"h-5 w-5"})}),i.jsx(g.E.button,{onClick:()=>r(e.id),className:"text-[#2c2c27] p-2 rounded-full hover:text-[#8a8778] transition-colors","aria-label":"Remove from wishlist",whileTap:{scale:.95},children:i.jsx(d.Z,{className:"h-5 w-5"})})]})})]},e.id))})]})})]})]}),i.jsx(x.x7,{position:"top-center",toastOptions:{duration:3e3,style:{background:"#F8F8F5",color:"#2C2C27",border:"1px solid #E5E2D9"},success:{iconTheme:{primary:"#2C2C27",secondary:"#F8F8F5"}},error:{iconTheme:{primary:"#2C2C27",secondary:"#F8F8F5"}}}})]})}a()}catch(e){a(e)}})},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var a=r(10326);r(17577);var i=r(34214),s=r(79360),o=r(51223);let n=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:s=!1,...l}){let d=s?i.g7:"button";return a.jsx(d,{"data-slot":"button",className:(0,o.cn)(n({variant:t,size:r,className:e})),...l})}},68211:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var a=r(10326);r(17577);let i=({size:e="md",color:t="#2c2c27",className:r=""})=>{let i={sm:{container:"w-6 h-6",dot:"w-1 h-1"},md:{container:"w-10 h-10",dot:"w-1.5 h-1.5"},lg:{container:"w-16 h-16",dot:"w-2 h-2"}};return(0,a.jsxs)(a.Fragment,{children:[a.jsx("style",{children:`
        @keyframes loaderRotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
        
        @keyframes loaderDot1 {
          0%, 100% {
            opacity: 0.2;
          }
          25% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot2 {
          0%, 100% {
            opacity: 0.2;
          }
          50% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot3 {
          0%, 100% {
            opacity: 0.2;
          }
          75% {
            opacity: 1;
          }
        }
        
        @keyframes loaderDot4 {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.2;
          }
        }
      `}),a.jsx("div",{className:`flex items-center justify-center ${r}`,children:(0,a.jsxs)("div",{className:`relative ${i[e].container}`,children:[a.jsx("div",{className:`absolute top-0 left-1/2 -translate-x-1/2 ${i[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot1 1.5s infinite"}}),a.jsx("div",{className:`absolute top-1/2 right-0 -translate-y-1/2 ${i[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot2 1.5s infinite"}}),a.jsx("div",{className:`absolute bottom-0 left-1/2 -translate-x-1/2 ${i[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot3 1.5s infinite"}}),a.jsx("div",{className:`absolute top-1/2 left-0 -translate-y-1/2 ${i[e].dot} rounded-full`,style:{backgroundColor:t,animation:"loaderDot4 1.5s infinite"}}),a.jsx("div",{className:"absolute inset-0 rounded-full",style:{border:`2px solid ${t}`,borderTopColor:"transparent",animation:"loaderRotate 1s linear infinite"}})]})})]})}},96040:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{Y:()=>u,x:()=>c});var i=r(60114),s=r(85251),o=r(15725),n=e([o]);o=(n.then?(await n)():n)[0];let l={getItem:e=>null,setItem:(e,t)=>{},removeItem:e=>{}},d=(e,t)=>{try{if(!t||!t.lines){console.error("Invalid normalized cart data",t);return}let r=t.lines.reduce((e,t)=>e+(t.quantity||0),0),a=t.lines.map(e=>({id:e.id,variantId:e.merchandise.id,productId:e.merchandise.product.id,title:e.merchandise.product.title,handle:e.merchandise.product.handle,image:e.merchandise.product.image?.url||"",price:e.merchandise.price,quantity:e.quantity,currencyCode:e.merchandise.currencyCode}));e({items:a,subtotal:t.cost.subtotalAmount.amount,total:t.cost.totalAmount.amount,currencyCode:t.cost.totalAmount.currencyCode,itemCount:r,checkoutUrl:t.checkoutUrl,isLoading:!1})}catch(t){console.error("Error updating cart state:",t),e({items:[],subtotal:"0.00",total:"0.00",itemCount:0,isLoading:!1})}},c=(0,i.Ue)()((0,s.tJ)((e,t)=>({cartId:null,items:[],isOpen:!1,isLoading:!1,subtotal:"0.00",total:"0.00",currencyCode:"USD",itemCount:0,checkoutUrl:null,initializationInProgress:!1,initializationError:null,openCart:()=>e({isOpen:!0}),closeCart:()=>e({isOpen:!1}),toggleCart:()=>e(e=>({isOpen:!e.isOpen})),initCart:async()=>{let r=t();if(r.initializationInProgress)return console.log("Cart initialization already in progress, skipping"),null;e({isLoading:!0,initializationInProgress:!0,initializationError:null});try{if(r.cartId)try{let t=await (0,o.dv)();if(t)return e({isLoading:!1,initializationInProgress:!1}),t}catch(e){console.log("Existing cart validation failed, creating new cart")}let t=await (0,o.Bk)();if(t&&t.id)return e({cartId:t.id,checkoutUrl:t.checkoutUrl,isLoading:!1,initializationInProgress:!1}),console.log("Cart initialized with ID:",t.id),t;throw Error("Failed to create cart: No cart ID returned")}catch(t){return console.error("Failed to initialize cart:",t),e({isLoading:!1,initializationInProgress:!1,initializationError:t instanceof Error?t.message:"Unknown error initializing cart"}),null}},addItem:async r=>{e({isLoading:!0});try{if(!r.variantId)throw console.error("Cannot add item to cart: Missing variant ID",r),e({isLoading:!1}),Error("Missing variant ID for item");let a=t().cartId;if(!a){console.log("Cart not initialized, creating a new cart...");let e=await (0,o.Bk)();if(e&&e.id)console.log("New cart created:",e.id),a=e.id;else throw Error("Failed to initialize cart")}if(!a)throw Error("Failed to initialize cart: No cart ID available");console.log(`Adding item to cart: ${r.title} (${r.variantId}), quantity: ${r.quantity}`);try{let t=await (0,o.Xq)(a,[{merchandiseId:r.variantId,quantity:r.quantity||1}]);if(!t)throw Error("Failed to add item to cart: No cart returned");let i=(0,o.Id)(t);d(e,i),e({isOpen:!0}),console.log(`Item added to cart successfully. Cart now has ${i.lines.length} items.`)}catch(e){if(console.error("Shopify API error when adding to cart:",e),e instanceof Error)throw Error(`Failed to add item to cart: ${e.message}`);throw Error("Failed to add item to cart: Unknown API error")}}catch(t){throw console.error("Failed to add item to cart:",t),e({isLoading:!1}),t}},updateItem:async(r,a)=>{let i=t();e({isLoading:!0});try{if(!i.cartId)throw Error("Cart not initialized");if(console.log(`Updating item in cart: ${r}, new quantity: ${a}`),a<=0)return console.log(`Quantity is ${a}, removing item from cart`),t().removeItem(r);let s=await (0,o.xu)(i.cartId,[{id:r,quantity:a}]);if(!s)throw Error("Failed to update item: No cart returned");let n=(0,o.Id)(s);d(e,n),console.log(`Item updated successfully. Cart now has ${n.lines.length} items.`)}catch(t){throw console.error("Failed to update item in cart:",t),e({isLoading:!1}),t}},removeItem:async r=>{let a=t();e({isLoading:!0});try{if(!a.cartId)throw console.error("Cannot remove item: Cart not initialized"),Error("Cart not initialized");console.log(`Removing item from cart: ${r}`);let t=[...a.items],i=t.find(e=>e.id===r);i?console.log(`Removing "${i.title}" (${i.variantId}) from cart`):console.warn(`Item with ID ${r} not found in cart`);let s=await (0,o.h2)(a.cartId,[r]);if(!s)throw console.error("Failed to remove item: No cart returned from Shopify"),Error("Failed to remove item: No cart returned");let n=(0,o.Id)(s),l=n.lines.map(e=>({id:e.id,title:e.merchandise.product.title}));console.log("Cart before removal:",t.length,"items"),console.log("Cart after removal:",l.length,"items"),t.length===l.length&&console.warn("Item count did not change after removal operation"),d(e,n),console.log(`Item removed successfully. Cart now has ${n.lines.length} items.`)}catch(t){throw console.error("Failed to remove item from cart:",t),e({isLoading:!1}),t}},clearCart:async()=>{t(),e({isLoading:!0});try{console.log("Clearing cart and creating a new one");let t=await (0,o.Bk)();if(!t)throw Error("Failed to create new cart");e({cartId:t.id,items:[],subtotal:"0.00",total:"0.00",itemCount:0,checkoutUrl:t.checkoutUrl,isLoading:!1}),console.log("Cart cleared successfully. New cart ID:",t.id)}catch(t){throw console.error("Failed to clear cart:",t),e({isLoading:!1}),t}}}),{name:"ankkor-cart",storage:(0,s.FL)(()=>l),version:1,partialize:e=>({cartId:e.cartId,items:e.items,subtotal:e.subtotal,total:e.total,currencyCode:e.currencyCode,itemCount:e.itemCount,checkoutUrl:e.checkoutUrl})})),u=(0,i.Ue)()((0,s.tJ)((e,t)=>({items:[],isLoading:!1,addToWishlist:t=>{e(e=>e.items.some(e=>e.id===t.id)?e:{items:[...e.items,t]})},removeFromWishlist:t=>{e(e=>({items:e.items.filter(e=>e.id!==t)}))},clearWishlist:()=>{e({items:[]})},isInWishlist:e=>t().items.some(t=>t.id===e)}),{name:"ankkor-wishlist",storage:(0,s.FL)(()=>l),partialize:e=>({items:e.items})}));a()}catch(e){a(e)}})},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var a=r(41135),i=r(31009);function s(...e){return(0,i.m6)((0,a.W)(e))}},99106:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\wishlist\page.tsx#default`)},40381:(e,t,r)=>{"use strict";r.d(t,{x7:()=>ed,Am:()=>F});var a,i=r(17577);let s={data:""},o=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||s,n=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,l=/\/\*[^]*?\*\/|  +/g,d=/\n+/g,c=(e,t)=>{let r="",a="",i="";for(let s in e){let o=e[s];"@"==s[0]?"i"==s[1]?r=s+" "+o+";":a+="f"==s[1]?c(o,s):s+"{"+c(o,"k"==s[1]?"":t)+"}":"object"==typeof o?a+=c(o,t?t.replace(/([^,])+/g,e=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):s):null!=o&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=c.p?c.p(s,o):s+":"+o+";")}return r+(t&&i?t+"{"+i+"}":i)+a},u={},m=e=>{if("object"==typeof e){let t="";for(let r in e)t+=r+m(e[r]);return t}return e},p=(e,t,r,a,i)=>{let s=m(e),o=u[s]||(u[s]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return"go"+r})(s));if(!u[o]){let t=s!==e?e:(e=>{let t,r,a=[{}];for(;t=n.exec(e.replace(l,""));)t[4]?a.shift():t[3]?(r=t[3].replace(d," ").trim(),a.unshift(a[0][r]=a[0][r]||{})):a[0][t[1]]=t[2].replace(d," ").trim();return a[0]})(e);u[o]=c(i?{["@keyframes "+o]:t}:t,r?"":"."+o)}let p=r&&u.g?u.g:null;return r&&(u.g=u[o]),((e,t,r,a)=>{a?t.data=t.data.replace(a,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(u[o],t,a,p),o},h=(e,t,r)=>e.reduce((e,a,i)=>{let s=t[i];if(s&&s.call){let e=s(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;s=t?"."+t:e&&"object"==typeof e?e.props?"":c(e,""):!1===e?"":e}return e+a+(null==s?"":s)},"");function f(e){let t=this||{},r=e.call?e(t.p):e;return p(r.unshift?r.raw?h(r,[].slice.call(arguments,1),t.p):r.reduce((e,r)=>Object.assign(e,r&&r.call?r(t.p):r),{}):r,o(t.target),t.g,t.o,t.k)}f.bind({g:1});let g,x,y,v=f.bind({k:1});function b(e,t){let r=this||{};return function(){let a=arguments;function i(s,o){let n=Object.assign({},s),l=n.className||i.className;r.p=Object.assign({theme:x&&x()},n),r.o=/ *go\d+/.test(l),n.className=f.apply(r,a)+(l?" "+l:""),t&&(n.ref=o);let d=e;return e[0]&&(d=n.as||e,delete n.as),y&&d[0]&&y(n),g(d,n)}return t?t(i):i}}var w=e=>"function"==typeof e,j=(e,t)=>w(e)?e(t):e,k=(()=>{let e=0;return()=>(++e).toString()})(),N=(()=>{let e;return()=>e})(),I=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,20)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return I(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:a}=t;return{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let i=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+i}))}}},C=[],E={toasts:[],pausedAt:void 0},$=e=>{E=I(E,e),C.forEach(e=>{e(E)})},z={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},P=(e={})=>{let[t,r]=(0,i.useState)(E),a=(0,i.useRef)(E);(0,i.useEffect)(()=>(a.current!==E&&r(E),C.push(r),()=>{let e=C.indexOf(r);e>-1&&C.splice(e,1)}),[]);let s=t.toasts.map(t=>{var r,a,i;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(r=e[t.type])?void 0:r.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(a=e[t.type])?void 0:a.duration)||(null==e?void 0:e.duration)||z[t.type],style:{...e.style,...null==(i=e[t.type])?void 0:i.style,...t.style}}});return{...t,toasts:s}},D=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(null==r?void 0:r.id)||k()}),A=e=>(t,r)=>{let a=D(t,e,r);return $({type:2,toast:a}),a.id},F=(e,t)=>A("blank")(e,t);F.error=A("error"),F.success=A("success"),F.loading=A("loading"),F.custom=A("custom"),F.dismiss=e=>{$({type:3,toastId:e})},F.remove=e=>$({type:4,toastId:e}),F.promise=(e,t,r)=>{let a=F.loading(t.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let i=t.success?j(t.success,e):void 0;return i?F.success(i,{id:a,...r,...null==r?void 0:r.success}):F.dismiss(a),e}).catch(e=>{let i=t.error?j(t.error,e):void 0;i?F.error(i,{id:a,...r,...null==r?void 0:r.error}):F.dismiss(a)}),e};var _=(e,t)=>{$({type:1,toast:{id:e,height:t}})},L=()=>{$({type:5,time:Date.now()})},O=new Map,U=1e3,q=(e,t=U)=>{if(O.has(e))return;let r=setTimeout(()=>{O.delete(e),$({type:4,toastId:e})},t);O.set(e,r)},S=e=>{let{toasts:t,pausedAt:r}=P(e);(0,i.useEffect)(()=>{if(r)return;let e=Date.now(),a=t.map(t=>{if(t.duration===1/0)return;let r=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(r<0){t.visible&&F.dismiss(t.id);return}return setTimeout(()=>F.dismiss(t.id),r)});return()=>{a.forEach(e=>e&&clearTimeout(e))}},[t,r]);let a=(0,i.useCallback)(()=>{r&&$({type:6,time:Date.now()})},[r]),s=(0,i.useCallback)((e,r)=>{let{reverseOrder:a=!1,gutter:i=8,defaultPosition:s}=r||{},o=t.filter(t=>(t.position||s)===(e.position||s)&&t.height),n=o.findIndex(t=>t.id===e.id),l=o.filter((e,t)=>t<n&&e.visible).length;return o.filter(e=>e.visible).slice(...a?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+i,0)},[t]);return(0,i.useEffect)(()=>{t.forEach(e=>{if(e.dismissed)q(e.id,e.removeDelay);else{let t=O.get(e.id);t&&(clearTimeout(t),O.delete(e.id))}})},[t]),{toasts:t,handlers:{updateHeight:_,startPause:L,endPause:a,calculateOffset:s}}},Z=v`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,M=v`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,T=v`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,R=b("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Z} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${M} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${T} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,H=v`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,W=b("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${H} 1s linear infinite;
`,V=v`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Y=v`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,B=b("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${V} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Y} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,G=b("div")`
  position: absolute;
`,J=b("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,X=v`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Q=b("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${X} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,K=({toast:e})=>{let{icon:t,type:r,iconTheme:a}=e;return void 0!==t?"string"==typeof t?i.createElement(Q,null,t):t:"blank"===r?null:i.createElement(J,null,i.createElement(W,{...a}),"loading"!==r&&i.createElement(G,null,"error"===r?i.createElement(R,{...a}):i.createElement(B,{...a})))},ee=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,et=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,er=b("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,ea=b("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ei=(e,t)=>{let r=e.includes("top")?1:-1,[a,i]=N()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[ee(r),et(r)];return{animation:t?`${v(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${v(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},es=i.memo(({toast:e,position:t,style:r,children:a})=>{let s=e.height?ei(e.position||t||"top-center",e.visible):{opacity:0},o=i.createElement(K,{toast:e}),n=i.createElement(ea,{...e.ariaProps},j(e.message,e));return i.createElement(er,{className:e.className,style:{...s,...r,...e.style}},"function"==typeof a?a({icon:o,message:n}):i.createElement(i.Fragment,null,o,n))});a=i.createElement,c.p=void 0,g=a,x=void 0,y=void 0;var eo=({id:e,className:t,style:r,onHeightUpdate:a,children:s})=>{let o=i.useCallback(t=>{if(t){let r=()=>{a(e,t.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,a]);return i.createElement("div",{ref:o,className:t,style:r},s)},en=(e,t)=>{let r=e.includes("top"),a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:N()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...a}},el=f`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ed=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:a,children:s,containerStyle:o,containerClassName:n})=>{let{toasts:l,handlers:d}=S(r);return i.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:n,onMouseEnter:d.startPause,onMouseLeave:d.endPause},l.map(r=>{let o=r.position||t,n=en(o,d.calculateOffset(r,{reverseOrder:e,gutter:a,defaultPosition:t}));return i.createElement(eo,{id:r.id,key:r.id,onHeightUpdate:d.updateHeight,className:r.visible?el:"",style:n},"custom"===r.type?j(r.message,r):s?s(r):i.createElement(es,{toast:r,position:o}))}))}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8948,3373,9404,2481,2325,7207,8578,3283],()=>r(51507));module.exports=a})();