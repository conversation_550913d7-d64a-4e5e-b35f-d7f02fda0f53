"use strict";(()=>{var t={};t.id=6666,t.ids=[6666],t.modules={20399:t=>{t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:t=>{t.exports=require("crypto")},93690:t=>{t.exports=import("graphql-request")},52074:(t,a,e)=>{e.a(t,async(t,o)=>{try{e.r(a),e.d(a,{originalPathname:()=>v,patchFetch:()=>d,requestAsyncStorage:()=>l,routeModule:()=>u,serverHooks:()=>k,staticGenerationAsyncStorage:()=>p});var r=e(49303),s=e(88716),i=e(60670),n=e(52223),c=t([n]);n=(c.then?(await c)():c)[0];let u=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/products/validate-stock/route",pathname:"/api/products/validate-stock",filename:"route",bundlePath:"app/api/products/validate-stock/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\products\\validate-stock\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:k}=u,v="/api/products/validate-stock/route";function d(){return(0,i.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:p})}o()}catch(t){o(t)}})},52223:(t,a,e)=>{e.a(t,async(t,o)=>{try{e.r(a),e.d(a,{POST:()=>c});var r=e(87070),s=e(93690),i=e(94868),n=t([s]);s=(n.then?(await n)():n)[0];let d=process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN?new i.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN}):null,u=process.env.WOOCOMMERCE_GRAPHQL_URL||"",l=new s.GraphQLClient(u),p=(0,s.gql)`
  query GetProductsStock($ids: [ID!]!) {
    products(where: { include: $ids }) {
      nodes {
        id
        databaseId
        stockStatus
        stockQuantity
        manageStock
        ... on VariableProduct {
          variations {
            nodes {
              id
              databaseId
              stockStatus
              stockQuantity
              manageStock
            }
          }
        }
      }
    }
  }
`,k=(0,s.gql)`
  query GetVariationsStock($ids: [ID!]!) {
    productVariations(where: { include: $ids }) {
      nodes {
        id
        databaseId
        stockStatus
        stockQuantity
        manageStock
        parent {
          node {
            id
            databaseId
          }
        }
      }
    }
  }
`;async function c(t){try{let{items:a}=await t.json();if(!a||!Array.isArray(a)||0===a.length)return r.NextResponse.json({error:"Items array is required"},{status:400});let e=a.filter(t=>!t.variationId).map(t=>t.productId),o=a.filter(t=>t.variationId).map(t=>t.variationId),[s,i]=await Promise.all([e.length>0?l.request(p,{ids:e}):Promise.resolve({products:{nodes:[]}}),o.length>0?l.request(k,{ids:o}):Promise.resolve({productVariations:{nodes:[]}})]),n=new Map;s.products?.nodes?.forEach(t=>{n.set(t.databaseId.toString(),t)});let c=new Map;i.productVariations?.nodes?.forEach(t=>{c.set(t.databaseId.toString(),t)});let u=a.map(t=>{let a=t.quantity;if(t.variationId){let e=c.get(t.variationId);if(!e)return{productId:t.productId,variationId:t.variationId,available:!1,requestedQuantity:a,message:"Variation not found"};let o="IN_STOCK"===e.stockStatus||"instock"===e.stockStatus,r=!e.manageStock||null===e.stockQuantity||e.stockQuantity>=a;return{productId:t.productId,variationId:t.variationId,available:o&&r,stockStatus:e.stockStatus,stockQuantity:e.stockQuantity,requestedQuantity:a,message:o?r?"Available":`Only ${e.stockQuantity} items available for this variation`:"Product variation is out of stock"}}{let e=n.get(t.productId);if(!e)return{productId:t.productId,available:!1,requestedQuantity:a,message:"Product not found"};let o="IN_STOCK"===e.stockStatus||"instock"===e.stockStatus,r=!e.manageStock||null===e.stockQuantity||e.stockQuantity>=a;return{productId:t.productId,available:o&&r,stockStatus:e.stockStatus,stockQuantity:e.stockQuantity,requestedQuantity:a,message:o?r?"Available":`Only ${e.stockQuantity} items available`:"Product is out of stock"}}});if(d)try{let t=`stock_validation:${Date.now()}`;await d.set(t,u,30)}catch(t){console.warn("Cache write failed, continuing without cache:",t)}let v=u.every(t=>t.available),S=u.filter(t=>!t.available).length;return r.NextResponse.json({validations:u,allAvailable:v,summary:{totalItems:a.length,availableItems:a.length-S,unavailableItems:S},timestamp:new Date().toISOString()})}catch(t){return console.error("Stock validation error:",t),r.NextResponse.json({error:"Stock validation failed",details:t instanceof Error?t.message:"Unknown error"},{status:500})}}o()}catch(t){o(t)}})}};var a=require("../../../../webpack-runtime.js");a.C(t);var e=t=>a(a.s=t),o=a.X(0,[8948,5972,4766,4868],()=>e(52074));module.exports=o})();