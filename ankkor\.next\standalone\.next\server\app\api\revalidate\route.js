"use strict";(()=>{var e={};e.id=3899,e.ids=[3899],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},7674:(e,r,a)=>{a.r(r),a.d(r,{originalPathname:()=>m,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>g,staticGenerationAsyncStorage:()=>v});var t={};a.r(t),a.d(t,{GET:()=>d,POST:()=>l});var n=a(49303),s=a(88716),o=a(60670),c=a(87070),i=a(90152);async function l(e){try{let{handle:r,type:a}=await e.json();if("product"===a&&r)return await (0,i.C2)(r),c.NextResponse.json({revalidated:!0,type:"product",handle:r,timestamp:Date.now()});if("all"===a)return await (0,i.S$)(),c.NextResponse.json({revalidated:!0,type:"all",timestamp:Date.now()});return c.NextResponse.json({revalidated:!1,error:"Invalid revalidation type or missing handle",timestamp:Date.now()},{status:400})}catch(e){return console.error("Revalidation error:",e),c.NextResponse.json({revalidated:!1,error:"Revalidation failed",message:e instanceof Error?e.message:"Unknown error",timestamp:Date.now()},{status:500})}}async function d(e){let{searchParams:r}=new URL(e.url),a=r.get("handle"),t=r.get("type")||"product";try{if("product"===t&&a)return await (0,i.C2)(a),c.NextResponse.json({revalidated:!0,type:"product",handle:a,timestamp:Date.now()});if("all"===t)return await (0,i.S$)(),c.NextResponse.json({revalidated:!0,type:"all",timestamp:Date.now()});return c.NextResponse.json({revalidated:!1,error:"Invalid parameters. Use ?handle=product-handle or ?type=all",timestamp:Date.now()},{status:400})}catch(e){return console.error("Revalidation error:",e),c.NextResponse.json({revalidated:!1,error:"Revalidation failed",message:e instanceof Error?e.message:"Unknown error",timestamp:Date.now()},{status:500})}}let u=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/revalidate/route",pathname:"/api/revalidate",filename:"route",bundlePath:"app/api/revalidate/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\revalidate\\route.ts",nextConfigOutput:"standalone",userland:t}),{requestAsyncStorage:p,staticGenerationAsyncStorage:v,serverHooks:g}=u,m="/api/revalidate/route";function f(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:v})}},90152:(e,r,a)=>{a.d(r,{C2:()=>c,S$:()=>l,ck:()=>i});var t=a(24330);a(60166);var n=a(57708),s=a(71615),o=a(58585);async function c(e){try{return(0,n.revalidatePath)(`/product/${e}`),(0,n.revalidateTag)(`product-${e}`),(0,n.revalidatePath)("/collection"),(0,n.revalidateTag)("products"),{success:!0,message:`Successfully revalidated product: ${e}`}}catch(r){return console.error(`Error revalidating product ${e}:`,r),{success:!1,message:r instanceof Error?r.message:"Unknown error during revalidation"}}}async function i(){try{return(0,n.revalidatePath)("/product"),(0,n.revalidatePath)("/collection"),(0,n.revalidateTag)("products"),(0,n.revalidateTag)("inventory"),{success:!0,message:"Successfully revalidated inventory"}}catch(e){return console.error("Error revalidating inventory:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error during inventory revalidation"}}}async function l(){try{return(0,n.revalidatePath)("/","layout"),(0,n.revalidateTag)("global"),{success:!0,message:"Successfully revalidated all pages"}}catch(e){return console.error("Error revalidating all pages:",e),{success:!1,message:e instanceof Error?e.message:"Unknown error during site-wide revalidation"}}}async function d(e){try{return(0,n.revalidatePath)(`/collection/${e}`),(0,n.revalidateTag)(`collection-${e}`),(0,n.revalidatePath)("/collection"),{success:!0,message:`Successfully revalidated collection: ${e}`}}catch(r){return console.error(`Error revalidating collection ${e}:`,r),{success:!1,message:r instanceof Error?r.message:"Unknown error during collection revalidation"}}}async function u(e,r,a=2592e3){try{return(0,s.cookies)().set(e,r,{maxAge:a,path:"/",sameSite:"lax",secure:!0}),{success:!0}}catch(r){return console.error(`Error setting preference cookie ${e}:`,r),{success:!1}}}async function p(e){try{return(0,s.cookies)().delete(e),{success:!0}}catch(r){return console.error(`Error deleting preference cookie ${e}:`,r),{success:!1}}}async function v(e){(0,o.redirect)(e)}(0,a(40618).h)([c,i,l,d,u,p,v]),(0,t.j)("a50717f48f6a2815a952ed293c1ffcd237e84398",c),(0,t.j)("4aa66c79eaba83e398a49c79f548be0f77e35a74",i),(0,t.j)("7acd90ecc7717585a4e59fd620100679aac11a1c",l),(0,t.j)("d64dc9d742eb29b5e8020c257b5ce339a41cb5ec",d),(0,t.j)("67efe3eacae6c151748c818acc5c136660a35769",u),(0,t.j)("db63bd4ffc5b15cee559935b44ade40fe5ef3b60",p),(0,t.j)("dfe708f68eaa721bbf91e3563a432bf0e6758d82",v)}};var r=require("../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[8948,5972,7708,2727],()=>a(7674));module.exports=t})();