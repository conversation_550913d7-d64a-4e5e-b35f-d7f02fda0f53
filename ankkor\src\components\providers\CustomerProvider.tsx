'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

// Minimal Customer context type to fix circular dependency
interface CustomerContextType {
  customer: any | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  token: string | null;
  login: (credentials: {email: string, password: string}) => Promise<void>;
  register: (registration: {email: string, firstName: string, lastName: string, password: string}) => Promise<void>;
  logout: () => void;
  updateProfile: (data: any) => Promise<any>;
  error: string | null;
  refreshCustomer: () => Promise<void>;
}

// Create the context with default values
const CustomerContext = createContext<CustomerContextType>({
  customer: null,
  isLoading: false,
  isAuthenticated: false,
  token: null,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  updateProfile: async () => {},
  error: null,
  refreshCustomer: async () => {}
});

// Custom hook to use the customer context
export const useCustomer = () => useContext(CustomerContext);

// Minimal Customer provider component to fix circular dependency
export const CustomerProvider = ({ children }: { children: ReactNode }) => {
  const [customer, setCustomer] = useState<any | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);

  // Minimal implementations to prevent build errors
  const login = async (credentials: {email: string, password: string}) => {
    console.log('Login function called - minimal implementation');
    // TODO: Implement actual login logic after fixing circular dependency
  };

  const register = async (registration: {email: string, firstName: string, lastName: string, password: string}) => {
    console.log('Register function called - minimal implementation');
    // TODO: Implement actual register logic after fixing circular dependency
  };

  const logout = () => {
    console.log('Logout function called - minimal implementation');
    setCustomer(null);
    setToken(null);
    // TODO: Implement actual logout logic after fixing circular dependency
  };

  const updateProfile = async (data: any) => {
    console.log('Update profile function called - minimal implementation');
    // TODO: Implement actual update profile logic after fixing circular dependency
    return {};
  };

  const refreshCustomer = async () => {
    console.log('Refresh customer function called - minimal implementation');
    // TODO: Implement actual refresh customer logic after fixing circular dependency
  };

  const isAuthenticated = !!customer && !!token;

  const value: CustomerContextType = {
    customer,
    isLoading,
    isAuthenticated,
    token,
    login,
    register,
    logout,
    updateProfile,
    error,
    refreshCustomer
  };

  return (
    <CustomerContext.Provider value={value}>
      {children}
    </CustomerContext.Provider>
  );
};
