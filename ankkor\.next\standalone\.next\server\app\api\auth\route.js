"use strict";(()=>{var e={};e.id=5932,e.ids=[5932],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93690:e=>{e.exports=import("graphql-request")},31699:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{originalPathname:()=>g,patchFetch:()=>c,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>f,staticGenerationAsyncStorage:()=>p});var o=r(49303),n=r(88716),a=r(60670),i=r(81287),u=e([i]);i=(u.then?(await u)():u)[0];let l=new o.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/auth/route",pathname:"/api/auth",filename:"route",bundlePath:"app/api/auth/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\auth\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:f}=l,g="/api/auth/route";function c(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:p})}s()}catch(e){s(e)}})},71615:(e,t,r)=>{var s=r(88757);r.o(s,"cookies")&&r.d(t,{cookies:function(){return s.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return n}});let s=r(45869),o=r(6278);class n{get isEnabled(){return this._provider.isEnabled}enable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return p},draftMode:function(){return f},headers:function(){return d}});let s=r(68996),o=r(53047),n=r(92044),a=r(72934),i=r(33085),u=r(6278),c=r(45869),l=r(54580);function d(){let e="headers",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return o.HeadersAdapter.seal(new Headers({}));(0,u.trackDynamicDataAccessed)(t,e)}return(0,l.getExpectedRequestStore)(e).headers}function p(){let e="cookies",t=c.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.RequestCookiesAdapter.seal(new n.RequestCookies(new Headers({})));(0,u.trackDynamicDataAccessed)(t,e)}let r=(0,l.getExpectedRequestStore)(e),o=a.actionAsyncStorage.getStore();return(null==o?void 0:o.isAction)||(null==o?void 0:o.isAppRoute)?r.mutableCookies:r.cookies}function f(){let e=(0,l.getExpectedRequestStore)("draftMode");return new i.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return n},ReadonlyHeadersError:function(){return o}});let s=r(38238);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class n extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return s.ReflectAdapter.get(t,r,o);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);if(void 0!==a)return s.ReflectAdapter.get(t,a,o)},set(t,r,o,n){if("symbol"==typeof r)return s.ReflectAdapter.set(t,r,o,n);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return s.ReflectAdapter.set(t,i??r,o,n)},has(t,r){if("symbol"==typeof r)return s.ReflectAdapter.has(t,r);let o=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==n&&s.ReflectAdapter.has(t,n)},deleteProperty(t,r){if("symbol"==typeof r)return s.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===n||s.ReflectAdapter.deleteProperty(t,n)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new n(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return d},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return l},getModifiedCookieValues:function(){return c}});let s=r(92044),o=r(38238),n=r(45869);class a extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new a}}class i{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function c(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}function l(e,t){let r=c(t);if(0===r.length)return!1;let o=new s.ResponseCookies(e),n=o.getAll();for(let e of r)o.set(e);for(let e of n)o.set(e);return!0}class d{static wrap(e,t){let r=new s.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let a=[],i=new Set,c=()=>{let e=n.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of a){let r=new s.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case u:return a;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{c()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{c()}};default:return o.ReflectAdapter.get(e,t,r)}}})}}},81287:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{POST:()=>c});var o=r(71615),n=r(87070),a=r(93690),i=r(70591),u=e([a]);a=(u.then?(await u)():u)[0];let g="woo_auth_token",h="woo_refresh_token",m=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",y=new a.GraphQLClient(m,{headers:{"Content-Type":"application/json"}});m&&!m.startsWith("http")&&(console.warn("GraphQL endpoint URL does not start with http(s)://, adding https:// prefix"),y.setEndpoint(`https://${m}`));let k=(0,a.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,w=(0,a.gql)`
  mutation RegisterUser($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      clientMutationId
      authToken
      refreshToken
      customer {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,x=(0,a.gql)`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`;async function c(e){try{let{action:t,...r}=await e.json();switch(t){case"login":return l(r);case"register":return d(r);case"logout":return p();case"refresh":return f(r);default:return n.NextResponse.json({success:!1,message:"Invalid action"},{status:400})}}catch(e){return console.error("Auth API error:",e),n.NextResponse.json({success:!1,message:e.message||"Server error"},{status:500})}}async function l({username:e,password:t}){try{console.log(`Attempting login for user: ${e}`),console.log(`Using GraphQL endpoint: ${m}`);let r=await y.request(k,{username:e,password:t});if(console.log("Login response received:",JSON.stringify(r,null,2)),!r||!r.login||!r.login.authToken)return console.error("Login failed: No auth token returned"),n.NextResponse.json({success:!1,message:"Invalid credentials"},{status:401});let s=(0,o.cookies)(),a={httpOnly:!0,secure:!0,sameSite:"lax",path:"/",maxAge:604800};return s.set(g,r.login.authToken,a),r.login.refreshToken&&s.set(h,r.login.refreshToken,{...a,maxAge:2592e3}),console.log("Login successful, cookies set for user:",r.login.user.email),n.NextResponse.json({success:!0,user:{id:r.login.user.id,email:r.login.user.email,firstName:r.login.user.firstName,lastName:r.login.user.lastName},token:r.login.authToken})}catch(e){if(console.error("Login error:",e),e.response?.errors){let t=e.response.errors;console.error("GraphQL errors:",t);let r=t[0]?.message||"Login failed";if(r.includes("Invalid username")||r.includes("incorrect password"))return n.NextResponse.json({success:!1,message:"Invalid username or password"},{status:401});return n.NextResponse.json({success:!1,message:r},{status:401})}return n.NextResponse.json({success:!1,message:e.message||"Login failed. Please check your connection and try again."},{status:500})}}async function d(e){try{let{email:t,firstName:r,lastName:s,password:a}=e,i=await y.request(w,{input:{clientMutationId:"registerCustomer",email:t,firstName:r,lastName:s,password:a,username:t}});if(!i.registerCustomer||!i.registerCustomer.authToken)return n.NextResponse.json({success:!1,message:"Registration failed"},{status:400});let u=(0,o.cookies)(),c={httpOnly:!0,secure:!0,sameSite:"lax",path:"/",maxAge:604800};return u.set(g,i.registerCustomer.authToken,c),i.registerCustomer.refreshToken&&u.set(h,i.registerCustomer.refreshToken,{...c,maxAge:2592e3}),console.log("Registration successful, cookies set for user:",t),n.NextResponse.json({success:!0,customer:i.registerCustomer.customer,token:i.registerCustomer.authToken})}catch(e){if(console.error("Registration error:",e),e.response?.errors){let t=e.response.errors;return console.error("GraphQL errors:",t),n.NextResponse.json({success:!1,message:t[0]?.message||"Registration failed"},{status:400})}return n.NextResponse.json({success:!1,message:e.message||"Registration failed"},{status:500})}}async function p(){let e=(0,o.cookies)();return e.set(g,"",{expires:new Date(0),path:"/",secure:!0,httpOnly:!0,sameSite:"lax"}),e.set(h,"",{expires:new Date(0),path:"/",secure:!0,httpOnly:!0,sameSite:"lax"}),console.log("Logout: Auth cookies cleared"),n.NextResponse.json({success:!0,message:"Logged out successfully"})}async function f({refreshToken:e}){try{let t=(0,o.cookies)(),r=t.get(h)?.value,s=e||r;if(!s)return n.NextResponse.json({success:!1,message:"No refresh token provided"},{status:400});let a=await y.request(x,{input:{clientMutationId:"refreshToken",jwtRefreshToken:s}});if(!a.refreshJwtAuthToken||!a.refreshJwtAuthToken.authToken)return n.NextResponse.json({success:!1,message:"Failed to refresh token"},{status:400});let u=a.refreshJwtAuthToken.authToken;t.set(g,u,{httpOnly:!0,secure:!0,sameSite:"lax",path:"/",maxAge:604800});let c="unknown";try{let e=(0,i.o)(u);c=e.data?.user?.id||"unknown"}catch(e){console.error("Error decoding JWT token:",e)}return console.log("Token refreshed successfully for user ID:",c),n.NextResponse.json({success:!0})}catch(e){if(console.error("Token refresh error:",e),e.response?.errors){let t=e.response.errors;console.error("GraphQL errors:",t);let r=(0,o.cookies)();return r.delete(g),r.delete(h),n.NextResponse.json({success:!1,message:t[0]?.message||"Token refresh failed"},{status:401})}return n.NextResponse.json({success:!1,message:e.message||"Token refresh failed"},{status:500})}}s()}catch(e){s(e)}})},70591:(e,t,r)=>{r.d(t,{o:()=>o});class s extends Error{}function o(e,t){let r;if("string"!=typeof e)throw new s("Invalid token specified: must be a string");t||(t={});let o=!0===t.header?0:1,n=e.split(".")[o];if("string"!=typeof n)throw new s(`Invalid token specified: missing part #${o+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(n)}catch(e){throw new s(`Invalid token specified: invalid base64 for part #${o+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new s(`Invalid token specified: invalid json for part #${o+1} (${e.message})`)}}s.prototype.name="InvalidTokenError"}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(31699));module.exports=s})();