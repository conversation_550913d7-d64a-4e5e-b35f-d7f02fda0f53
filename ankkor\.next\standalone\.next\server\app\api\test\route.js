"use strict";(()=>{var e={};e.id=2329,e.ids=[2329],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},94914:(e,t,o)=>{o.a(e,async(e,s)=>{try{o.r(t),o.d(t,{originalPathname:()=>m,patchFetch:()=>p,requestAsyncStorage:()=>l,routeModule:()=>u,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d});var r=o(49303),n=o(88716),a=o(60670),c=o(68888),i=e([c]);c=(i.then?(await i)():i)[0];let u=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/test/route",pathname:"/api/test",filename:"route",bundlePath:"app/api/test/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\test\\route.ts",nextConfigOutput:"standalone",userland:c}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:h}=u,m="/api/test/route";function p(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}s()}catch(e){s(e)}})},68888:(e,t,o)=>{o.a(e,async(e,s)=>{try{o.r(t),o.d(t,{GET:()=>p,OPTIONS:()=>i});var r=o(87070),n=o(19910),a=e([n]);function c(e){return e.headers.set("Access-Control-Allow-Origin","*"),e.headers.set("Access-Control-Allow-Methods","GET, POST, PUT, DELETE, OPTIONS"),e.headers.set("Access-Control-Allow-Headers","Content-Type, Authorization"),e}async function i(){return c(new r.NextResponse(null,{status:200}))}async function p(){try{console.log("Testing WooCommerce connection..."),console.log("GraphQL URL:",process.env.WOOCOMMERCE_GRAPHQL_URL),console.log("WordPress URL:","https://maroon-lapwing-781450.hostingersite.com");let e=await (0,n.Xp)(),t=await (0,n.CP)(),o=r.NextResponse.json({success:!0,message:"WooCommerce API connection successful",data:{productsCount:e.nodes?.length||0,categoriesCount:t.nodes?.length||0,graphqlUrl:process.env.WOOCOMMERCE_GRAPHQL_URL,wordpressUrl:"https://maroon-lapwing-781450.hostingersite.com"}});return c(o)}catch(t){console.error("WooCommerce API test failed:",t);let e=r.NextResponse.json({success:!1,message:"WooCommerce API connection failed",error:t.message,stack:void 0},{status:500});return c(e)}}n=(a.then?(await a)():a)[0],s()}catch(e){s(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var o=e=>t(t.s=e),s=t.X(0,[8948,5972,4766,4868,9910],()=>o(94914));module.exports=s})();