exports.id=4154,exports.ids=[4154],exports.modules={13417:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},96799:(e,s,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},54039:(e,s,r)=>{Promise.resolve().then(r.bind(r,83846))},83846:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n});var t=r(10326);r(17577);var a=r(33265);let i=()=>t.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,t.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),t.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),o=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>t.jsx(i,{})});function n(){return t.jsx("div",{className:"container mx-auto py-20",children:t.jsx(o,{})})}},32457:(e,s,r)=>{"use strict";r.d(s,{default:()=>d});var t=r(10326),a=r(17577),i=r(35047),o=r(74723),n=r(75290),l=r(68897);let d=({mode:e,redirectUrl:s="/"})=>{let r=(0,i.useRouter)(),{refreshCustomer:d}=(0,l.O)(),[c,m]=(0,a.useState)(!1),[u,x]=(0,a.useState)(null),[g,h]=(0,a.useState)(null),[p,f]=(0,a.useState)(null),b="login"===e,{register:y,handleSubmit:N,watch:w,formState:{errors:j}}=(0,o.cI)({mode:"onBlur"}),v=w("password",""),P=async e=>{m(!0),x(null),h(null),f(null);try{if(b){console.log("Attempting login with:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e.email,password:e.password})}),a=await t.json();a.success?(h("Login successful! Redirecting..."),setTimeout(async()=>{await d(),r.push(s),r.refresh()},500)):x(a.message||"Login failed. Please check your credentials.")}else{console.log("Attempting registration for:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password})}),a=await t.json();a.success?(h("Registration successful! Redirecting..."),await d(),setTimeout(()=>{r.push(s),r.refresh()},1e3)):x(a.message||"Registration failed. Please try again.")}}catch(e){console.error("Authentication error:",e),x(e.message||"An error occurred during authentication"),h(null)}finally{m(!1)}};return(0,t.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[t.jsx("h2",{className:"text-2xl font-serif mb-6 text-center",children:b?"Sign In to Your Account":"Create an Account"}),u&&t.jsx("div",{className:"mb-4 p-3 bg-red-50 text-red-700 text-sm border border-red-200",children:u}),g&&t.jsx("div",{className:"mb-4 p-3 bg-green-50 text-green-700 text-sm border border-green-200",children:g}),p&&!1,(0,t.jsxs)("form",{onSubmit:N(P),className:"space-y-4",children:[!b&&t.jsx(t.Fragment,{children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),t.jsx("input",{id:"firstName",type:"text",className:`w-full p-2 border ${j.firstName?"border-red-500":"border-gray-300"}`,...y("firstName",{required:"First name is required"})}),j.firstName&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:j.firstName.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),t.jsx("input",{id:"lastName",type:"text",className:`w-full p-2 border ${j.lastName?"border-red-500":"border-gray-300"}`,...y("lastName",{required:"Last name is required"})}),j.lastName&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:j.lastName.message})]})]})}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),t.jsx("input",{id:"email",type:"email",className:`w-full p-2 border ${j.email?"border-red-500":"border-gray-300"}`,...y("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})}),j.email&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:j.email.message})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),t.jsx("input",{id:"password",type:"password",className:`w-full p-2 border ${j.password?"border-red-500":"border-gray-300"}`,...y("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}})}),j.password&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:j.password.message})]}),!b&&(0,t.jsxs)("div",{children:[t.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),t.jsx("input",{id:"confirmPassword",type:"password",className:`w-full p-2 border ${j.confirmPassword?"border-red-500":"border-gray-300"}`,...y("confirmPassword",{required:"Please confirm your password",validate:e=>e===v||"Passwords do not match"})}),j.confirmPassword&&t.jsx("p",{className:"mt-1 text-xs text-red-600",children:j.confirmPassword.message})]}),t.jsx("button",{type:"submit",disabled:c,className:"w-full bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#4c4c47] transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed",children:c?(0,t.jsxs)("span",{className:"flex items-center justify-center",children:[t.jsx(n.Z,{className:"animate-spin mr-2 h-4 w-4"}),b?"Signing in...":"Creating account..."]}):b?"Sign In":"Create Account"})]}),b?t.jsx("div",{className:"mt-4 text-center",children:t.jsx("a",{href:"/forgot-password",className:"text-sm text-[#2c2c27] hover:text-[#8a8778] underline",children:"Forgot your password?"})}):null]})}},68897:(e,s,r)=>{"use strict";r.d(s,{CustomerProvider:()=>n,O:()=>o});var t=r(10326),a=r(17577);let i=(0,a.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),o=()=>(0,a.useContext)(i),n=({children:e})=>{let[s,r]=(0,a.useState)(null),[o,n]=(0,a.useState)(!1),[l,d]=(0,a.useState)(null),[c,m]=(0,a.useState)(null),u=async e=>{console.log("Login function called - minimal implementation")},x=async e=>{console.log("Register function called - minimal implementation")},g=async e=>(console.log("Update profile function called - minimal implementation"),{}),h=async()=>{console.log("Refresh customer function called - minimal implementation")};return t.jsx(i.Provider,{value:{customer:s,isLoading:o,isAuthenticated:!!s&&!!c,token:c,login:u,register:x,logout:()=>{console.log("Logout function called - minimal implementation"),r(null),m(null)},updateProfile:g,error:l,refreshCustomer:h},children:e})}},75367:(e,s,r)=>{"use strict";r.d(s,{ToastProvider:()=>u});var t=r(10326),a=r(17577),i=r(92148),o=r(86462),n=r(54659),l=r(87888),d=r(18019),c=r(94019);let m=(0,a.createContext)(void 0);function u({children:e}){let[s,r]=(0,a.useState)([]);return(0,t.jsxs)(m.Provider,{value:{toasts:s,addToast:(e,s="info",t=3e3)=>{let a=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:a,message:e,type:s,duration:t}])},removeToast:e=>{r(s=>s.filter(s=>s.id!==e))}},children:[e,t.jsx(g,{})]})}function x({toast:e,onRemove:s}){return(0,t.jsxs)(i.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[t.jsx(()=>{switch(e.type){case"success":return t.jsx(n.Z,{className:"h-5 w-5"});case"error":return t.jsx(l.Z,{className:"h-5 w-5"});default:return t.jsx(d.Z,{className:"h-5 w-5"})}},{}),t.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),t.jsx("button",{onClick:s,className:"ml-4 text-gray-400 hover:text-gray-600",children:t.jsx(c.Z,{className:"h-4 w-4"})})]})}function g(){let{toasts:e,removeToast:s}=function(){let e=(0,a.useContext)(m);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return t.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:t.jsx(o.M,{children:e.map(e=>t.jsx(x,{toast:e,onRemove:()=>s(e.id)},e.id))})})}},51806:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>u,metadata:()=>m});var t=r(19510),a=r(10527),i=r.n(a),o=r(36822),n=r.n(o);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let m={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function u({children:e}){return t.jsx("html",{lang:"en",children:t.jsx("body",{className:`${i().variable} ${n().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:t.jsx(c,{children:t.jsx(d,{children:t.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};