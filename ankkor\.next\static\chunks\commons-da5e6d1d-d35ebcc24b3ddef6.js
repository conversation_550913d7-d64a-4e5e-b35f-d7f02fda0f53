"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7158],{50749:function(e,t,s){var r=s(57437),a=s(2265),l=s(99376),i=s(29501),n=s(15863),c=s(3371);t.default=e=>{let{mode:t,redirectUrl:s="/"}=e,o=(0,l.useRouter)(),{refreshCustomer:d}=(0,c.O)(),[m,u]=(0,a.useState)(!1),[x,h]=(0,a.useState)(null),[p,g]=(0,a.useState)(null),[f,b]=(0,a.useState)(null),j="login"===t,{register:y,handleSubmit:N,watch:w,formState:{errors:v}}=(0,i.cI)({mode:"onBlur"}),C=w("password",""),k=async e=>{u(!0),h(null),g(null),b(null);try{if(j){console.log("Attempting login with:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e.email,password:e.password})}),r=await t.json();r.success?(g("Login successful! Redirecting..."),setTimeout(async()=>{await d(),o.push(s),o.refresh()},500)):h(r.message||"Login failed. Please check your credentials.")}else{console.log("Attempting registration for:",e.email);let t=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password})}),r=await t.json();r.success?(g("Registration successful! Redirecting..."),await d(),setTimeout(()=>{o.push(s),o.refresh()},1e3)):h(r.message||"Registration failed. Please try again.")}}catch(e){console.error("Authentication error:",e),h(e.message||"An error occurred during authentication"),g(null)}finally{u(!1)}};return(0,r.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[(0,r.jsx)("h2",{className:"text-2xl font-serif mb-6 text-center",children:j?"Sign In to Your Account":"Create an Account"}),x&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 text-sm border border-red-200",children:x}),p&&(0,r.jsx)("div",{className:"mb-4 p-3 bg-green-50 text-green-700 text-sm border border-green-200",children:p}),f&&!1,(0,r.jsxs)("form",{onSubmit:N(k),className:"space-y-4",children:[!j&&(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),(0,r.jsx)("input",{id:"firstName",type:"text",className:"w-full p-2 border ".concat(v.firstName?"border-red-500":"border-gray-300"),...y("firstName",{required:"First name is required"})}),v.firstName&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.firstName.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),(0,r.jsx)("input",{id:"lastName",type:"text",className:"w-full p-2 border ".concat(v.lastName?"border-red-500":"border-gray-300"),...y("lastName",{required:"Last name is required"})}),v.lastName&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.lastName.message})]})]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,r.jsx)("input",{id:"email",type:"email",className:"w-full p-2 border ".concat(v.email?"border-red-500":"border-gray-300"),...y("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})}),v.email&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.email.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,r.jsx)("input",{id:"password",type:"password",className:"w-full p-2 border ".concat(v.password?"border-red-500":"border-gray-300"),...y("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}})}),v.password&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.password.message})]}),!j&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,r.jsx)("input",{id:"confirmPassword",type:"password",className:"w-full p-2 border ".concat(v.confirmPassword?"border-red-500":"border-gray-300"),...y("confirmPassword",{required:"Please confirm your password",validate:e=>e===C||"Passwords do not match"})}),v.confirmPassword&&(0,r.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.confirmPassword.message})]}),(0,r.jsx)("button",{type:"submit",disabled:m,className:"w-full bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#4c4c47] transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed",children:m?(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(n.Z,{className:"animate-spin mr-2 h-4 w-4"}),j?"Signing in...":"Creating account..."]}):j?"Sign In":"Create Account"})]}),j?(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)("a",{href:"/forgot-password",className:"text-sm text-[#2c2c27] hover:text-[#8a8778] underline",children:"Forgot your password?"})}):null]})}},16194:function(e,t,s){s.d(t,{default:function(){return F},j:function(){return q}});var r=s(57437),a=s(2265),l=s(87758),i=s(33145),n=s(99376),c=s(48131),o=s(43886),d=s(42449),m=s(32489),u=s(76858),x=s(63639),h=s(82431),p=s(75395),g=s(21047),f=s(99397),b=s(18930),j=s(82372),y=s(29658),N=s(70597),w=s(12381),v=s(71917),C=s(15863),k=e=>{let{onClick:t,isDisabled:s=!1,text:l="Proceed to Checkout",loadingText:i="Processing..."}=e,[n,c]=(0,a.useState)(!1),[d,m]=(0,a.useState)(null),u=async()=>{if(!s&&!n){c(!0),m(null);try{await t()}catch(e){console.error("Checkout button error:",e),m(e instanceof Error?e.message:"An error occurred"),c(!1)}}};return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsx)(o.E.button,{whileHover:s||n?{}:{scale:1.02},whileTap:s||n?{}:{scale:.98},transition:{duration:.2},className:"w-full py-3 px-4 rounded-md font-medium text-center transition-colors ".concat(s?"bg-gray-300 text-gray-500 cursor-not-allowed":n?"bg-indigo-500 text-white cursor-wait":"bg-indigo-600 text-white hover:bg-indigo-700"),onClick:u,disabled:s||n,children:n?(0,r.jsxs)("span",{className:"flex items-center justify-center",children:[(0,r.jsx)(C.Z,{className:"animate-spin h-4 w-4 mr-2"}),i]}):l}),d&&(0,r.jsx)("div",{className:"mt-2 p-2 bg-red-50 border border-red-200 rounded-md",children:(0,r.jsx)("p",{className:"text-xs text-red-600",children:d})})]})},P=s(3371);let S=e=>{var t;let{item:s,updateQuantity:a,removeFromCart:l,formatPrice:n}=e;return(0,r.jsxs)("li",{className:"flex gap-4 py-4 border-b",children:[(0,r.jsx)("div",{className:"relative h-20 w-20 bg-gray-100 flex-shrink-0",children:(null===(t=s.image)||void 0===t?void 0:t.url)&&(0,r.jsx)(i.default,{src:s.image.url,alt:s.image.altText||s.name,fill:!0,sizes:"80px",className:"object-cover",priority:!1})}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,r.jsx)("h4",{className:"text-sm font-medium line-clamp-2",children:s.name}),s.attributes&&s.attributes.length>0&&(0,r.jsx)("div",{className:"mt-1 text-xs text-gray-500",children:s.attributes.map((e,t)=>(0,r.jsxs)("span",{children:[e.name,": ",e.value,t<s.attributes.length-1?", ":""]},e.name))}),(0,r.jsx)("div",{className:"mt-1 text-sm font-medium",children:s.price&&"string"==typeof s.price&&s.price.toString().includes("₹")?s.price:"".concat(N.J6).concat(n(s.price||"0"))}),(0,r.jsxs)("div",{className:"mt-2 flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center border border-gray-300",children:[(0,r.jsx)("button",{onClick:()=>{s.quantity>1&&a(s.id,s.quantity-1)},disabled:s.quantity<=1,className:"px-2 py-1 hover:bg-gray-100 disabled:opacity-50","aria-label":"Decrease quantity",children:(0,r.jsx)(g.Z,{className:"h-3 w-3"})}),(0,r.jsx)("span",{className:"px-2 py-1 text-sm",children:s.quantity}),(0,r.jsx)("button",{onClick:()=>{a(s.id,s.quantity+1)},className:"px-2 py-1 hover:bg-gray-100","aria-label":"Increase quantity",children:(0,r.jsx)(f.Z,{className:"h-3 w-3"})})]}),(0,r.jsx)("button",{onClick:()=>{l(s.id)},className:"p-1 hover:bg-gray-100 rounded-full","aria-label":"Remove item",children:(0,r.jsx)(b.Z,{className:"h-4 w-4 text-gray-500"})})]})]})]})};var E=e=>{let{isOpen:t,toggleCart:s}=e,[i,g]=(0,a.useState)(!1),[f,b]=(0,a.useState)(null),[N,C]=(0,a.useState)(!1),[E,I]=(0,a.useState)(!1),[q,F]=(0,a.useState)({}),Z=(0,n.useRouter)(),{isAuthenticated:A,customer:T,token:O}=(0,P.O)(),R=(0,l.rY)(),{items:L,itemCount:Y,removeCartItem:z,updateCartItem:D,clearCart:_,error:J,setError:M}=R,$=(0,v.p)(),Q=e=>{try{let t="string"==typeof e?parseFloat(e):e;if(isNaN(t))return"0.00";return t.toFixed(2)}catch(e){return console.error("Error formatting price:",e),"0.00"}};(0,a.useEffect)(()=>{console.log("Cart items:",L),console.log("Cart subtotal calculation:");let e=0;L.forEach(t=>{let s=0,r=(s="string"==typeof t.price?parseFloat(t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"")):t.price)*t.quantity;console.log("Item: ".concat(t.name,", Price: ").concat(t.price,", Cleaned price: ").concat(s,", Quantity: ").concat(t.quantity,", Total: ").concat(r)),e+=r}),console.log("Manual subtotal calculation: ".concat(e)),console.log("Store subtotal calculation: ".concat(R.subtotal()))},[L,R]);let B=Q(L.reduce((e,t)=>{let s=0;return isNaN(s="string"==typeof t.price?parseFloat(t.price.replace(/[₹$€£]/g,"").trim().replace(/,/g,"")):t.price)?(console.warn("Invalid price for item ".concat(t.id,": ").concat(t.price)),e):e+s*t.quantity},0));(0,a.useEffect)(()=>{(async()=>{let e={};for(let s of L)try{if(!q[s.productId])try{let t=await j.gk(s.productId);(null==t?void 0:t.slug)?e[s.productId]=t.slug:(console.warn("Product with ID ".concat(s.productId," has no slug")),e[s.productId]="product-not-found")}catch(r){var t;console.error("Failed to load handle for product ".concat(s.productId,":"),r),e[s.productId]="product-not-found",(null===(t=r.message)||void 0===t?void 0:t.includes("No product ID was found"))&&console.warn("Product with ID ".concat(s.productId," not found in WooCommerce, but keeping in cart"))}}catch(e){console.error("Error processing product ".concat(s.productId,":"),e)}Object.keys(e).length>0&&F(t=>({...t,...e}))})()},[L,q]);let H=async(e,t)=>{C(!0);try{await D(e,t)}catch(e){console.error("Error updating quantity:",e),M(e instanceof Error?e.message:"Failed to update quantity")}finally{C(!1)}},W=async e=>{try{await z(e)}catch(e){console.error("Error removing item:",e),M(e instanceof Error?e.message:"Failed to remove item")}},G=async()=>{g(!0),b(null);try{if(0===L.length)throw Error("Your cart is empty");if(!A)throw Error("Please log in to continue with checkout");s(),Z.push("/checkout")}catch(e){console.error("Checkout error:",e),b(e instanceof Error?e.message:"An error occurred during checkout"),$.addToast(e instanceof Error?e.message:"An error occurred during checkout","error"),g(!1)}},K=async()=>{I(!0),b(null);try{await G()}catch(e){console.error("Retry error:",e),b(e instanceof Error?e.message:"Retry failed")}finally{I(!1)}},U=L.length>0;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.M,{children:t&&(0,r.jsx)(o.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:s,className:"fixed inset-0 bg-black/50 z-40","aria-hidden":"true"})}),(0,r.jsx)(c.M,{children:t&&(0,r.jsxs)(o.E.div,{initial:{x:"100%"},animate:{x:0},exit:{x:"100%"},transition:{type:"tween",ease:"easeInOut",duration:.3},className:"fixed top-0 right-0 h-full w-full max-w-md bg-white z-50 shadow-xl flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,r.jsxs)("h2",{className:"text-lg font-medium flex items-center gap-2",children:[(0,r.jsx)(d.Z,{className:"h-5 w-5"}),"Your Cart"]}),(0,r.jsx)("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-full","aria-label":"Close cart",children:(0,r.jsx)(m.Z,{className:"h-5 w-5"})})]}),(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto p-4",children:[!U&&!J&&(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center p-4",children:[(0,r.jsx)(d.Z,{className:"h-12 w-12 text-gray-300 mb-2"}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Your cart is empty"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:"Looks like you haven't added any items yet."}),(0,r.jsxs)(w.z,{onClick:s,className:"flex items-center gap-2",children:["Continue Shopping",(0,r.jsx)(u.Z,{className:"h-4 w-4"})]})]}),J&&(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full text-center p-4",children:[(0,r.jsx)(x.Z,{className:"h-12 w-12 text-red-500 mb-2"}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-1",children:"Something went wrong"}),(0,r.jsx)("p",{className:"text-gray-500 mb-4",children:J}),(0,r.jsxs)(w.z,{onClick:()=>M(null),className:"flex items-center gap-2",variant:"outline",children:[(0,r.jsx)(h.Z,{className:"h-4 w-4"}),"Try Again"]})]}),U&&(0,r.jsx)("ul",{className:"divide-y",children:L.map(e=>(0,r.jsx)(S,{item:e,updateQuantity:H,removeFromCart:W,formatPrice:Q},e.id))})]}),(0,r.jsxs)("div",{className:"border-t p-4 space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between font-medium",children:[(0,r.jsx)("span",{children:"Subtotal"}),(0,r.jsxs)("span",{children:["₹",B]})]}),(0,r.jsxs)("div",{className:"flex justify-between text-lg font-semibold",children:[(0,r.jsx)("span",{children:"Total"}),(0,r.jsxs)("span",{children:["₹",B]})]}),(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsx)(k,{onClick:G,isDisabled:!U||N,text:"Proceed to Checkout",loadingText:"Preparing Checkout..."})}),f&&(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 p-3 rounded-md",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(x.Z,{className:"h-5 w-5 text-red-500 mt-0.5 mr-2 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm text-red-700",children:f}),(0,r.jsx)("button",{onClick:K,disabled:E,className:"mt-2 text-xs flex items-center text-red-700 hover:text-red-800",children:E?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.Z,{className:"h-3 w-3 animate-spin mr-1"}),"Retrying..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.Z,{className:"h-3 w-3 mr-1"}),"Try again"]})})]})]})}),!navigator.onLine&&(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 p-3 rounded-md",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.Z,{className:"h-4 w-4 text-yellow-500 mr-2"}),(0,r.jsx)("p",{className:"text-xs text-yellow-700",children:"You appear to be offline. Please check your internet connection."})]})})]}),(0,r.jsx)("button",{onClick:_,className:"w-full text-center text-gray-500 text-sm mt-2 hover:text-gray-700",disabled:i||N,children:"Clear Cart"})]})})]})};let I=(0,a.createContext)(void 0),q=()=>{let e=(0,a.useContext)(I);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e};var F=e=>{let{children:t}=e,s=(0,l.rY)(),[i,n]=(0,a.useState)(!1),c={openCart:()=>n(!0),closeCart:()=>n(!1),toggleCart:()=>n(e=>!e),isOpen:i,itemCount:s.itemCount};return(0,r.jsxs)(I.Provider,{value:c,children:[t,(0,r.jsx)(E,{isOpen:c.isOpen,toggleCart:c.toggleCart})]})}}}]);