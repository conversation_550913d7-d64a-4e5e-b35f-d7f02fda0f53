(()=>{var e={};e.id=1599,e.ids=[1599],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},37523:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{GlobalError:()=>c.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>f,routeModule:()=>g,tree:()=>p});var a=r(8179);r(52617),r(12523);var s=r(23191),o=r(88716),i=r(37922),c=r.n(i),l=r(95231),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);r.d(t,d);var u=e([a]);a=(u.then?(await u)():u)[0];let p=["",{children:["product",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,8179)),"E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],f=["E:\\ankkorwoo\\ankkor\\src\\app\\product\\[slug]\\page.tsx"],m="/product/[slug]/page",x={require:r,loadChunk:()=>Promise.resolve()},g=new s.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/product/[slug]/page",pathname:"/product/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}});n()}catch(e){n(e)}})},72996:(e,t,r)=>{Promise.resolve().then(r.bind(r,48039))},48039:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{default:()=>x});var a=r(10326),s=r(17577),o=r(46226),i=r(92148),c=r(86806),l=r(91664),d=r(77321),u=r(11019),p=r(83855),f=r(34565),m=e([d]);d=(m.then?(await m)():m)[0];let x=({product:e})=>{let[t,r]=(0,s.useState)(0),[n,m]=(0,s.useState)(1),[x,g]=(0,s.useState)(null),[h,y]=(0,s.useState)({}),[v,b]=(0,s.useState)(!1),j=(0,c.rY)(),{openCart:N}=(0,d.j)(),{id:_,databaseId:w,name:k,description:S,shortDescription:P,price:R,regularPrice:E,onSale:O,stockStatus:T,image:C,galleryImages:A,attributes:M,type:q,variations:F}=e,U="VARIABLE"===q,I=[C?.sourceUrl?{sourceUrl:C.sourceUrl,altText:C.altText||k}:null,...A?.nodes||[]].filter(Boolean),L=(e,t)=>{if(y(r=>({...r,[e]:t})),U&&F?.nodes){let r={...h,[e]:t};if(M?.nodes?.every(e=>r[e.name])){let e=F.nodes.find(e=>e.attributes.nodes.every(e=>{let t=r[e.name];return e.value===t}));e?g(e):g(null)}}},$=async()=>{b(!0);try{let e={productId:w.toString(),quantity:n,name:k,price:x?.price||R,image:{url:I[0]?.sourceUrl||"",altText:I[0]?.altText||k}};await j.addToCart(e),N()}catch(e){console.error("Error adding product to cart:",e)}finally{b(!1)}},D="IN_STOCK"!==T,G=!U||U&&x;return a.jsx("div",{className:"container mx-auto px-4 py-12",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("div",{className:"relative aspect-square bg-[#f4f3f0] overflow-hidden",children:I[t]?.sourceUrl&&a.jsx(o.default,{src:I[t].sourceUrl,alt:I[t].altText||k,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",priority:!0,className:"object-cover"})}),I.length>1&&a.jsx("div",{className:"grid grid-cols-5 gap-2",children:I.map((e,n)=>a.jsx("button",{onClick:()=>r(n),className:`relative aspect-square bg-[#f4f3f0] ${t===n?"ring-2 ring-[#2c2c27]":""}`,children:a.jsx(o.default,{src:e.sourceUrl,alt:e.altText||`${k} - Image ${n+1}`,fill:!0,sizes:"(max-width: 768px) 20vw, 10vw",className:"object-cover"})},n))})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[a.jsx("h1",{className:"text-3xl font-serif text-[#2c2c27]",children:k}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx("span",{className:"text-xl font-medium text-[#2c2c27]",children:(x?.price||R).toString().includes("₹")||(x?.price||R).toString().includes("$")||(x?.price||R).toString().includes("€")||(x?.price||R).toString().includes("\xa3")?x?.price||R:`₹${x?.price||R}`}),O&&E&&a.jsx("span",{className:"text-sm line-through text-[#8a8778]",children:E.toString().includes("₹")||E.toString().includes("$")||E.toString().includes("€")||E.toString().includes("\xa3")?E:`₹${E}`})]}),P&&a.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:P}}),U&&M?.nodes&&a.jsx("div",{className:"space-y-4",children:M.nodes.map(e=>(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),a.jsx("div",{className:"flex flex-wrap gap-2",children:e.options.map(t=>a.jsx("button",{onClick:()=>L(e.name,t),className:`px-4 py-2 border ${h[e.name]===t?"border-[#2c2c27] bg-[#2c2c27] text-white":"border-gray-300 hover:border-[#8a8778]"}`,children:t},t))})]},e.name))}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx("span",{className:"text-[#5c5c52]",children:"Quantity:"}),(0,a.jsxs)("div",{className:"flex items-center border border-gray-300",children:[a.jsx("button",{onClick:()=>m(e=>e>1?e-1:1),disabled:n<=1,className:"px-3 py-2 hover:bg-gray-100","aria-label":"Decrease quantity",children:a.jsx(u.Z,{className:"h-4 w-4"})}),a.jsx("span",{className:"px-4 py-2 border-x border-gray-300",children:n}),a.jsx("button",{onClick:()=>m(e=>e+1),className:"px-3 py-2 hover:bg-gray-100","aria-label":"Increase quantity",children:a.jsx(p.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"text-sm",children:[a.jsx("span",{className:"font-medium",children:"Availability: "}),a.jsx("span",{className:D?"text-red-600":"text-green-600",children:D?"Out of Stock":"In Stock"})]}),(0,a.jsxs)(i.E.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:[(0,a.jsxs)(l.z,{onClick:$,disabled:D||v||!G,className:"w-full py-6 bg-[#2c2c27] text-white hover:bg-[#3c3c37] flex items-center justify-center gap-2",children:[a.jsx(f.Z,{className:"h-5 w-5"}),v?"Adding...":"Add to Cart"]}),U&&!G&&!D&&a.jsx("p",{className:"mt-2 text-sm text-red-600",children:"Please select all options to add this product to your cart"})]}),S&&(0,a.jsxs)("div",{className:"mt-12 border-t border-gray-200 pt-8",children:[a.jsx("h2",{className:"text-xl font-serif mb-4 text-[#2c2c27]",children:"Description"}),a.jsx("div",{className:"prose prose-sm text-[#5c5c52]",dangerouslySetInnerHTML:{__html:S}})]})]})]})})};n()}catch(e){n(e)}})},58585:(e,t,r)=>{"use strict";var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return o},RedirectType:function(){return n.RedirectType},notFound:function(){return a.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),a=r(16399);class s extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class o extends URLSearchParams{append(){throw new s}delete(){throw new s}set(){throw new s}sort(){throw new s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return a},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function a(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return c},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return f},getURLFromRedirectError:function(){return p},isRedirectError:function(){return u},permanentRedirect:function(){return d},redirect:function(){return l}});let a=r(54580),s=r(72934),o=r(8586),i="NEXT_REDIRECT";function c(e,t,r){void 0===r&&(r=o.RedirectStatusCode.TemporaryRedirect);let n=Error(i);n.digest=i+";"+t+";"+e+";"+r+";";let s=a.requestAsyncStorage.getStore();return s&&(n.mutableCookies=s.mutableCookies),n}function l(e,t){void 0===t&&(t="replace");let r=s.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.TemporaryRedirect)}function d(e,t){void 0===t&&(t="replace");let r=s.actionAsyncStorage.getStore();throw c(e,t,(null==r?void 0:r.isAction)?o.RedirectStatusCode.SeeOther:o.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,a]=e.digest.split(";",4),s=Number(a);return t===i&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(s)&&s in o.RedirectStatusCode}function p(e){return u(e)?e.digest.split(";",3)[2]:null}function f(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8179:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>d,generateMetadata:()=>l});var a=r(19510),s=r(58585),o=r(19910),i=r(80151),c=e([o]);async function l({params:e}){let{slug:t}=e;try{let e=await (0,o.gF)(t);if(!e)return{title:"Product Not Found | Ankkor",description:"The requested product could not be found."};return{title:`${e.name} | Ankkor`,description:e.shortDescription||e.description||"Luxury menswear from Ankkor.",openGraph:{images:e.image?[{url:e.image.sourceUrl,alt:e.name}]:[]}}}catch(e){return console.error("Error generating product metadata:",e),{title:"Product | Ankkor",description:"Luxury menswear from Ankkor."}}}async function d({params:e}){let{slug:t}=e;try{let e=await (0,o.gF)(t);return e||(0,s.notFound)(),a.jsx(i.Z,{product:e})}catch(e){console.error("Error fetching product:",e),(0,s.notFound)()}}o=(c.then?(await c)():c)[0],n()}catch(e){n(e)}})},80151:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\product\ProductDetail.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8948,805,4766,4868,1067,9910],()=>r(37523));module.exports=n})();