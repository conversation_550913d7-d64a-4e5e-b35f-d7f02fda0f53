"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[986],{92371:function(t,e,r){r.d(e,{Y:function(){return s},x:function(){return l}});var o=r(59625),i=r(89134),a=r(82372);let n={getItem:t=>{try{return localStorage.getItem(t)}catch(t){return console.error("localStorage.getItem error:",t),null}},setItem:(t,e)=>{try{localStorage.setItem(t,e)}catch(t){console.error("localStorage.setItem error:",t)}},removeItem:t=>{try{localStorage.removeItem(t)}catch(t){console.error("localStorage.removeItem error:",t)}}},c=(t,e)=>{try{if(!e||!e.lines){console.error("Invalid normalized cart data",e);return}let r=e.lines.reduce((t,e)=>t+(e.quantity||0),0),o=e.lines.map(t=>{var e;return{id:t.id,variantId:t.merchandise.id,productId:t.merchandise.product.id,title:t.merchandise.product.title,handle:t.merchandise.product.handle,image:(null===(e=t.merchandise.product.image)||void 0===e?void 0:e.url)||"",price:t.merchandise.price,quantity:t.quantity,currencyCode:t.merchandise.currencyCode}});t({items:o,subtotal:e.cost.subtotalAmount.amount,total:e.cost.totalAmount.amount,currencyCode:e.cost.totalAmount.currencyCode,itemCount:r,checkoutUrl:e.checkoutUrl,isLoading:!1})}catch(e){console.error("Error updating cart state:",e),t({items:[],subtotal:"0.00",total:"0.00",itemCount:0,isLoading:!1})}},l=(0,o.Ue)()((0,i.tJ)((t,e)=>({cartId:null,items:[],isOpen:!1,isLoading:!1,subtotal:"0.00",total:"0.00",currencyCode:"USD",itemCount:0,checkoutUrl:null,initializationInProgress:!1,initializationError:null,openCart:()=>t({isOpen:!0}),closeCart:()=>t({isOpen:!1}),toggleCart:()=>t(t=>({isOpen:!t.isOpen})),initCart:async()=>{let r=e();if(r.initializationInProgress)return console.log("Cart initialization already in progress, skipping"),null;t({isLoading:!0,initializationInProgress:!0,initializationError:null});try{if(r.cartId)try{let e=await (0,a.dv)();if(e)return t({isLoading:!1,initializationInProgress:!1}),e}catch(t){console.log("Existing cart validation failed, creating new cart")}let e=await (0,a.Bk)();if(e&&e.id)return t({cartId:e.id,checkoutUrl:e.checkoutUrl,isLoading:!1,initializationInProgress:!1}),console.log("Cart initialized with ID:",e.id),e;throw Error("Failed to create cart: No cart ID returned")}catch(e){return console.error("Failed to initialize cart:",e),t({isLoading:!1,initializationInProgress:!1,initializationError:e instanceof Error?e.message:"Unknown error initializing cart"}),null}},addItem:async r=>{t({isLoading:!0});try{if(!r.variantId)throw console.error("Cannot add item to cart: Missing variant ID",r),t({isLoading:!1}),Error("Missing variant ID for item");let o=e().cartId;if(!o){console.log("Cart not initialized, creating a new cart...");let t=await (0,a.Bk)();if(t&&t.id)console.log("New cart created:",t.id),o=t.id;else throw Error("Failed to initialize cart")}if(!o)throw Error("Failed to initialize cart: No cart ID available");console.log("Adding item to cart: ".concat(r.title," (").concat(r.variantId,"), quantity: ").concat(r.quantity));try{let e=await (0,a.Xq)(o,[{merchandiseId:r.variantId,quantity:r.quantity||1}]);if(!e)throw Error("Failed to add item to cart: No cart returned");let i=(0,a.Id)(e);c(t,i),t({isOpen:!0}),console.log("Item added to cart successfully. Cart now has ".concat(i.lines.length," items."))}catch(t){if(console.error("Shopify API error when adding to cart:",t),t instanceof Error)throw Error("Failed to add item to cart: ".concat(t.message));throw Error("Failed to add item to cart: Unknown API error")}}catch(e){throw console.error("Failed to add item to cart:",e),t({isLoading:!1}),e}},updateItem:async(r,o)=>{let i=e();t({isLoading:!0});try{if(!i.cartId)throw Error("Cart not initialized");if(console.log("Updating item in cart: ".concat(r,", new quantity: ").concat(o)),o<=0)return console.log("Quantity is ".concat(o,", removing item from cart")),e().removeItem(r);let n=await (0,a.xu)(i.cartId,[{id:r,quantity:o}]);if(!n)throw Error("Failed to update item: No cart returned");let l=(0,a.Id)(n);c(t,l),console.log("Item updated successfully. Cart now has ".concat(l.lines.length," items."))}catch(e){throw console.error("Failed to update item in cart:",e),t({isLoading:!1}),e}},removeItem:async r=>{let o=e();t({isLoading:!0});try{if(!o.cartId)throw console.error("Cannot remove item: Cart not initialized"),Error("Cart not initialized");console.log("Removing item from cart: ".concat(r));let e=[...o.items],i=e.find(t=>t.id===r);i?console.log('Removing "'.concat(i.title,'" (').concat(i.variantId,") from cart")):console.warn("Item with ID ".concat(r," not found in cart"));let n=await (0,a.h2)(o.cartId,[r]);if(!n)throw console.error("Failed to remove item: No cart returned from Shopify"),Error("Failed to remove item: No cart returned");let l=(0,a.Id)(n),s=l.lines.map(t=>({id:t.id,title:t.merchandise.product.title}));console.log("Cart before removal:",e.length,"items"),console.log("Cart after removal:",s.length,"items"),e.length===s.length&&console.warn("Item count did not change after removal operation"),c(t,l),console.log("Item removed successfully. Cart now has ".concat(l.lines.length," items."))}catch(e){throw console.error("Failed to remove item from cart:",e),t({isLoading:!1}),e}},clearCart:async()=>{e(),t({isLoading:!0});try{console.log("Clearing cart and creating a new one");let e=await (0,a.Bk)();if(!e)throw Error("Failed to create new cart");t({cartId:e.id,items:[],subtotal:"0.00",total:"0.00",itemCount:0,checkoutUrl:e.checkoutUrl,isLoading:!1}),console.log("Cart cleared successfully. New cart ID:",e.id)}catch(e){throw console.error("Failed to clear cart:",e),t({isLoading:!1}),e}}}),{name:"ankkor-cart",storage:(0,i.FL)(()=>n),version:1,partialize:t=>({cartId:t.cartId,items:t.items,subtotal:t.subtotal,total:t.total,currencyCode:t.currencyCode,itemCount:t.itemCount,checkoutUrl:t.checkoutUrl})})),s=(0,o.Ue)()((0,i.tJ)((t,e)=>({items:[],isLoading:!1,addToWishlist:e=>{t(t=>t.items.some(t=>t.id===e.id)?t:{items:[...t.items,e]})},removeFromWishlist:e=>{t(t=>({items:t.items.filter(t=>t.id!==e)}))},clearWishlist:()=>{t({items:[]})},isInWishlist:t=>e().items.some(e=>e.id===t)}),{name:"ankkor-wishlist",storage:(0,i.FL)(()=>n),partialize:t=>({items:t.items})}))},93448:function(t,e,r){r.d(e,{cn:function(){return a}});var o=r(61994),i=r(53335);function a(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];return(0,i.m6)((0,o.W)(e))}},87466:function(t,e,r){var o=r(29865),i=r(40257);new o.s({url:i.env.UPSTASH_REDIS_REST_URL||i.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:i.env.UPSTASH_REDIS_REST_TOKEN||i.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""})}}]);