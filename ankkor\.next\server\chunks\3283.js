exports.id=3283,exports.ids=[3283],exports.modules={13417:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,12994,23)),Promise.resolve().then(a.t.bind(a,96114,23)),Promise.resolve().then(a.t.bind(a,9727,23)),Promise.resolve().then(a.t.bind(a,79671,23)),Promise.resolve().then(a.t.bind(a,41868,23)),Promise.resolve().then(a.t.bind(a,84759,23))},96799:(e,t,a)=>{Promise.resolve().then(a.bind(a,68897)),Promise.resolve().then(a.bind(a,75367))},54039:(e,t,a)=>{Promise.resolve().then(a.bind(a,83846))},83846:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var r=a(10326);a(17577);var o=a(33265);let s=()=>r.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,r.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),n=(0,o.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>r.jsx(s,{})});function i(){return r.jsx("div",{className:"container mx-auto py-20",children:r.jsx(n,{})})}},68897:(e,t,a)=>{"use strict";a.d(t,{CustomerProvider:()=>i,O:()=>n});var r=a(10326),o=a(17577);let s=(0,o.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),n=()=>(0,o.useContext)(s),i=({children:e})=>{let[t,a]=(0,o.useState)(null),[n,i]=(0,o.useState)(!1),[l,d]=(0,o.useState)(null),[u,c]=(0,o.useState)(null),m=async e=>{console.log("Login function called - minimal implementation")},p=async e=>{console.log("Register function called - minimal implementation")},g=async e=>(console.log("Update profile function called - minimal implementation"),{}),y=async()=>{console.log("Refresh customer function called - minimal implementation")};return r.jsx(s.Provider,{value:{customer:t,isLoading:n,isAuthenticated:!!t&&!!u,token:u,login:m,register:p,logout:()=>{console.log("Logout function called - minimal implementation"),a(null),c(null)},updateProfile:g,error:l,refreshCustomer:y},children:e})}},75367:(e,t,a)=>{"use strict";a.d(t,{ToastProvider:()=>m});var r=a(10326),o=a(17577),s=a(92148),n=a(86462),i=a(54659),l=a(87888),d=a(18019),u=a(94019);let c=(0,o.createContext)(void 0);function m({children:e}){let[t,a]=(0,o.useState)([]);return(0,r.jsxs)(c.Provider,{value:{toasts:t,addToast:(e,t="info",r=3e3)=>{let o=Math.random().toString(36).substring(2,9);a(a=>[...a,{id:o,message:e,type:t,duration:r}])},removeToast:e=>{a(t=>t.filter(t=>t.id!==e))}},children:[e,r.jsx(g,{})]})}function p({toast:e,onRemove:t}){return(0,r.jsxs)(s.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[r.jsx(()=>{switch(e.type){case"success":return r.jsx(i.Z,{className:"h-5 w-5"});case"error":return r.jsx(l.Z,{className:"h-5 w-5"});default:return r.jsx(d.Z,{className:"h-5 w-5"})}},{}),r.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),r.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:r.jsx(u.Z,{className:"h-4 w-4"})})]})}function g(){let{toasts:e,removeToast:t}=function(){let e=(0,o.useContext)(c);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return r.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:r.jsx(n.M,{children:e.map(e=>r.jsx(p,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},53248:(e,t,a)=>{"use strict";new(a(78578)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""})},15725:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{Bk:()=>c,CP:()=>u,Dg:()=>d,Id:()=>h,ML:()=>f,Op:()=>y,Xp:()=>n,Xq:()=>p,dv:()=>m,h2:()=>g,mJ:()=>U,wv:()=>x,xu:()=>b});var o=a(93690);a(53248);var s=e([o]);o=(s.then?(await s)():s)[0];let v={storeUrl:"https://maroon-lapwing-781450.hostingersite.com",graphqlUrl:process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",apiVersion:"v1"},P=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",I=new o.GraphQLClient(P,{headers:{"Content-Type":"application/json",Accept:"application/json"}}),C=(0,o.gql)`
  fragment ProductFields on Product {
    id
    databaseId
    name
    slug
    description
    shortDescription
    type
    image {
      sourceUrl
      altText
    }
    galleryImages {
      nodes {
        sourceUrl
        altText
      }
    }
    ... on SimpleProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
    }
    ... on VariableProduct {
      price
      regularPrice
      salePrice
      onSale
      stockStatus
      stockQuantity
      attributes {
        nodes {
          name
          options
        }
      }
    }
  }
`,N=(0,o.gql)`
  fragment VariableProductWithVariations on VariableProduct {
    attributes {
      nodes {
        name
        options
      }
    }
    variations {
      nodes {
        id
        databaseId
        name
        price
        regularPrice
        salePrice
        stockStatus
        stockQuantity
        attributes {
          nodes {
            name
            value
          }
        }
      }
    }
  }
`,$=(0,o.gql)`
  query GetProducts(
    $first: Int
    $after: String
    $where: RootQueryToProductConnectionWhereArgs
  ) {
    products(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        ...ProductFields
        ... on VariableProduct {
          ...VariableProductWithVariations
        }
      }
    }
  }
  ${C}
  ${N}
`;async function n(e={}){try{return(await i($,{first:e.first||12,after:e.after||null,where:e.where||{}},["products"],60)).products}catch(e){return console.error("Error fetching products:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function i(e,t={},a=[],r=60){try{{let o={method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({query:e,variables:t}),next:{}};a&&a.length>0&&(o.next.tags=a),void 0!==r&&(o.next.revalidate=r);let s=await fetch(v.graphqlUrl,o);if(!s.ok)throw Error(`WooCommerce GraphQL API responded with status ${s.status}`);let{data:n,errors:i}=await s.json();if(i)throw console.error("GraphQL Errors:",i),Error(i[0].message);return n}}catch(e){throw console.error("Error fetching from WooCommerce:",e),e}}async function l({query:e,variables:t},a=3,r=1e3){let o=0,s=null;for(;o<a;)try{return await i(e,t,[],0)}catch(e){s=e,++o<a&&(console.log(`Retrying request (${o}/${a}) after ${r}ms`),await new Promise(e=>setTimeout(e,r)),r*=2)}throw console.error(`Failed after ${a} attempts:`,s),s}(0,o.gql)`
  query GetProductBySlug($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
    }
  }
  ${C}
  ${N}
`,(0,o.gql)`
  query GetProductBySlugWithTags($slug: ID!) {
    product(id: $slug, idType: SLUG) {
      ...ProductFields
      ... on VariableProduct {
        ...VariableProductWithVariations
      }
      productTags {
        nodes {
          id
          name
          slug
        }
      }
      productCategories {
        nodes {
          id
          name
          slug
        }
      }
    }
  }
  ${C}
  ${N}
`,(0,o.gql)`
  query GetCategories(
    $first: Int
    $after: String
    $where: RootQueryToProductCategoryConnectionWhereArgs
  ) {
    productCategories(first: $first, after: $after, where: $where) {
      pageInfo {
        hasNextPage
        endCursor
      }
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
      }
    }
  }
`;let w=(0,o.gql)`
  query GetAllProducts($first: Int = 20) {
    products(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        shortDescription
        productCategories {
          nodes {
            id
            name
            slug
          }
        }
        ... on SimpleProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          stockQuantity
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          onSale
          stockStatus
          variations {
            nodes {
              stockStatus
              stockQuantity
            }
          }
        }
        image {
          id
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            id
            sourceUrl
            altText
          }
        }
        ... on VariableProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
        }
      }
    }
  }
`,T=((0,o.gql)`
  query GetProductsByCategory($slug: ID!, $first: Int = 20) {
    productCategory(id: $slug, idType: SLUG) {
      id
      name
      slug
      description
      products(first: $first) {
        nodes {
          id
          databaseId
          name
          slug
          ... on SimpleProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          ... on VariableProduct {
            price
            regularPrice
            salePrice
            onSale
            stockStatus
          }
          image {
            id
            sourceUrl
            altText
          }
        }
      }
    }
  }
`,(0,o.gql)`
  query GetAllCategories($first: Int = 20) {
    productCategories(first: $first) {
      nodes {
        id
        databaseId
        name
        slug
        description
        count
        image {
          sourceUrl
          altText
        }
        children {
          nodes {
            id
            name
            slug
          }
        }
      }
    }
  }
`);(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
    }
  }
`,(0,o.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
        nicename
        nickname
        username
      }
    }
  }
`;let k=(0,o.gql)`
  query GetCart {
    cart {
      contents {
        nodes {
          key
          product {
            node {
              id
              databaseId
              name
              slug
              type
              image {
                sourceUrl
                altText
              }
            }
          }
          variation {
            node {
              id
              databaseId
              name
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
          quantity
          total
        }
      }
      subtotal
      total
      totalTax
      isEmpty
      contentsCount
    }
  }
`,S=(0,o.gql)`
  mutation AddToCart($productId: Int!, $variationId: Int, $quantity: Int, $extraData: String) {
    addToCart(
      input: {
        clientMutationId: "addToCart"
        productId: $productId
        variationId: $variationId
        quantity: $quantity
        extraData: $extraData
      }
    ) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
        contentsCount
      }
    }
  }
`,q=(0,o.gql)`
  mutation RemoveItemsFromCart($keys: [ID]!, $all: Boolean) {
    removeItemsFromCart(input: { keys: $keys, all: $all }) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                databaseId
                name
                slug
                type
                image {
                  sourceUrl
                  altText
                }
              }
            }
            variation {
              node {
                id
                databaseId
                name
                attributes {
                  nodes {
                    name
                    value
                  }
                }
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function d(e=20){try{let t=await l({query:w,variables:{first:e}});return t?.products?.nodes||[]}catch(e){return console.error("Error fetching all products:",e),[]}}async function u(e={}){try{let t=await l({query:T,variables:{first:e.first||20,after:e.after||null,where:e.where||{}}});return{nodes:t.productCategories.nodes,pageInfo:t.productCategories.pageInfo}}catch(e){return console.error("Error fetching categories:",e),{nodes:[],pageInfo:{hasNextPage:!1,endCursor:null}}}}async function c(e=[]){try{if(0===e.length)return{contents:{nodes:[]},subtotal:"0",total:"0",totalTax:"0",isEmpty:!0,contentsCount:0};let t=e[0],a=await p("",[t]);if(e.length>1){for(let t=1;t<e.length;t++)await p("",[e[t]]);return await m()}return a}catch(e){throw console.error("Error creating cart:",e),e}}async function m(){try{let e=await l({query:k,variables:{}});return e?.cart||null}catch(e){return console.error("Error fetching cart:",e),null}}async function p(e,t){try{if(0===t.length)throw Error("No items provided to add to cart");let e=t[0],a={productId:parseInt(e.productId),quantity:e.quantity||1,variationId:e.variationId?parseInt(e.variationId):null,extraData:null};console.log("Adding to cart with variables:",a);let r=await l({query:S,variables:a});return console.log("Add to cart response:",r),r.addToCart.cart}catch(e){throw console.error("Error adding items to cart:",e),e}}async function g(e,t){try{let e=await l({query:q,variables:{keys:t,all:!1}});return e?.removeItemsFromCart?.cart||null}catch(e){throw console.error("Error removing items from cart:",e),e}}function y(e){if(!e)return null;let t=!!e.variations?.nodes?.length,a={minVariantPrice:{amount:e.price||"0",currencyCode:"INR"},maxVariantPrice:{amount:e.price||"0",currencyCode:"INR"}};if(t&&e.variations?.nodes?.length>0){let t=e.variations.nodes.map(e=>parseFloat(e.price||"0")).filter(e=>!isNaN(e));t.length>0&&(a={minVariantPrice:{amount:String(Math.min(...t)),currencyCode:"INR"},maxVariantPrice:{amount:String(Math.max(...t)),currencyCode:"INR"}})}let r=function(e){let t=[];return e.image&&t.push({url:e.image.sourceUrl,altText:e.image.altText||e.name||""}),e.galleryImages?.nodes?.length&&e.galleryImages.nodes.forEach(a=>{e.image&&a.sourceUrl===e.image.sourceUrl||t.push({url:a.sourceUrl,altText:a.altText||e.name||""})}),t}(e),o=e.variations?.nodes?.map(e=>({id:e.id,title:e.name,price:{amount:e.price||"0",currencyCode:"USD"},availableForSale:"IN_STOCK"===e.stockStatus,selectedOptions:e.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[],sku:e.sku||"",image:e.image?{url:e.image.sourceUrl,altText:e.image.altText||""}:null}))||[],s=e.attributes?.nodes?.map(e=>({name:e.name,values:e.options||[]}))||[],n=e.productCategories?.nodes?.map(e=>({handle:e.slug,title:e.name}))||[],i={};return e.metafields&&e.metafields.forEach(e=>{i[e.key]=e.value}),{id:e.id,handle:e.slug,title:e.name,description:e.description||"",descriptionHtml:e.description||"",priceRange:a,options:s,variants:o,images:r,collections:n,availableForSale:"OUT_OF_STOCK"!==e.stockStatus,metafields:i,_originalWooProduct:e}}(0,o.gql)`
  query GetShippingMethods {
    shippingMethods {
      nodes {
        id
        title
        description
        cost
      }
    }
  }
`,(0,o.gql)`
  query GetPaymentGateways {
    paymentGateways {
      nodes {
        id
        title
        description
        enabled
      }
    }
  }
`;let U=(e,t,a,r="")=>{if(!e||!e.metafields)return r;if(a){let o=`${a}:${t}`;return e.metafields[o]||r}return e.metafields[t]||r};function h(e){if(!e)return null;let t=e.contents?.nodes?.map(e=>{let t=e.product?.node,a=e.variation?.node;return{id:e.key,quantity:e.quantity,merchandise:{id:a?.id||t?.id,title:a?.name||t?.name,product:{id:t?.id,handle:t?.slug,title:t?.name,image:t?.image?{url:t?.image.sourceUrl,altText:t?.image.altText||""}:null},selectedOptions:a?.attributes?.nodes?.map(e=>({name:e.name,value:e.value}))||[]},cost:{totalAmount:{amount:e.total||"0",currencyCode:"USD"}}}})||[],a=e.appliedCoupons?.nodes?.map(e=>({code:e.code,amount:e.discountAmount||"0"}))||[],r=t.reduce((e,t)=>e+t.quantity,0);return{id:e.id,checkoutUrl:"",totalQuantity:r,cost:{subtotalAmount:{amount:e.subtotal||"0",currencyCode:"USD"},totalAmount:{amount:e.total||"0",currencyCode:"USD"}},lines:t,discountCodes:a}}function f(e,t=!1){let a=`${v.storeUrl}/checkout`,r=e?`?cart=${e}`:"",o="";return t||(o=`${r?"&":"?"}guest_checkout=yes&checkout_woocommerce_checkout_login_reminder=0&create_account=0&skip_login=1&force_guest_checkout=1`),`${a}${r}${o}`}async function x(e){let t=(0,o.gql)`
    query GetProduct($id: ID!) {
      product(id: $id, idType: DATABASE_ID) {
        id
        databaseId
        name
        slug
        description
        shortDescription
        price
        regularPrice
        salePrice
        onSale
        stockStatus
        stockQuantity
        image {
          sourceUrl
          altText
        }
        galleryImages {
          nodes {
            sourceUrl
            altText
          }
        }
        ... on SimpleProduct {
          attributes {
            nodes {
              name
              options
            }
          }
          price
          regularPrice
          salePrice
        }
        ... on VariableProduct {
          price
          regularPrice
          salePrice
          attributes {
            nodes {
              name
              options
            }
          }
          variations {
            nodes {
              id
              databaseId
              name
              price
              regularPrice
              salePrice
              stockStatus
              attributes {
                nodes {
                  name
                  value
                }
              }
            }
          }
        }
      }
    }
  `;try{return(await I.request(t,{id:e})).product}catch(e){throw console.error("Error fetching product:",e),Error("Failed to fetch product")}}(0,o.gql)`
  mutation CreateCustomer($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
      }
      authToken
      refreshToken
    }
  }
`,(0,o.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
      customerUserErrors {
        field
        message
      }
    }
  }
`,(0,o.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      displayName
      username
      role
      date
      modified
      isPayingCustomer
      orderCount
      totalSpent
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders(first: 50) {
        nodes {
          id
          databaseId
          date
          status
          total
          subtotal
          totalTax
          shippingTotal
          discountTotal
          paymentMethodTitle
          customerNote
          billing {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
            email
            phone
          }
          shipping {
            firstName
            lastName
            company
            address1
            address2
            city
            state
            postcode
            country
          }
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                  slug
                  image {
                    sourceUrl
                    altText
                  }
                }
              }
              variation {
                node {
                  id
                  name
                  attributes {
                    nodes {
                      name
                      value
                    }
                  }
                }
              }
              quantity
              total
              subtotal
              totalTax
            }
          }
          shippingLines {
            nodes {
              methodTitle
              total
            }
          }
          feeLines {
            nodes {
              name
              total
            }
          }
          couponLines {
            nodes {
              code
              discount
            }
          }
        }
      }
      downloadableItems {
        nodes {
          name
          downloadId
          downloadsRemaining
          accessExpires
          product {
            node {
              id
              name
            }
          }
        }
      }
      metaData {
        key
        value
      }
    }
  }
`,(0,o.gql)`
  mutation CreateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation UpdateAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation DeleteAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,(0,o.gql)`
  mutation SetDefaultAddress($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      customer {
        id
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let E=(0,o.gql)`
  mutation UpdateCart($input: UpdateItemQuantitiesInput!) {
    updateItemQuantities(input: $input) {
      cart {
        contents {
          nodes {
            key
            product {
              node {
                id
                name
                price
              }
            }
            quantity
            total
          }
        }
        subtotal
        total
        totalTax
        isEmpty
      }
    }
  }
`;async function b(e){try{return(await l({query:E,variables:{input:{items:e}}})).updateItemQuantities.cart}catch(e){throw console.error("Error updating cart:",e),e}}r()}catch(e){r(e)}})},51806:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>m,metadata:()=>c});var r=a(19510),o=a(10527),s=a.n(o),n=a(36822),i=a.n(n);a(5023);var l=a(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),u=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let c={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:`${s().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:r.jsx(u,{children:r.jsx(d,{children:r.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};