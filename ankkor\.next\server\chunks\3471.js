"use strict";exports.id=3471,exports.ids=[3471],exports.modules={53471:(t,e,a)=>{a.a(t,async(t,i)=>{try{a.d(e,{Z:()=>y});var r=a(10326),o=a(17577),s=a(90434),n=a(92148),l=a(67427),c=a(75290),d=a(34565),m=a(86806),u=a(96040),h=a(68897),g=a(77321),p=a(44960),f=a(68471),x=a(40381),w=t([u]);u=(w.then?(await w)():w)[0];let v=t=>{if("number"==typeof t)return t.toString();if(!t)return"0";let e=t.toString().replace(/[^\d.-]/g,""),a=parseFloat(e);return isNaN(a)?"0":a.toString()},y=({id:t,name:e,price:a,image:i,slug:w,material:y,isNew:I=!1,stockStatus:N="IN_STOCK",compareAtPrice:b=null,regularPrice:j=null,salePrice:C=null,onSale:$=!1,currencySymbol:k=f.J6,currencyCode:S=f.EJ,shortDescription:E,type:F})=>{let[z,A]=(0,o.useState)(!1),L=(0,m.rY)(),{openCart:O}=(0,g.j)(),{addToWishlist:U,isInWishlist:D,removeFromWishlist:P}=(0,u.Y)(),{isAuthenticated:T}=(0,h.O)(),q=D(t),Z=async r=>{if(r.preventDefault(),r.stopPropagation(),!t||""===t){console.error("Cannot add to cart: Missing product ID for product",e),x.Am.error("Cannot add to cart: Invalid product");return}if(!z){A(!0),console.log(`Adding product to cart: ${e} (ID: ${t})`);try{await L.addToCart({productId:t,quantity:1,name:e,price:a,image:{url:i,altText:e}}),x.Am.success(`${e} added to cart!`),O()}catch(t){console.error(`Failed to add ${e} to cart:`,t),x.Am.error("Failed to add item to cart. Please try again.")}finally{A(!1)}}},_=r=>{r.preventDefault(),r.stopPropagation(),q?(P(t),x.Am.success("Removed from wishlist")):(U({id:t,name:e,price:v(a),image:i,handle:w,material:y||"Material not specified",variantId:t}),T?x.Am.success("Added to your wishlist"):x.Am.success("Added to wishlist (saved locally)"))},M=b&&parseFloat(b)>parseFloat(a)?Math.round((parseFloat(b)-parseFloat(a))/parseFloat(b)*100):null,R="IN_STOCK"!==N;return(0,r.jsxs)(n.E.div,{className:"group relative",whileHover:{y:-5},transition:{duration:.3},children:[(0,r.jsxs)(s.default,{href:`/product/${w}`,className:"block",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden mb-4",children:[r.jsx("div",{className:"aspect-[3/4] relative bg-[#f4f3f0] overflow-hidden",children:r.jsx(p.Z,{src:i,alt:e,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",animate:!0,className:"h-full"})}),(0,r.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[r.jsx(n.E.button,{onClick:_,className:`p-2 rounded-none ${q?"bg-[#2c2c27]":"bg-[#f8f8f5]"}`,whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":q?"Remove from wishlist":"Add to wishlist",children:r.jsx(l.Z,{className:`h-5 w-5 ${q?"text-[#f4f3f0] fill-current":"text-[#2c2c27]"}`})}),r.jsx(n.E.button,{onClick:Z,className:`p-2 rounded-none ${R||z?"bg-gray-400 cursor-not-allowed":"bg-[#2c2c27]"} text-[#f4f3f0]`,whileHover:R||z?{}:{scale:1.05},whileTap:R||z?{}:{scale:.95},"aria-label":R?"Out of stock":z?"Adding to cart...":"Add to cart",disabled:R||z,children:z?r.jsx(c.Z,{className:"h-5 w-5 animate-spin"}):r.jsx(d.Z,{className:"h-5 w-5"})})]}),I&&r.jsx("div",{className:"absolute top-0 left-0 bg-[#2c2c27] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"New"}),R&&r.jsx("div",{className:"absolute top-0 right-0 bg-red-600 text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"Out of Stock"}),!R&&M&&(0,r.jsxs)("div",{className:"absolute top-0 right-0 bg-[#8a8778] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:[M,"% Off"]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h3",{className:"font-serif text-lg text-[#2c2c27] mb-1 line-clamp-2",children:e}),y&&r.jsx("p",{className:"text-[#8a8778] text-xs",children:y}),F&&r.jsx("p",{className:"text-[#8a8778] text-xs capitalize",children:F.toLowerCase().replace("_"," ")}),E&&r.jsx("p",{className:"text-[#5c5c52] text-xs line-clamp-2",dangerouslySetInnerHTML:{__html:E.replace(/<[^>]*>/g,"")}}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 product-card-price",children:[r.jsx("p",{className:"text-[#2c2c27] font-medium",children:$&&C?C.toString().includes("₹")||C.toString().includes("$")||C.toString().includes("€")||C.toString().includes("\xa3")?C:`${k}${C}`:a.toString().includes("₹")||a.toString().includes("$")||a.toString().includes("€")||a.toString().includes("\xa3")?a:`${k}${a}`}),$&&j&&r.jsx("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:j.toString().includes("₹")||j.toString().includes("$")||j.toString().includes("€")||j.toString().includes("\xa3")?j:`${k}${j}`}),!$&&b&&parseFloat(b.toString().replace(/[₹$€£]/g,""))>parseFloat(a.toString().replace(/[₹$€£]/g,""))&&r.jsx("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:b.toString().includes("₹")||b.toString().includes("$")||b.toString().includes("€")||b.toString().includes("\xa3")?b:`${k}${b}`})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"flex items-center gap-2",children:"IN_STOCK"===N?r.jsx("span",{className:"text-green-600 text-xs font-medium",children:"✓ In Stock"}):"OUT_OF_STOCK"===N?r.jsx("span",{className:"text-red-600 text-xs font-medium",children:"✗ Out of Stock"}):"ON_BACKORDER"===N?r.jsx("span",{className:"text-orange-600 text-xs font-medium",children:"⏳ Backorder"}):r.jsx("span",{className:"text-gray-600 text-xs font-medium",children:"? Unknown"})}),$&&r.jsx("span",{className:"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium",children:"Sale"})]})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 space-y-2",children:[r.jsx(n.E.button,{onClick:Z,className:`w-full py-3 px-4 transition-all duration-200 ${R||z?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-[#2c2c27] text-[#f4f3f0] hover:bg-[#1a1a17]"}`,whileHover:R||z?{}:{scale:1.02},whileTap:R||z?{}:{scale:.98},"aria-label":R?"Out of stock":z?"Adding to cart...":"Add to cart",disabled:R||z,children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[z?r.jsx(c.Z,{className:"h-4 w-4 animate-spin"}):r.jsx(d.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"text-sm font-medium",children:R?"Out of Stock":z?"Adding...":"Add to Cart"})]})}),r.jsx(n.E.button,{onClick:_,className:`w-full py-3 px-4 border transition-all duration-200 ${q?"bg-[#2c2c27] text-[#f4f3f0] border-[#2c2c27]":"bg-transparent text-[#2c2c27] border-[#2c2c27] hover:bg-[#2c2c27] hover:text-[#f4f3f0]"}`,whileHover:{scale:1.02},whileTap:{scale:.98},"aria-label":q?"Remove from wishlist":"Add to wishlist",children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[r.jsx(l.Z,{className:`h-4 w-4 ${q?"fill-current":""}`}),r.jsx("span",{className:"text-sm font-medium",children:q?"In Wishlist":"Add to Wishlist"})]})})]})]})};i()}catch(t){i(t)}})},44960:(t,e,a)=>{a.d(e,{Z:()=>n});var i=a(10326),r=a(17577),o=a(46226),s=a(92148);let n=({src:t,alt:e,width:a,height:n,fill:l=!1,sizes:c=l?"(max-width: 768px) 100vw, 50vw":void 0,priority:d=!1,className:m="",animate:u=!0,style:h={}})=>{let[g,p]=(0,r.useState)(!0),[f,x]=(0,r.useState)(!1);return(0,i.jsxs)("div",{className:`relative overflow-hidden ${m}`,style:{minHeight:l?"100%":void 0,height:l?"100%":void 0,...h},onMouseEnter:()=>x(!0),onMouseLeave:()=>x(!1),children:[g&&i.jsx(s.E.div,{className:"absolute inset-0 bg-[#f4f3f0]",initial:{opacity:1},animate:{opacity:[.5,.8,.5],backgroundPosition:["0% 0%","100% 100%"]},transition:{opacity:{duration:1.5,repeat:1/0,ease:"easeInOut"},backgroundPosition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},style:{background:"linear-gradient(90deg, #f4f3f0, #e5e2d9, #f4f3f0)",backgroundSize:"200% 100%"}}),i.jsx(s.E.div,{className:"w-full h-full",animate:u&&f?{scale:1.05,filter:"brightness(1.1)"}:{scale:1,filter:"brightness(1)"},transition:{duration:.7,ease:"easeInOut"},children:i.jsx(o.default,{src:t,alt:e,width:a,height:n,fill:l,sizes:c,priority:d,className:`
            ${g?"opacity-0":"opacity-100"} 
            transition-opacity duration-500
            ${l?"object-cover":""}
          `,onLoad:()=>p(!1)})})]})}},68471:(t,e,a)=>{a.d(e,{EJ:()=>r,J6:()=>i});let i="₹",r="INR"},96040:(t,e,a)=>{a.a(t,async(t,i)=>{try{a.d(e,{Y:()=>m,x:()=>d});var r=a(60114),o=a(85251),s=a(15725),n=t([s]);s=(n.then?(await n)():n)[0];let l={getItem:t=>null,setItem:(t,e)=>{},removeItem:t=>{}},c=(t,e)=>{try{if(!e||!e.lines){console.error("Invalid normalized cart data",e);return}let a=e.lines.reduce((t,e)=>t+(e.quantity||0),0),i=e.lines.map(t=>({id:t.id,variantId:t.merchandise.id,productId:t.merchandise.product.id,title:t.merchandise.product.title,handle:t.merchandise.product.handle,image:t.merchandise.product.image?.url||"",price:t.merchandise.price,quantity:t.quantity,currencyCode:t.merchandise.currencyCode}));t({items:i,subtotal:e.cost.subtotalAmount.amount,total:e.cost.totalAmount.amount,currencyCode:e.cost.totalAmount.currencyCode,itemCount:a,checkoutUrl:e.checkoutUrl,isLoading:!1})}catch(e){console.error("Error updating cart state:",e),t({items:[],subtotal:"0.00",total:"0.00",itemCount:0,isLoading:!1})}},d=(0,r.Ue)()((0,o.tJ)((t,e)=>({cartId:null,items:[],isOpen:!1,isLoading:!1,subtotal:"0.00",total:"0.00",currencyCode:"USD",itemCount:0,checkoutUrl:null,initializationInProgress:!1,initializationError:null,openCart:()=>t({isOpen:!0}),closeCart:()=>t({isOpen:!1}),toggleCart:()=>t(t=>({isOpen:!t.isOpen})),initCart:async()=>{let a=e();if(a.initializationInProgress)return console.log("Cart initialization already in progress, skipping"),null;t({isLoading:!0,initializationInProgress:!0,initializationError:null});try{if(a.cartId)try{let e=await (0,s.dv)();if(e)return t({isLoading:!1,initializationInProgress:!1}),e}catch(t){console.log("Existing cart validation failed, creating new cart")}let e=await (0,s.Bk)();if(e&&e.id)return t({cartId:e.id,checkoutUrl:e.checkoutUrl,isLoading:!1,initializationInProgress:!1}),console.log("Cart initialized with ID:",e.id),e;throw Error("Failed to create cart: No cart ID returned")}catch(e){return console.error("Failed to initialize cart:",e),t({isLoading:!1,initializationInProgress:!1,initializationError:e instanceof Error?e.message:"Unknown error initializing cart"}),null}},addItem:async a=>{t({isLoading:!0});try{if(!a.variantId)throw console.error("Cannot add item to cart: Missing variant ID",a),t({isLoading:!1}),Error("Missing variant ID for item");let i=e().cartId;if(!i){console.log("Cart not initialized, creating a new cart...");let t=await (0,s.Bk)();if(t&&t.id)console.log("New cart created:",t.id),i=t.id;else throw Error("Failed to initialize cart")}if(!i)throw Error("Failed to initialize cart: No cart ID available");console.log(`Adding item to cart: ${a.title} (${a.variantId}), quantity: ${a.quantity}`);try{let e=await (0,s.Xq)(i,[{merchandiseId:a.variantId,quantity:a.quantity||1}]);if(!e)throw Error("Failed to add item to cart: No cart returned");let r=(0,s.Id)(e);c(t,r),t({isOpen:!0}),console.log(`Item added to cart successfully. Cart now has ${r.lines.length} items.`)}catch(t){if(console.error("Shopify API error when adding to cart:",t),t instanceof Error)throw Error(`Failed to add item to cart: ${t.message}`);throw Error("Failed to add item to cart: Unknown API error")}}catch(e){throw console.error("Failed to add item to cart:",e),t({isLoading:!1}),e}},updateItem:async(a,i)=>{let r=e();t({isLoading:!0});try{if(!r.cartId)throw Error("Cart not initialized");if(console.log(`Updating item in cart: ${a}, new quantity: ${i}`),i<=0)return console.log(`Quantity is ${i}, removing item from cart`),e().removeItem(a);let o=await (0,s.xu)(r.cartId,[{id:a,quantity:i}]);if(!o)throw Error("Failed to update item: No cart returned");let n=(0,s.Id)(o);c(t,n),console.log(`Item updated successfully. Cart now has ${n.lines.length} items.`)}catch(e){throw console.error("Failed to update item in cart:",e),t({isLoading:!1}),e}},removeItem:async a=>{let i=e();t({isLoading:!0});try{if(!i.cartId)throw console.error("Cannot remove item: Cart not initialized"),Error("Cart not initialized");console.log(`Removing item from cart: ${a}`);let e=[...i.items],r=e.find(t=>t.id===a);r?console.log(`Removing "${r.title}" (${r.variantId}) from cart`):console.warn(`Item with ID ${a} not found in cart`);let o=await (0,s.h2)(i.cartId,[a]);if(!o)throw console.error("Failed to remove item: No cart returned from Shopify"),Error("Failed to remove item: No cart returned");let n=(0,s.Id)(o),l=n.lines.map(t=>({id:t.id,title:t.merchandise.product.title}));console.log("Cart before removal:",e.length,"items"),console.log("Cart after removal:",l.length,"items"),e.length===l.length&&console.warn("Item count did not change after removal operation"),c(t,n),console.log(`Item removed successfully. Cart now has ${n.lines.length} items.`)}catch(e){throw console.error("Failed to remove item from cart:",e),t({isLoading:!1}),e}},clearCart:async()=>{e(),t({isLoading:!0});try{console.log("Clearing cart and creating a new one");let e=await (0,s.Bk)();if(!e)throw Error("Failed to create new cart");t({cartId:e.id,items:[],subtotal:"0.00",total:"0.00",itemCount:0,checkoutUrl:e.checkoutUrl,isLoading:!1}),console.log("Cart cleared successfully. New cart ID:",e.id)}catch(e){throw console.error("Failed to clear cart:",e),t({isLoading:!1}),e}}}),{name:"ankkor-cart",storage:(0,o.FL)(()=>l),version:1,partialize:t=>({cartId:t.cartId,items:t.items,subtotal:t.subtotal,total:t.total,currencyCode:t.currencyCode,itemCount:t.itemCount,checkoutUrl:t.checkoutUrl})})),m=(0,r.Ue)()((0,o.tJ)((t,e)=>({items:[],isLoading:!1,addToWishlist:e=>{t(t=>t.items.some(t=>t.id===e.id)?t:{items:[...t.items,e]})},removeFromWishlist:e=>{t(t=>({items:t.items.filter(t=>t.id!==e)}))},clearWishlist:()=>{t({items:[]})},isInWishlist:t=>e().items.some(e=>e.id===t)}),{name:"ankkor-wishlist",storage:(0,o.FL)(()=>l),partialize:t=>({items:t.items})}));i()}catch(t){i(t)}})}};