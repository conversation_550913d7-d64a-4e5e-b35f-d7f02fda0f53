"use strict";(()=>{var t={};t.id=167,t.ids=[167],t.modules={20399:t=>{t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:t=>{t.exports=require("crypto")},93690:t=>{t.exports=import("graphql-request")},6694:(t,a,e)=>{e.a(t,async(t,o)=>{try{e.r(a),e.d(a,{originalPathname:()=>S,patchFetch:()=>d,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>k,staticGenerationAsyncStorage:()=>l});var r=e(49303),i=e(88716),s=e(60670),n=e(64839),c=t([n]);n=(c.then?(await c)():c)[0];let u=new r.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/products/[id]/stock/route",pathname:"/api/products/[id]/stock",filename:"route",bundlePath:"app/api/products/[id]/stock/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\products\\[id]\\stock\\route.ts",nextConfigOutput:"standalone",userland:n}),{requestAsyncStorage:p,staticGenerationAsyncStorage:l,serverHooks:k}=u,S="/api/products/[id]/stock/route";function d(){return(0,s.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:l})}o()}catch(t){o(t)}})},64839:(t,a,e)=>{e.a(t,async(t,o)=>{try{e.r(a),e.d(a,{GET:()=>c,POST:()=>d});var r=e(87070),i=e(93690),s=e(94868),n=t([i]);i=(n.then?(await n)():n)[0];let u=process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN?new s.s({url:process.env.UPSTASH_REDIS_REST_URL,token:process.env.UPSTASH_REDIS_REST_TOKEN}):null,p=process.env.WOOCOMMERCE_GRAPHQL_URL||"",l=new i.GraphQLClient(p),k=(0,i.gql)`
  query GetProductStock($id: ID!, $idType: ProductIdType = DATABASE_ID) {
    product(id: $id, idType: $idType) {
      id
      databaseId
      stockStatus
      stockQuantity
      manageStock
      ... on VariableProduct {
        variations {
          nodes {
            id
            databaseId
            stockStatus
            stockQuantity
            manageStock
          }
        }
      }
    }
  }
`,S=(0,i.gql)`
  query GetVariationStock($id: ID!) {
    productVariation(id: $id, idType: DATABASE_ID) {
      id
      databaseId
      stockStatus
      stockQuantity
      manageStock
      parent {
        node {
          id
          databaseId
        }
      }
    }
  }
`;async function c(t,{params:a}){try{let e;let{searchParams:o}=new URL(t.url),i=o.get("variation_id"),s=a.id,n=null;if(u)try{let t=i?`stock:variation:${i}`:`stock:product:${s}`;if(n=await u.get(t))return console.log("Returning cached stock data for:",t),r.NextResponse.json(n)}catch(t){console.warn("Cache read failed, continuing without cache:",t)}if(i){let t=await l.request(S,{id:i});if(!t.productVariation)return r.NextResponse.json({error:"Variation not found"},{status:404});let a=t.productVariation;e={id:a.databaseId,type:"variation",stockStatus:a.stockStatus,stockQuantity:a.stockQuantity,manageStock:a.manageStock,parentId:a.parent?.node?.databaseId,lastUpdated:new Date().toISOString()}}else{let t=await l.request(k,{id:s,idType:"DATABASE_ID"});if(!t.product)return r.NextResponse.json({error:"Product not found"},{status:404});let a=t.product;e={id:a.databaseId,type:"product",stockStatus:a.stockStatus,stockQuantity:a.stockQuantity,manageStock:a.manageStock,variations:a.variations?.nodes?.map(t=>({id:t.databaseId,stockStatus:t.stockStatus,stockQuantity:t.stockQuantity,manageStock:t.manageStock})),lastUpdated:new Date().toISOString()}}if(u)try{let t=i?`stock:variation:${i}`:`stock:product:${s}`;await u.set(t,e,30)}catch(t){console.warn("Cache write failed, continuing without cache:",t)}return r.NextResponse.json(e)}catch(t){return console.error("Stock check error:",t),r.NextResponse.json({error:"Failed to check stock",details:t instanceof Error?t.message:"Unknown error"},{status:500})}}async function d(t,{params:a}){try{let{items:e}=await t.json(),o=await Promise.all(e.map(async e=>{try{let o=await c(new r.NextRequest(`${t.url}?${e.variationId?`variation_id=${e.variationId}`:""}`),{params:a}),i=await o.json();if(200!==o.status)return{productId:e.productId,variationId:e.variationId,available:!1,error:i.error};let s="IN_STOCK"===i.stockStatus||"instock"===i.stockStatus,n=!i.manageStock||null===i.stockQuantity||i.stockQuantity>=e.quantity;return{productId:e.productId,variationId:e.variationId,available:s&&n,stockStatus:i.stockStatus,stockQuantity:i.stockQuantity,requestedQuantity:e.quantity,message:s?n?"Available":`Only ${i.stockQuantity} items available`:"Product is out of stock"}}catch(t){return{productId:e.productId,variationId:e.variationId,available:!1,error:"Stock validation failed"}}}));return r.NextResponse.json({validations:o,allAvailable:o.every(t=>t.available)})}catch(t){return console.error("Bulk stock validation error:",t),r.NextResponse.json({error:"Bulk stock validation failed"},{status:500})}}o()}catch(t){o(t)}})}};var a=require("../../../../../webpack-runtime.js");a.C(t);var e=t=>a(a.s=t),o=a.X(0,[8948,5972,4766,4868],()=>e(6694));module.exports=o})();