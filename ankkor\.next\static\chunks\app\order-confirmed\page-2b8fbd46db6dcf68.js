(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1978],{43916:function(e,s,t){Promise.resolve().then(t.bind(t,84882))},84882:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return x}});var r=t(57437),a=t(2265),n=t(99376),l=t(65302),c=t(88226),i=t(44794),d=t(40340),m=t(12381);function x(){let e=(0,n.useRouter)(),s=(0,n.useSearchParams)(),[t,x]=(0,a.useState)(null);return((0,a.useEffect)(()=>{let t=s.get("id");if(!t){e.push("/");return}x(t)},[s,e]),t)?(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"mx-auto w-24 h-24 bg-green-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(l.Z,{className:"w-12 h-12 text-green-600"})})}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-serif mb-4 text-gray-900",children:"Thank You for Your Order!"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"Your order has been successfully placed and is being processed."}),(0,r.jsxs)("div",{className:"bg-gray-50 border rounded-lg p-6 mb-6",children:[(0,r.jsx)("h2",{className:"text-lg font-medium mb-2",children:"Order Details"}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Order ID:"}),(0,r.jsxs)("span",{className:"font-mono text-lg font-medium text-[#2c2c27]",children:["#",t]})]})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h3",{className:"text-xl font-medium mb-6",children:"What happens next?"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-3",children:(0,r.jsx)(c.Z,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Payment Confirmed"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Your payment has been successfully processed"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-3",children:(0,r.jsx)(i.Z,{className:"w-6 h-6 text-yellow-600"})}),(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Order Processing"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"We're preparing your items for shipment"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-3",children:(0,r.jsx)(d.Z,{className:"w-6 h-6 text-green-600"})}),(0,r.jsx)("h4",{className:"font-medium mb-2",children:"On the Way"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Your order will be shipped soon"})]})]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",children:[(0,r.jsx)("h3",{className:"font-medium mb-2",children:"Order Confirmation Email"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"We've sent an order confirmation email with your order details and tracking information. Please check your inbox and spam folder."})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(m.z,{onClick:()=>e.push("/"),className:"w-full md:w-auto bg-[#2c2c27] hover:bg-[#3c3c37] text-white px-8 py-3",children:"Continue Shopping"}),(0,r.jsx)("div",{className:"text-center",children:(0,r.jsx)("button",{onClick:()=>e.push("/account"),className:"text-[#2c2c27] hover:underline text-sm",children:"View Order History"})})]}),(0,r.jsxs)("div",{className:"mt-12 pt-8 border-t border-gray-200",children:[(0,r.jsx)("h3",{className:"font-medium mb-4",children:"Need Help?"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"If you have any questions about your order, please don't hesitate to contact us."}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Email:"})," ",(0,r.jsx)("a",{href:"mailto:<EMAIL>",className:"text-[#2c2c27] hover:underline",children:"<EMAIL>"})]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Phone:"})," ",(0,r.jsx)("a",{href:"tel:+**********",className:"text-[#2c2c27] hover:underline",children:"+91 12345 67890"})]})]})]})]})}):null}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,2532,2068,4595,3280,8790,1104,7158,7044,9482,986,4754,1744],function(){return e(e.s=43916)}),_N_E=e.O()}]);