(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9288],{47928:function(e,s,t){Promise.resolve().then(t.bind(t,27649))},27649:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return d}});var r=t(57437),a=t(2265),c=t(27648),i=t(33145),l=t(73559),n=t(33245);let o=e=>{let{title:s,headers:t,rows:a,units:c}=e;return(0,r.jsxs)("div",{className:"mb-12",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-xl font-serif font-bold text-[#2c2c27]",children:s}),(0,r.jsxs)("span",{className:"text-sm text-[#8a8778]",children:["Measurements in ",c]})]}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full border-collapse",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"bg-[#f4f3f0]",children:[(0,r.jsx)("th",{className:"border border-[#e5e2d9] py-3 px-4 text-left text-[#2c2c27] font-medium",children:"Size"}),t.map((e,s)=>(0,r.jsx)("th",{className:"border border-[#e5e2d9] py-3 px-4 text-left text-[#2c2c27] font-medium",children:e},s))]})}),(0,r.jsx)("tbody",{children:a.map((e,s)=>(0,r.jsxs)("tr",{className:s%2==0?"bg-white":"bg-[#faf9f6]",children:[(0,r.jsx)("td",{className:"border border-[#e5e2d9] py-3 px-4 font-medium text-[#2c2c27]",children:e.size}),e.measurements.map((e,s)=>(0,r.jsx)("td",{className:"border border-[#e5e2d9] py-3 px-4 text-[#5c5c52]",children:e},s))]},s))})]})})]})},m=e=>{let{title:s,description:t,image:a}=e;return(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-6 mb-8 items-center",children:[(0,r.jsx)("div",{className:"md:w-1/3 relative h-[200px] w-full",children:(0,r.jsx)(i.default,{src:a,alt:s,fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"})}),(0,r.jsxs)("div",{className:"md:w-2/3",children:[(0,r.jsx)("h4",{className:"text-lg font-serif font-bold mb-2 text-[#2c2c27]",children:s}),(0,r.jsx)("p",{className:"text-[#5c5c52]",children:t})]})]})};function d(){let[e,s]=(0,a.useState)("inches"),t={headers:["Chest","Waist","Sleeve Length","Shoulder Width","Neck"],rows:[{size:"XS",measurements:["34-36","28-30","32","17","14.5"]},{size:"S",measurements:["36-38","30-32","33","17.5","15"]},{size:"M",measurements:["38-40","32-34","34","18","15.5"]},{size:"L",measurements:["40-42","34-36","35","18.5","16"]},{size:"XL",measurements:["42-44","36-38","36","19","16.5"]},{size:"XXL",measurements:["44-46","38-40","37","19.5","17"]}]},i={headers:["Chest","Waist","Sleeve Length","Shoulder Width","Neck"],rows:[{size:"XS",measurements:["86-91","71-76","81","43","37"]},{size:"S",measurements:["91-97","76-81","84","44","38"]},{size:"M",measurements:["97-102","81-86","86","46","39"]},{size:"L",measurements:["102-107","86-91","89","47","41"]},{size:"XL",measurements:["107-112","91-97","91","48","42"]},{size:"XXL",measurements:["112-117","97-102","94","50","43"]}]};return(0,r.jsx)("div",{className:"min-h-screen bg-[#f8f8f5] py-12",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"mb-8 text-sm text-[#8a8778]",children:[(0,r.jsx)(c.default,{href:"/",className:"hover:text-[#2c2c27] transition-colors",children:"Home"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)(c.default,{href:"/customer-service",className:"hover:text-[#2c2c27] transition-colors",children:"Customer Service"}),(0,r.jsx)("span",{className:"mx-2",children:"/"}),(0,r.jsx)("span",{className:"text-[#2c2c27]",children:"Size Guide"})]}),(0,r.jsxs)("div",{className:"text-center max-w-3xl mx-auto mb-16",children:[(0,r.jsx)("h1",{className:"text-4xl font-serif font-bold mb-6 text-[#2c2c27]",children:"Size Guide"}),(0,r.jsx)("p",{className:"text-[#5c5c52] leading-relaxed",children:"Find your perfect fit with our comprehensive size charts. If you're between sizes, we recommend sizing up for a more comfortable fit or contacting our customer service team for personalized assistance."})]}),(0,r.jsx)("div",{className:"flex justify-center mb-12",children:(0,r.jsxs)("div",{className:"inline-flex border border-[#e5e2d9] rounded-none overflow-hidden",children:[(0,r.jsx)("button",{onClick:()=>s("inches"),className:"px-6 py-2 text-sm ".concat("inches"===e?"bg-[#2c2c27] text-[#f4f3f0]":"bg-[#f4f3f0] text-[#2c2c27] hover:bg-[#e5e2d9]"," transition-colors"),children:"Inches"}),(0,r.jsx)("button",{onClick:()=>s("cm"),className:"px-6 py-2 text-sm ".concat("cm"===e?"bg-[#2c2c27] text-[#f4f3f0]":"bg-[#f4f3f0] text-[#2c2c27] hover:bg-[#e5e2d9]"," transition-colors"),children:"Centimeters"})]})}),(0,r.jsx)("div",{className:"mb-16",children:(0,r.jsx)(o,{title:"Shirts",headers:"inches"===e?t.headers:i.headers,rows:"inches"===e?t.rows:i.rows,units:e})}),(0,r.jsxs)("div",{className:"bg-[#f4f3f0] p-8 border border-[#e5e2d9] mb-16",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)(l.Z,{className:"h-5 w-5 text-[#8a8778]"}),(0,r.jsx)("h2",{className:"text-2xl font-serif font-bold text-[#2c2c27]",children:"How to Measure"})]}),(0,r.jsx)("p",{className:"text-[#5c5c52]",children:"For the most accurate measurements, we recommend having someone else measure you. Wear lightweight clothing and stand straight with your feet together."})]}),(0,r.jsx)("div",{className:"space-y-10",children:[{title:"Chest",description:"Measure around the fullest part of your chest, keeping the tape measure horizontal and under your arms.",image:"https://images.unsplash.com/photo-1594938298603-c8148c4dae35?q=80"},{title:"Waist",description:"Measure around your natural waistline, which is located above your hip bones and below your ribcage. Keep the tape measure snug but not tight.",image:"https://images.unsplash.com/photo-1598032895397-b9472444bf93?q=80"},{title:"Sleeve Length",description:"Measure from the center back of your neck, across your shoulder, and down to your wrist. Keep your arm slightly bent.",image:"https://images.unsplash.com/photo-1594938298613-c9546b6f6c51?q=80"},{title:"Inseam",description:"Measure from the crotch seam to the bottom of the leg. For the most accurate measurement, use a pair of pants that fit you well.",image:"https://images.unsplash.com/photo-1584865288642-42078afe6942?q=80"}].map((e,s)=>(0,r.jsx)(m,{title:e.title,description:e.description,image:e.image},s))})]}),(0,r.jsxs)("div",{className:"bg-white p-8 border border-[#e5e2d9] mb-16",children:[(0,r.jsxs)("div",{className:"flex items-start gap-3 mb-6",children:[(0,r.jsx)("div",{className:"text-[#8a8778] mt-1",children:(0,r.jsx)(n.Z,{className:"h-5 w-5"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Fit Notes"}),(0,r.jsx)("p",{className:"text-[#5c5c52] mb-4",children:"At Ankkor, we offer the following fits across our collection:"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-serif font-bold mb-2 text-[#2c2c27]",children:"Classic Fit"}),(0,r.jsx)("p",{className:"text-[#5c5c52]",children:"Our most generous fit, designed for comfort with a relaxed silhouette. Classic fit shirts have a fuller cut through the chest and waist."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-serif font-bold mb-2 text-[#2c2c27]",children:"Tailored Fit"}),(0,r.jsx)("p",{className:"text-[#5c5c52]",children:"A refined silhouette that's trimmer than our Classic fit but not overly slim. Tailored fit offers a clean, modern profile without being restrictive."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-serif font-bold mb-2 text-[#2c2c27]",children:"Slim Fit"}),(0,r.jsx)("p",{className:"text-[#5c5c52]",children:"Our most fitted silhouette, cut close to the body for a contemporary look. Slim fit shirts are narrower through the chest and waist with higher armholes."})]})]})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h2",{className:"text-2xl font-serif font-bold mb-4 text-[#2c2c27]",children:"Need Additional Assistance?"}),(0,r.jsx)("p",{className:"text-[#5c5c52] mb-6 max-w-2xl mx-auto",children:"If you have any questions about sizing or need personalized recommendations, our customer service team is here to help."}),(0,r.jsx)(c.default,{href:"/customer-service/contact",className:"inline-block bg-[#2c2c27] text-[#f4f3f0] px-8 py-3 hover:bg-[#3d3d35] transition-colors text-sm tracking-wider uppercase font-medium",children:"Contact Us"})]})]})})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,4595,3280,8790,1104,7158,7044,9482,986,4754,1744],function(){return e(e.s=47928)}),_N_E=e.O()}]);