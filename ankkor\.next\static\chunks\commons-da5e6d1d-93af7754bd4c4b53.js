"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7158],{50749:function(e,s,r){var a=r(57437),t=r(2265),i=r(99376),l=r(29501),o=r(15863),d=r(3371);s.default=e=>{let{mode:s,redirectUrl:r="/"}=e,n=(0,i.useRouter)(),{refreshCustomer:m}=(0,d.O)(),[c,u]=(0,t.useState)(!1),[x,h]=(0,t.useState)(null),[g,p]=(0,t.useState)(null),[b,f]=(0,t.useState)(null),w="login"===s,{register:N,handleSubmit:j,watch:y,formState:{errors:v}}=(0,l.cI)({mode:"onBlur"}),P=y("password",""),S=async e=>{u(!0),h(null),p(null),f(null);try{if(w){console.log("Attempting login with:",e.email);let s=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e.email,password:e.password})}),a=await s.json();a.success?(p("Login successful! Redirecting..."),setTimeout(async()=>{await m(),n.push(r),n.refresh()},500)):h(a.message||"Login failed. Please check your credentials.")}else{console.log("Attempting registration for:",e.email);let s=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e.email,firstName:e.firstName,lastName:e.lastName,password:e.password})}),a=await s.json();a.success?(p("Registration successful! Redirecting..."),await m(),setTimeout(()=>{n.push(r),n.refresh()},1e3)):h(a.message||"Registration failed. Please try again.")}}catch(e){console.error("Authentication error:",e),h(e.message||"An error occurred during authentication"),p(null)}finally{u(!1)}};return(0,a.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[(0,a.jsx)("h2",{className:"text-2xl font-serif mb-6 text-center",children:w?"Sign In to Your Account":"Create an Account"}),x&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-red-50 text-red-700 text-sm border border-red-200",children:x}),g&&(0,a.jsx)("div",{className:"mb-4 p-3 bg-green-50 text-green-700 text-sm border border-green-200",children:g}),b&&!1,(0,a.jsxs)("form",{onSubmit:j(S),className:"space-y-4",children:[!w&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),(0,a.jsx)("input",{id:"firstName",type:"text",className:"w-full p-2 border ".concat(v.firstName?"border-red-500":"border-gray-300"),...N("firstName",{required:"First name is required"})}),v.firstName&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.firstName.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),(0,a.jsx)("input",{id:"lastName",type:"text",className:"w-full p-2 border ".concat(v.lastName?"border-red-500":"border-gray-300"),...N("lastName",{required:"Last name is required"})}),v.lastName&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.lastName.message})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),(0,a.jsx)("input",{id:"email",type:"email",className:"w-full p-2 border ".concat(v.email?"border-red-500":"border-gray-300"),...N("email",{required:"Email is required",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"Invalid email address"}})}),v.email&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.email.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-1",children:"Password"}),(0,a.jsx)("input",{id:"password",type:"password",className:"w-full p-2 border ".concat(v.password?"border-red-500":"border-gray-300"),...N("password",{required:"Password is required",minLength:{value:8,message:"Password must be at least 8 characters"}})}),v.password&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.password.message})]}),!w&&(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-1",children:"Confirm Password"}),(0,a.jsx)("input",{id:"confirmPassword",type:"password",className:"w-full p-2 border ".concat(v.confirmPassword?"border-red-500":"border-gray-300"),...N("confirmPassword",{required:"Please confirm your password",validate:e=>e===P||"Passwords do not match"})}),v.confirmPassword&&(0,a.jsx)("p",{className:"mt-1 text-xs text-red-600",children:v.confirmPassword.message})]}),(0,a.jsx)("button",{type:"submit",disabled:c,className:"w-full bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#4c4c47] transition-colors duration-300 disabled:bg-gray-400 disabled:cursor-not-allowed",children:c?(0,a.jsxs)("span",{className:"flex items-center justify-center",children:[(0,a.jsx)(o.Z,{className:"animate-spin mr-2 h-4 w-4"}),w?"Signing in...":"Creating account..."]}):w?"Sign In":"Create Account"})]}),w?(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsx)("a",{href:"/forgot-password",className:"text-sm text-[#2c2c27] hover:text-[#8a8778] underline",children:"Forgot your password?"})}):null]})}},16194:function(e,s,r){r.d(s,{jD:function(){return i}}),r(57437);var a=r(2265);r(87758),r(33145),r(99376),r(82372),r(29658),r(12381),r(71917),r(3371);let t=(0,a.createContext)(void 0),i=()=>{let e=(0,a.useContext)(t);if(void 0===e)throw Error("useCart must be used within a CartProvider");return e}}}]);