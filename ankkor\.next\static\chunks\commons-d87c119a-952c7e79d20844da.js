"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7044],{62670:function(e,t,a){var n=a(57437),s=a(2265),i=a(27648),r=a(43886),o=a(88997),l=a(15863),c=a(42449),d=a(87758),u=a(92371),f=a(3371),m=a(16194),x=a(57152),p=a(70597),h=a(11738);let g=e=>{if("number"==typeof e)return e.toString();if(!e)return"0";let t=parseFloat(e.toString().replace(/[^\d.-]/g,""));return isNaN(t)?"0":t.toString()};t.Z=e=>{let{id:t,name:a,price:v,image:b,slug:y,material:w,isNew:j=!1,stockStatus:N="IN_STOCK",compareAtPrice:k=null,regularPrice:S=null,salePrice:C=null,onSale:A=!1,currencySymbol:T=p.J6,currencyCode:O=p.EJ,shortDescription:E,type:I}=e,[_,Z]=(0,s.useState)(!1),D=(0,d.rY)(),{openCart:F}=(0,m.jD)(),{addToWishlist:z,isInWishlist:P,removeFromWishlist:R}=(0,u.Y)(),{isAuthenticated:L}=(0,f.O)(),M=P(t),H=async e=>{if(e.preventDefault(),e.stopPropagation(),!t||""===t){console.error("Cannot add to cart: Missing product ID for product",a),h.Am.error("Cannot add to cart: Invalid product");return}if(!_){Z(!0),console.log("Adding product to cart: ".concat(a," (ID: ").concat(t,")"));try{await D.addToCart({productId:t,quantity:1,name:a,price:v,image:{url:b,altText:a}}),h.Am.success("".concat(a," added to cart!")),F()}catch(e){console.error("Failed to add ".concat(a," to cart:"),e),h.Am.error("Failed to add item to cart. Please try again.")}finally{Z(!1)}}},$=e=>{e.preventDefault(),e.stopPropagation(),M?(R(t),h.Am.success("Removed from wishlist")):(z({id:t,name:a,price:g(v),image:b,handle:y,material:w||"Material not specified",variantId:t}),L?h.Am.success("Added to your wishlist"):h.Am.success("Added to wishlist (saved locally)"))},K=k&&parseFloat(k)>parseFloat(v)?Math.round((parseFloat(k)-parseFloat(v))/parseFloat(k)*100):null,U="IN_STOCK"!==N;return(0,n.jsxs)(r.E.div,{className:"group relative",whileHover:{y:-5},transition:{duration:.3},children:[(0,n.jsxs)(i.default,{href:"/product/".concat(y),className:"block",children:[(0,n.jsxs)("div",{className:"relative overflow-hidden mb-4",children:[(0,n.jsx)("div",{className:"aspect-[3/4] relative bg-[#f4f3f0] overflow-hidden",children:(0,n.jsx)(x.Z,{src:b,alt:a,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",animate:!0,className:"h-full"})}),(0,n.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4 flex justify-between opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:[(0,n.jsx)(r.E.button,{onClick:$,className:"p-2 rounded-none ".concat(M?"bg-[#2c2c27]":"bg-[#f8f8f5]"),whileHover:{scale:1.05},whileTap:{scale:.95},"aria-label":M?"Remove from wishlist":"Add to wishlist",children:(0,n.jsx)(o.Z,{className:"h-5 w-5 ".concat(M?"text-[#f4f3f0] fill-current":"text-[#2c2c27]")})}),(0,n.jsx)(r.E.button,{onClick:H,className:"p-2 rounded-none ".concat(U||_?"bg-gray-400 cursor-not-allowed":"bg-[#2c2c27]"," text-[#f4f3f0]"),whileHover:U||_?{}:{scale:1.05},whileTap:U||_?{}:{scale:.95},"aria-label":U?"Out of stock":_?"Adding to cart...":"Add to cart",disabled:U||_,children:_?(0,n.jsx)(l.Z,{className:"h-5 w-5 animate-spin"}):(0,n.jsx)(c.Z,{className:"h-5 w-5"})})]}),j&&(0,n.jsx)("div",{className:"absolute top-0 left-0 bg-[#2c2c27] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"New"}),U&&(0,n.jsx)("div",{className:"absolute top-0 right-0 bg-red-600 text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:"Out of Stock"}),!U&&K&&(0,n.jsxs)("div",{className:"absolute top-0 right-0 bg-[#8a8778] text-[#f4f3f0] py-1 px-3 text-xs uppercase tracking-wider",children:[K,"% Off"]})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h3",{className:"font-serif text-lg text-[#2c2c27] mb-1 line-clamp-2",children:a}),w&&(0,n.jsx)("p",{className:"text-[#8a8778] text-xs",children:w}),I&&(0,n.jsx)("p",{className:"text-[#8a8778] text-xs capitalize",children:I.toLowerCase().replace("_"," ")}),E&&(0,n.jsx)("p",{className:"text-[#5c5c52] text-xs line-clamp-2",dangerouslySetInnerHTML:{__html:E.replace(/<[^>]*>/g,"")}}),(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 product-card-price",children:[(0,n.jsx)("p",{className:"text-[#2c2c27] font-medium",children:A&&C?C.toString().includes("₹")||C.toString().includes("$")||C.toString().includes("€")||C.toString().includes("\xa3")?C:"".concat(T).concat(C):v.toString().includes("₹")||v.toString().includes("$")||v.toString().includes("€")||v.toString().includes("\xa3")?v:"".concat(T).concat(v)}),A&&S&&(0,n.jsx)("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:S.toString().includes("₹")||S.toString().includes("$")||S.toString().includes("€")||S.toString().includes("\xa3")?S:"".concat(T).concat(S)}),!A&&k&&parseFloat(k.toString().replace(/[₹$€£]/g,""))>parseFloat(v.toString().replace(/[₹$€£]/g,""))&&(0,n.jsx)("p",{className:"text-[#8a8778] text-xs line-through product-card-compare-price",children:k.toString().includes("₹")||k.toString().includes("$")||k.toString().includes("€")||k.toString().includes("\xa3")?k:"".concat(T).concat(k)})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{className:"flex items-center gap-2",children:"IN_STOCK"===N?(0,n.jsx)("span",{className:"text-green-600 text-xs font-medium",children:"✓ In Stock"}):"OUT_OF_STOCK"===N?(0,n.jsx)("span",{className:"text-red-600 text-xs font-medium",children:"✗ Out of Stock"}):"ON_BACKORDER"===N?(0,n.jsx)("span",{className:"text-orange-600 text-xs font-medium",children:"⏳ Backorder"}):(0,n.jsx)("span",{className:"text-gray-600 text-xs font-medium",children:"? Unknown"})}),A&&(0,n.jsx)("span",{className:"bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full font-medium",children:"Sale"})]})]})]})]}),(0,n.jsxs)("div",{className:"mt-4 space-y-2",children:[(0,n.jsx)(r.E.button,{onClick:H,className:"w-full py-3 px-4 transition-all duration-200 ".concat(U||_?"bg-gray-400 text-gray-600 cursor-not-allowed":"bg-[#2c2c27] text-[#f4f3f0] hover:bg-[#1a1a17]"),whileHover:U||_?{}:{scale:1.02},whileTap:U||_?{}:{scale:.98},"aria-label":U?"Out of stock":_?"Adding to cart...":"Add to cart",disabled:U||_,children:(0,n.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[_?(0,n.jsx)(l.Z,{className:"h-4 w-4 animate-spin"}):(0,n.jsx)(c.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"text-sm font-medium",children:U?"Out of Stock":_?"Adding...":"Add to Cart"})]})}),(0,n.jsx)(r.E.button,{onClick:$,className:"w-full py-3 px-4 border transition-all duration-200 ".concat(M?"bg-[#2c2c27] text-[#f4f3f0] border-[#2c2c27]":"bg-transparent text-[#2c2c27] border-[#2c2c27] hover:bg-[#2c2c27] hover:text-[#f4f3f0]"),whileHover:{scale:1.02},whileTap:{scale:.98},"aria-label":M?"Remove from wishlist":"Add to wishlist",children:(0,n.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,n.jsx)(o.Z,{className:"h-4 w-4 ".concat(M?"fill-current":"")}),(0,n.jsx)("span",{className:"text-sm font-medium",children:M?"In Wishlist":"Add to Wishlist"})]})})]})]})}},3371:function(e,t,a){a.d(t,{CustomerProvider:function(){return o},O:function(){return r}});var n=a(57437),s=a(2265);let i=(0,s.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),r=()=>(0,s.useContext)(i),o=e=>{let{children:t}=e,[a,r]=(0,s.useState)(null),[o,l]=(0,s.useState)(!1),[c,d]=(0,s.useState)(null),[u,f]=(0,s.useState)(null),m=async e=>{console.log("Login function called - minimal implementation")},x=async e=>{console.log("Register function called - minimal implementation")},p=async e=>(console.log("Update profile function called - minimal implementation"),{}),h=async()=>{console.log("Refresh customer function called - minimal implementation")};return(0,n.jsx)(i.Provider,{value:{customer:a,isLoading:o,isAuthenticated:!!a&&!!u,token:u,login:m,register:x,logout:()=>{console.log("Logout function called - minimal implementation"),r(null),f(null)},updateProfile:p,error:c,refreshCustomer:h},children:t})}},57152:function(e,t,a){var n=a(57437),s=a(2265),i=a(33145),r=a(43886);t.Z=e=>{let{src:t,alt:a,width:o,height:l,fill:c=!1,sizes:d=c?"(max-width: 768px) 100vw, 50vw":void 0,priority:u=!1,className:f="",animate:m=!0,style:x={}}=e,[p,h]=(0,s.useState)(!0),[g,v]=(0,s.useState)(!1);return(0,n.jsxs)("div",{className:"relative overflow-hidden ".concat(f),style:{minHeight:c?"100%":void 0,height:c?"100%":void 0,...x},onMouseEnter:()=>v(!0),onMouseLeave:()=>v(!1),children:[p&&(0,n.jsx)(r.E.div,{className:"absolute inset-0 bg-[#f4f3f0]",initial:{opacity:1},animate:{opacity:[.5,.8,.5],backgroundPosition:["0% 0%","100% 100%"]},transition:{opacity:{duration:1.5,repeat:1/0,ease:"easeInOut"},backgroundPosition:{duration:1.5,repeat:1/0,ease:"easeInOut"}},style:{background:"linear-gradient(90deg, #f4f3f0, #e5e2d9, #f4f3f0)",backgroundSize:"200% 100%"}}),(0,n.jsx)(r.E.div,{className:"w-full h-full",animate:m&&g?{scale:1.05,filter:"brightness(1.1)"}:{scale:1,filter:"brightness(1)"},transition:{duration:.7,ease:"easeInOut"},children:(0,n.jsx)(i.default,{src:t,alt:a,width:o,height:l,fill:c,sizes:d,priority:u,className:"\n            ".concat(p?"opacity-0":"opacity-100"," \n            transition-opacity duration-500\n            ").concat(c?"object-cover":"","\n          "),onLoad:()=>h(!1)})})]})}},12381:function(e,t,a){a.d(t,{z:function(){return l}});var n=a(57437);a(2265);var s=a(37053),i=a(90535),r=a(93448);let o=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:i,asChild:l=!1,...c}=e,d=l?s.g7:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,r.cn)(o({variant:a,size:i,className:t})),...c})}},40279:function(e,t,a){a.d(t,{I:function(){return r}});var n=a(57437),s=a(2265),i=a(93448);let r=s.forwardRef((e,t)=>{let{className:a,type:s,...r}=e;return(0,n.jsx)("input",{type:s,"data-slot":"input",className:(0,i.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",a),ref:t,...r})});r.displayName="Input"},29658:function(e,t,a){var n=a(57437);a(2265),t.Z=e=>{let{size:t="md",color:a="#2c2c27",className:s=""}=e,i={sm:{container:"w-6 h-6",dot:"w-1 h-1"},md:{container:"w-10 h-10",dot:"w-1.5 h-1.5"},lg:{container:"w-16 h-16",dot:"w-2 h-2"}};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{children:"\n        @keyframes loaderRotate {\n          0% {\n            transform: rotate(0deg);\n          }\n          100% {\n            transform: rotate(360deg);\n          }\n        }\n        \n        @keyframes loaderDot1 {\n          0%, 100% {\n            opacity: 0.2;\n          }\n          25% {\n            opacity: 1;\n          }\n        }\n        \n        @keyframes loaderDot2 {\n          0%, 100% {\n            opacity: 0.2;\n          }\n          50% {\n            opacity: 1;\n          }\n        }\n        \n        @keyframes loaderDot3 {\n          0%, 100% {\n            opacity: 0.2;\n          }\n          75% {\n            opacity: 1;\n          }\n        }\n        \n        @keyframes loaderDot4 {\n          0%, 100% {\n            opacity: 1;\n          }\n          50% {\n            opacity: 0.2;\n          }\n        }\n      "}),(0,n.jsx)("div",{className:"flex items-center justify-center ".concat(s),children:(0,n.jsxs)("div",{className:"relative ".concat(i[t].container),children:[(0,n.jsx)("div",{className:"absolute top-0 left-1/2 -translate-x-1/2 ".concat(i[t].dot," rounded-full"),style:{backgroundColor:a,animation:"loaderDot1 1.5s infinite"}}),(0,n.jsx)("div",{className:"absolute top-1/2 right-0 -translate-y-1/2 ".concat(i[t].dot," rounded-full"),style:{backgroundColor:a,animation:"loaderDot2 1.5s infinite"}}),(0,n.jsx)("div",{className:"absolute bottom-0 left-1/2 -translate-x-1/2 ".concat(i[t].dot," rounded-full"),style:{backgroundColor:a,animation:"loaderDot3 1.5s infinite"}}),(0,n.jsx)("div",{className:"absolute top-1/2 left-0 -translate-y-1/2 ".concat(i[t].dot," rounded-full"),style:{backgroundColor:a,animation:"loaderDot4 1.5s infinite"}}),(0,n.jsx)("div",{className:"absolute inset-0 rounded-full",style:{border:"2px solid ".concat(a),borderTopColor:"transparent",animation:"loaderRotate 1s linear infinite"}})]})})]})}},71917:function(e,t,a){a.d(t,{ToastProvider:function(){return f}});var n=a(57437),s=a(2265),i=a(43886),r=a(48131),o=a(65302),l=a(22252),c=a(33245),d=a(32489);let u=(0,s.createContext)(void 0);function f(e){let{children:t}=e,[a,i]=(0,s.useState)([]);return(0,n.jsxs)(u.Provider,{value:{toasts:a,addToast:function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"info",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3e3,n=Math.random().toString(36).substring(2,9);i(s=>[...s,{id:n,message:e,type:t,duration:a}])},removeToast:e=>{i(t=>t.filter(t=>t.id!==e))}},children:[t,(0,n.jsx)(x,{})]})}function m(e){let{toast:t,onRemove:a}=e;return(0,s.useEffect)(()=>{if(t.duration){let e=setTimeout(()=>{a()},t.duration);return()=>clearTimeout(e)}},[t.duration,a]),(0,n.jsxs)(i.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:"flex items-center p-4 rounded-lg border shadow-lg ".concat((()=>{switch(t.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()," max-w-md"),children:[(0,n.jsx)(()=>{switch(t.type){case"success":return(0,n.jsx)(o.Z,{className:"h-5 w-5"});case"error":return(0,n.jsx)(l.Z,{className:"h-5 w-5"});default:return(0,n.jsx)(c.Z,{className:"h-5 w-5"})}},{}),(0,n.jsx)("span",{className:"ml-3 text-sm font-medium flex-1",children:t.message}),(0,n.jsx)("button",{onClick:a,className:"ml-4 text-gray-400 hover:text-gray-600",children:(0,n.jsx)(d.Z,{className:"h-4 w-4"})})]})}function x(){let{toasts:e,removeToast:t}=function(){let e=(0,s.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return(0,n.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:(0,n.jsx)(r.M,{children:e.map(e=>(0,n.jsx)(m,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},66196:function(e,t,a){a.d(t,{Z:function(){return r}});var n=a(2265);a(57437),a(99376);let s=(0,n.createContext)({isLoading:!1,setLoading:()=>{},variant:"thread",setVariant:()=>{}}),i=()=>(0,n.useContext)(s);var r=function(e,t){let{setLoading:a,setVariant:s}=i();(0,n.useEffect)(()=>{a(e),t&&s(t)},[e,t,a,s])}}}]);