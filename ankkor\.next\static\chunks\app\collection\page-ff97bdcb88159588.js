(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1306],{18360:function(e,t,i){Promise.resolve().then(i.bind(i,49092))},49092:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return x}});var r=i(57437),c=i(2265),o=i(33145),l=i(90740),s=i(32489),a=i(62670),n=i(82372),d=i(3697);let u=[{id:"all",name:"All Categories"}],m=[{id:"featured",name:"Featured"},{id:"newest",name:"Newest"},{id:"price-asc",name:"Price: Low to High"},{id:"price-desc",name:"Price: High to Low"},{id:"rating",name:"Alphabetical"}];function x(){var e;let[t,i]=(0,c.useState)([]),[x,p]=(0,c.useState)(!0),[v,h]=(0,c.useState)(null),[f,g]=(0,c.useState)("all"),[b,j]=(0,c.useState)("featured"),[N,w]=(0,c.useState)(!1);(0,d.Z)(x,"fabric"),(0,c.useEffect)(()=>{(async()=>{try{p(!0);let e=await (0,n.Dg)();if(!e||0===e.length){h("No products found. Please check your WooCommerce store configuration."),p(!1);return}let t=e.map(e=>{var t,i,r,c,o,l,s,a;let d=(0,n.Iz)(e),u=[];try{(null===(a=e.variants)||void 0===a?void 0:a.edges)?u=e.variants.edges.map(e=>{var t,i,r;return{id:e.node.id,title:e.node.title,price:null===(t=e.node.price)||void 0===t?void 0:t.amount,compareAtPrice:null===(i=e.node.compareAtPrice)||void 0===i?void 0:i.amount,currencyCode:null===(r=e.node.price)||void 0===r?void 0:r.currencyCode}}):Array.isArray(e.variants)&&(u=e.variants)}catch(t){console.error("Error normalizing variants for product ".concat(e.title,":"),t)}return{id:e.id,title:e.title||"Untitled Product",handle:e.handle||"",price:(null===(i=e.priceRange)||void 0===i?void 0:null===(t=i.minVariantPrice)||void 0===t?void 0:t.amount)||(null===(r=u[0])||void 0===r?void 0:r.price)||"0.00",images:d,variants:u,metafields:e.metafields||{},productType:e.productType||"",tags:Array.isArray(e.tags)?e.tags:[],vendor:e.vendor||"",compareAtPrice:(null===(c=u[0])||void 0===c?void 0:c.compareAtPrice)||null,currencyCode:(null===(l=e.priceRange)||void 0===l?void 0:null===(o=l.minVariantPrice)||void 0===o?void 0:o.currencyCode)||(null===(s=u[0])||void 0===s?void 0:s.currencyCode)||"INR"}});i(t),console.log("Successfully fetched ".concat(t.length," products from WooCommerce"))}catch(e){console.error("Error fetching products:",e),h("Failed to load products from WooCommerce")}finally{p(!1)}})()},[]);let y=e=>{var t,i;let r=(null===(t=e.productType)||void 0===t?void 0:t.toLowerCase())||"",c=(null===(i=e.tags)||void 0===i?void 0:i.map(e=>e.toLowerCase()))||[];return r.includes("shirt")||c.some(e=>e.includes("shirt"))?"shirts":r.includes("polo")||c.some(e=>e.includes("polo"))?"polos":"other"},P=[..."all"===f?t:t.filter(e=>y(e)===f)].sort((e,t)=>{switch(b){case"price-asc":return parseFloat(e.price)-parseFloat(t.price);case"price-desc":return parseFloat(t.price)-parseFloat(e.price);case"rating":return e.title.localeCompare(t.title);case"newest":return t.id.localeCompare(e.id);default:return 0}});return(0,r.jsxs)("div",{className:"min-h-screen bg-[#f8f8f5] pt-8 pb-24",children:[(0,r.jsx)("div",{className:"container mx-auto px-4 mb-12",children:(0,r.jsxs)("div",{className:"text-center max-w-3xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-4xl font-serif font-bold mb-4 text-[#2c2c27]",children:"The Collection"}),(0,r.jsx)("p",{className:"text-[#5c5c52] mb-8",children:"Discover our curated selection of timeless menswear essentials, crafted with exceptional materials and meticulous attention to detail."})]})}),(0,r.jsxs)("div",{className:"relative h-[300px] mb-16 overflow-hidden",children:[(0,r.jsx)(o.default,{src:"https://images.unsplash.com/photo-1441984904996-e0b6ba687e04?q=80",alt:"Ankkor Collection",fill:!0,sizes:"(max-width: 768px) 100vw, 50vw",className:"object-cover image-animate"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-[#2c2c27] bg-opacity-30 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center text-white",children:[(0,r.jsx)("h2",{className:"text-3xl font-serif font-bold mb-4",children:"Spring/Summer 2025"}),(0,r.jsx)("p",{className:"text-lg max-w-xl mx-auto",children:"Timeless elegance for the modern gentleman"})]})})]}),(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[v&&(0,r.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 mb-8 rounded",children:[(0,r.jsx)("p",{children:v}),(0,r.jsx)("p",{className:"text-sm mt-2",children:"Please check your WooCommerce configuration in the .env.local file."})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8 md:hidden",children:[(0,r.jsxs)("button",{onClick:()=>w(!0),className:"flex items-center gap-2 text-[#2c2c27] border border-[#e5e2d9] px-4 py-2",children:[(0,r.jsx)(l.Z,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Filter & Sort"})]}),(0,r.jsxs)("div",{className:"text-[#5c5c52] text-sm",children:[P.length," products"]})]}),N&&(0,r.jsxs)("div",{className:"fixed inset-0 z-50 md:hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:()=>w(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 top-0 bottom-0 w-80 bg-[#f8f8f5] p-6 overflow-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:"font-serif text-lg text-[#2c2c27]",children:"Filter & Sort"}),(0,r.jsx)("button",{onClick:()=>w(!1),children:(0,r.jsx)(s.Z,{className:"h-5 w-5 text-[#2c2c27]"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-[#8a8778] text-xs uppercase tracking-wider mb-4",children:"Sort By"}),(0,r.jsx)("div",{className:"space-y-3",children:m.map(e=>(0,r.jsx)("button",{onClick:()=>j(e.id),className:"block w-full text-left py-1 ".concat(b===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52]"),children:e.name},e.id))})]}),(0,r.jsx)("button",{onClick:()=>w(!1),className:"w-full bg-[#2c2c27] text-[#f4f3f0] py-3 mt-8 text-sm uppercase tracking-wider",children:"Apply Filters"})]})]}),(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-10",children:[(0,r.jsx)("div",{className:"hidden md:block w-64 shrink-0",children:(0,r.jsx)("div",{className:"sticky top-24",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-[#2c2c27] font-serif text-lg mb-6",children:"Sort By"}),(0,r.jsx)("div",{className:"space-y-3",children:m.map(e=>(0,r.jsx)("button",{onClick:()=>j(e.id),className:"block w-full text-left py-1 ".concat(b===e.id?"text-[#2c2c27] font-medium":"text-[#5c5c52] hover:text-[#2c2c27] transition-colors"),children:e.name},e.id))})]})})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"hidden md:flex justify-between items-center mb-8",children:[(0,r.jsx)("h2",{className:"text-[#2c2c27] font-serif text-xl",children:"all"===f?"All Products":null===(e=u.find(e=>e.id===f))||void 0===e?void 0:e.name}),(0,r.jsxs)("div",{className:"text-[#5c5c52]",children:[P.length," products"]})]}),(0,r.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:P.map(e=>{let t="";try{var i,c,o,l,s,n,d,u,m;if(e.variants&&e.variants.length>0){let i=e.variants[0];if(i&&i.id&&!(t=i.id).startsWith("gid://woocommerce/ProductVariant/")){let i=t.replace(/\D/g,"");i?t="gid://woocommerce/ProductVariant/".concat(i):(console.warn("Cannot parse variant ID for product ".concat(e.title,": ").concat(t)),t="")}}if(!t&&e.id&&e.id.includes("/")){let i=e.id.split("/"),r=i[i.length-1];r&&/^\d+$/.test(r)&&(t="gid://woocommerce/ProductVariant/".concat(r,"1"),console.warn("Using fallback variant ID for product ".concat(e.title,": ").concat(t)))}return(0,r.jsx)(a.Z,{id:e.id,name:e.title,slug:e.handle,price:(null===(i=e._originalWooProduct)||void 0===i?void 0:i.salePrice)||(null===(c=e._originalWooProduct)||void 0===c?void 0:c.price)||e.price,compareAtPrice:e.compareAtPrice,currencyCode:e.currencyCode,image:(null===(o=e.images[0])||void 0===o?void 0:o.url)||"",stockStatus:(null===(l=e._originalWooProduct)||void 0===l?void 0:l.stockStatus)||"IN_STOCK",regularPrice:null===(s=e._originalWooProduct)||void 0===s?void 0:s.regularPrice,salePrice:null===(n=e._originalWooProduct)||void 0===n?void 0:n.salePrice,onSale:(null===(d=e._originalWooProduct)||void 0===d?void 0:d.onSale)||!1,shortDescription:null===(u=e._originalWooProduct)||void 0===u?void 0:u.shortDescription,type:null===(m=e._originalWooProduct)||void 0===m?void 0:m.type},e.id)}catch(t){return console.error("Error processing product ".concat(e.title||"unknown",":"),t),null}})})]})]})]})]})}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,4595,3280,8790,1104,7158,7044,9482,986,4754,1744],function(){return e(e.s=18360)}),_N_E=e.O()}]);