(()=>{var e={};e.id=9346,e.ids=[9346],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93690:e=>{"use strict";e.exports=import("graphql-request")},42291:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,originalPathname:()=>p,pages:()=>m,routeModule:()=>x,tree:()=>f});var n=r(36147);r(51806),r(12523);var a=r(23191),i=r(88716),o=r(37922),l=r.n(o),d=r(95231),c={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);r.d(t,c);var u=e([n]);n=(u.then?(await u)():u)[0];let f=["",{children:["account",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36147)),"E:\\ankkorwoo\\ankkor\\src\\app\\account\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],m=["E:\\ankkorwoo\\ankkor\\src\\app\\account\\page.tsx"],p="/account/page",h={require:r,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/account/page",pathname:"/account",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:f}});s()}catch(e){s(e)}})},13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},96799:(e,t,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},12448:(e,t,r)=>{Promise.resolve().then(r.bind(r,35063))},48705:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(76557).Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},35047:(e,t,r)=>{"use strict";var s=r(77389);r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(10326);r(17577);var n=r(33265);let a=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),i=(0,n.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(a,{})});function o(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(i,{})})}},35063:(e,t,r)=>{"use strict";r.d(t,{default:()=>eg});var s=r(10326),n=r(17577),a=r.t(n,2),i=r(35047),o=r(92148);function l(e,t,{checkForDefaultPrevented:r=!0}={}){return function(s){if(e?.(s),!1===r||!s.defaultPrevented)return t?.(s)}}function d(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let s=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:s}}),[r,s])}};return a.scopeName=e,[function(t,a){let i=n.createContext(a),o=r.length;r=[...r,a];let l=t=>{let{scope:r,children:a,...l}=t,d=r?.[e]?.[o]||i,c=n.useMemo(()=>l,Object.values(l));return(0,s.jsx)(d.Provider,{value:c,children:a})};return l.displayName=t+"Provider",[l,function(r,s){let l=s?.[e]?.[o]||i,d=n.useContext(l);if(d)return d;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let s=r.reduce((t,{useScope:r,scopeName:s})=>{let n=r(e)[`__scope${s}`];return{...t,...n}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:s}),[s])}};return r.scopeName=t.scopeName,r}(a,...t)]}var c=r(48051),u=r(34214),f=globalThis?.document?n.useLayoutEffect:()=>{},m=a["useId".toString()]||(()=>void 0),p=0;function h(e){let[t,r]=n.useState(m());return f(()=>{e||r(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}var x=r(45226);function g(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}function v({prop:e,defaultProp:t,onChange:r=()=>{}}){let[s,a]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[s]=r,a=n.useRef(s),i=g(t);return n.useEffect(()=>{a.current!==s&&(i(s),a.current=s)},[s,a,i]),r}({defaultProp:t,onChange:r}),i=void 0!==e,o=i?e:s,l=g(r);return[o,n.useCallback(t=>{if(i){let r="function"==typeof t?t(e):t;r!==e&&l(r)}else a(t)},[i,e,a,l])]}var b=n.createContext(void 0);function y(e){let t=n.useContext(b);return e||t||"ltr"}var j="rovingFocusGroup.onEntryFocus",N={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[k,R,P]=function(e){let t=e+"CollectionProvider",[r,a]=d(t),[i,o]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,a=n.useRef(null),o=n.useRef(new Map).current;return(0,s.jsx)(i,{scope:t,itemMap:o,collectionRef:a,children:r})};l.displayName=t;let f=e+"CollectionSlot",m=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,a=o(f,r),i=(0,c.e)(t,a.collectionRef);return(0,s.jsx)(u.g7,{ref:i,children:n})});m.displayName=f;let p=e+"CollectionItemSlot",h="data-radix-collection-item",x=n.forwardRef((e,t)=>{let{scope:r,children:a,...i}=e,l=n.useRef(null),d=(0,c.e)(t,l),f=o(p,r);return n.useEffect(()=>(f.itemMap.set(l,{ref:l,...i}),()=>void f.itemMap.delete(l))),(0,s.jsx)(u.g7,{[h]:"",ref:d,children:a})});return x.displayName=p,[{Provider:l,Slot:m,ItemSlot:x},function(t){let r=o(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${h}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}(w),[C,S]=d(w,[P]),[A,_]=C(w),O=n.forwardRef((e,t)=>(0,s.jsx)(k.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(k.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,s.jsx)(E,{...e,ref:t})})}));O.displayName=w;var E=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:i=!1,dir:o,currentTabStopId:d,defaultCurrentTabStopId:u,onCurrentTabStopIdChange:f,onEntryFocus:m,preventScrollOnEntryFocus:p=!1,...h}=e,b=n.useRef(null),w=(0,c.e)(t,b),k=y(o),[P=null,C]=v({prop:d,defaultProp:u,onChange:f}),[S,_]=n.useState(!1),O=g(m),E=R(r),M=n.useRef(!1),[T,I]=n.useState(0);return n.useEffect(()=>{let e=b.current;if(e)return e.addEventListener(j,O),()=>e.removeEventListener(j,O)},[O]),(0,s.jsx)(A,{scope:r,orientation:a,dir:k,loop:i,currentTabStopId:P,onItemFocus:n.useCallback(e=>C(e),[C]),onItemShiftTab:n.useCallback(()=>_(!0),[]),onFocusableItemAdd:n.useCallback(()=>I(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>I(e=>e-1),[]),children:(0,s.jsx)(x.WV.div,{tabIndex:S||0===T?-1:0,"data-orientation":a,...h,ref:w,style:{outline:"none",...e.style},onMouseDown:l(e.onMouseDown,()=>{M.current=!0}),onFocus:l(e.onFocus,e=>{let t=!M.current;if(e.target===e.currentTarget&&t&&!S){let t=new CustomEvent(j,N);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=E().filter(e=>e.focusable);F([e.find(e=>e.active),e.find(e=>e.id===P),...e].filter(Boolean).map(e=>e.ref.current),p)}}M.current=!1}),onBlur:l(e.onBlur,()=>_(!1))})})}),M="RovingFocusGroupItem",T=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:i=!1,tabStopId:o,...d}=e,c=h(),u=o||c,f=_(M,r),m=f.currentTabStopId===u,p=R(r),{onFocusableItemAdd:g,onFocusableItemRemove:v}=f;return n.useEffect(()=>{if(a)return g(),()=>v()},[a,g,v]),(0,s.jsx)(k.ItemSlot,{scope:r,id:u,focusable:a,active:i,children:(0,s.jsx)(x.WV.span,{tabIndex:m?0:-1,"data-orientation":f.orientation,...d,ref:t,onMouseDown:l(e.onMouseDown,e=>{a?f.onItemFocus(u):e.preventDefault()}),onFocus:l(e.onFocus,()=>f.onItemFocus(u)),onKeyDown:l(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var s;let n=(s=e.key,"rtl"!==r?s:"ArrowLeft"===s?"ArrowRight":"ArrowRight"===s?"ArrowLeft":s);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(n))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(n)))return I[n]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=p().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let s=r.indexOf(e.currentTarget);r=f.loop?function(e,t){return e.map((r,s)=>e[(t+s)%e.length])}(r,s+1):r.slice(s+1)}setTimeout(()=>F(r))}})})})});T.displayName=M;var I={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function F(e,t=!1){let r=document.activeElement;for(let s of e)if(s===r||(s.focus({preventScroll:t}),document.activeElement!==r))return}var D=e=>{let{present:t,children:r}=e,s=function(e){var t,r;let[s,a]=n.useState(),i=n.useRef({}),o=n.useRef(e),l=n.useRef("none"),[d,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=$(i.current);l.current="mounted"===d?e:"none"},[d]),f(()=>{let t=i.current,r=o.current;if(r!==e){let s=l.current,n=$(t);e?c("MOUNT"):"none"===n||t?.display==="none"?c("UNMOUNT"):r&&s!==n?c("ANIMATION_OUT"):c("UNMOUNT"),o.current=e}},[e,c]),f(()=>{if(s){let e;let t=s.ownerDocument.defaultView??window,r=r=>{let n=$(i.current).includes(r.animationName);if(r.target===s&&n&&(c("ANIMATION_END"),!o.current)){let r=s.style.animationFillMode;s.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===s.style.animationFillMode&&(s.style.animationFillMode=r)})}},n=e=>{e.target===s&&(l.current=$(i.current))};return s.addEventListener("animationstart",n),s.addEventListener("animationcancel",r),s.addEventListener("animationend",r),()=>{t.clearTimeout(e),s.removeEventListener("animationstart",n),s.removeEventListener("animationcancel",r),s.removeEventListener("animationend",r)}}c("ANIMATION_END")},[s,c]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(i.current=getComputedStyle(e)),a(e)},[])}}(t),a="function"==typeof r?r({present:s.isPresent}):n.Children.only(r),i=(0,c.e)(s.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||s.isPresent?n.cloneElement(a,{ref:i}):null};function $(e){return e?.animationName||"none"}D.displayName="Presence";var L="Tabs",[q,U]=d(L,[S]),z=S(),[G,V]=q(L),H=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:a,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:d="automatic",...c}=e,u=y(l),[f,m]=v({prop:n,onChange:a,defaultProp:i});return(0,s.jsx)(G,{scope:r,baseId:h(),value:f,onValueChange:m,orientation:o,dir:u,activationMode:d,children:(0,s.jsx)(x.WV.div,{dir:u,"data-orientation":o,...c,ref:t})})});H.displayName=L;var W="TabsList",Z=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...a}=e,i=V(W,r),o=z(r);return(0,s.jsx)(O,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:n,children:(0,s.jsx)(x.WV.div,{role:"tablist","aria-orientation":i.orientation,...a,ref:t})})});Z.displayName=W;var K="TabsTrigger",B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...i}=e,o=V(K,r),d=z(r),c=X(o.baseId,n),u=Y(o.baseId,n),f=n===o.value;return(0,s.jsx)(T,{asChild:!0,...d,focusable:!a,active:f,children:(0,s.jsx)(x.WV.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":u,"data-state":f?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:c,...i,ref:t,onMouseDown:l(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(n)}),onKeyDown:l(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(n)}),onFocus:l(e.onFocus,()=>{let e="manual"!==o.activationMode;f||a||!e||o.onValueChange(n)})})})});B.displayName=K;var J="TabsContent",Q=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:i,children:o,...l}=e,d=V(J,r),c=X(d.baseId,a),u=Y(d.baseId,a),f=a===d.value,m=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.jsx)(D,{present:i||f,children:({present:r})=>(0,s.jsx)(x.WV.div,{"data-state":f?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!r,id:u,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:r&&o})})});function X(e,t){return`${e}-trigger-${t}`}function Y(e,t){return`${e}-content-${t}`}Q.displayName=J;var ee=r(51223);let et=n.forwardRef(({className:e,...t},r)=>s.jsx(Z,{ref:r,className:(0,ee.cn)("inline-flex h-10 items-center justify-center rounded-md p-1 text-[#5c5c52]",e),...t}));et.displayName=Z.displayName;let er=n.forwardRef(({className:e,...t},r)=>s.jsx(B,{ref:r,className:(0,ee.cn)("inline-flex items-center justify-center whitespace-nowrap px-3 py-1.5 text-sm font-medium ring-offset-white transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#8a8778] focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-b-2 data-[state=active]:border-[#2c2c27] data-[state=active]:text-[#2c2c27] data-[state=active]:font-semibold",e),...t}));er.displayName=B.displayName;let es=n.forwardRef(({className:e,...t},r)=>s.jsx(Q,{ref:r,className:(0,ee.cn)("mt-2 ring-offset-white focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#8a8778] focus-visible:ring-offset-2",e),...t}));es.displayName=Q.displayName;let en=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,ee.cn)("rounded-lg border border-[#e5e2d9] bg-[#f8f8f5] text-[#2c2c27] shadow-sm",e),...t}));en.displayName="Card";let ea=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,ee.cn)("flex flex-col space-y-1.5 p-6",e),...t}));ea.displayName="CardHeader";let ei=n.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,ee.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));ei.displayName="CardTitle";let eo=n.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,ee.cn)("text-sm text-[#5c5c52]",e),...t}));eo.displayName="CardDescription";let el=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,ee.cn)("p-6 pt-0",e),...t}));el.displayName="CardContent";let ed=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,ee.cn)("flex items-center p-6 pt-0",e),...t}));ed.displayName="CardFooter";var ec=r(91664),eu=r(41190),ef=r(76557);let em=(0,ef.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var ep=r(48705);let eh=(0,ef.Z)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);var ex=r(68897);let eg=()=>{let e=(0,i.useRouter)(),{customer:t,updateProfile:r,refreshCustomer:a}=(0,ex.O)();if(!t)return s.jsx("div",{className:"flex justify-center items-center py-12",children:s.jsx("div",{className:"text-[#8a8778]",children:"Loading account information..."})});let[l,d]=(0,n.useState)("profile"),[c,u]=(0,n.useState)(!1),[f,m]=(0,n.useState)(!1),[p,h]=(0,n.useState)(null),[x,g]=(0,n.useState)(null),[v,b]=(0,n.useState)({firstName:t.firstName||"",lastName:t.lastName||"",email:t.email||"",phone:t.billing?.phone||""});(0,n.useEffect)(()=>{b({firstName:t.firstName||"",lastName:t.lastName||"",email:t.email||"",phone:t.billing?.phone||""})},[t]);let y=e=>{let{name:t,value:r}=e.target;b(e=>({...e,[t]:r}))},j=async e=>{e.preventDefault(),h(null),g(null),m(!0);try{let e={id:t.id,firstName:v.firstName,lastName:v.lastName,billing:{...t.billing,firstName:v.firstName,lastName:v.lastName,phone:v.phone}};await r(e),u(!1),g("Profile updated successfully"),setTimeout(()=>{g(null)},3e3)}catch(e){console.error("Error updating profile:",e),h(e.message||"An error occurred while updating your profile")}finally{m(!1)}};return(0,s.jsxs)(o.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,s.jsxs)("p",{className:"text-[#8a8778] mb-8",children:["Welcome back, ",t.firstName," ",t.lastName]}),(0,s.jsxs)(H,{defaultValue:l,onValueChange:d,className:"w-full",children:[(0,s.jsxs)(et,{className:`grid ${t.downloadableItems&&t.downloadableItems.nodes.length>0?"grid-cols-3":"grid-cols-2"} mb-8`,children:[(0,s.jsxs)(er,{value:"profile",className:"flex items-center gap-2",children:[s.jsx(em,{className:"h-4 w-4"}),s.jsx("span",{className:"hidden sm:inline",children:"Profile"})]}),(0,s.jsxs)(er,{value:"orders",className:"flex items-center gap-2",children:[s.jsx(ep.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"hidden sm:inline",children:"Orders"})]}),t.downloadableItems&&t.downloadableItems.nodes.length>0&&(0,s.jsxs)(er,{value:"downloads",className:"flex items-center gap-2",children:[s.jsx(ep.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"hidden sm:inline",children:"Downloads"})]})]}),s.jsx(es,{value:"profile",children:(0,s.jsxs)(en,{children:[(0,s.jsxs)(ea,{children:[s.jsx(ei,{children:"Profile Information"}),s.jsx(eo,{children:"Manage your personal information"})]}),(0,s.jsxs)(el,{className:"space-y-4",children:[p&&s.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4",children:p}),x&&s.jsx("div",{className:"bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-4",children:x}),c?(0,s.jsxs)("form",{onSubmit:j,className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"firstName",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"First Name"}),s.jsx(eu.I,{id:"firstName",name:"firstName",type:"text",value:v.firstName,onChange:y,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",required:!0})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"lastName",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Last Name"}),s.jsx(eu.I,{id:"lastName",name:"lastName",type:"text",value:v.lastName,onChange:y,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",required:!0})]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Email"}),s.jsx(eu.I,{id:"email",name:"email",type:"email",value:v.email,onChange:y,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778] bg-[#f4f3f0]",disabled:!0}),s.jsx("p",{className:"mt-1 text-xs text-[#8a8778]",children:"Email cannot be changed. Please contact support if you need to change your email."})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-[#5c5c52] mb-1",children:"Phone"}),s.jsx(eu.I,{id:"phone",name:"phone",type:"tel",value:v.phone,onChange:y,className:"w-full border-[#e5e2d9] focus:border-[#8a8778] focus:ring-[#8a8778]",placeholder:"(*************"})]}),(0,s.jsxs)("div",{className:"flex gap-2 pt-2",children:[s.jsx(ec.z,{type:"submit",disabled:f,className:"bg-[#2c2c27]",children:f?"Saving...":"Save Changes"}),s.jsx(ec.z,{type:"button",variant:"outline",onClick:()=>{u(!1),b({firstName:t.firstName||"",lastName:t.lastName||"",email:t.email||"",phone:t.billing?.phone||""})},children:"Cancel"})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"First Name"}),s.jsx("p",{className:"text-[#2c2c27]",children:t.firstName||"Not provided"})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Last Name"}),s.jsx("p",{className:"text-[#2c2c27]",children:t.lastName||"Not provided"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Email"}),s.jsx("p",{className:"text-[#2c2c27]",children:t.email||"Not provided"})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-[#5c5c52]",children:"Phone"}),s.jsx("p",{className:"text-[#2c2c27]",children:t.billing?.phone||"Not provided"})]})]})]})]}),s.jsx(ed,{children:!c&&(0,s.jsxs)(ec.z,{variant:"outline",onClick:()=>u(!0),className:"flex items-center gap-2",children:[s.jsx(eh,{className:"h-4 w-4"}),"Edit Profile"]})})]})}),s.jsx(es,{value:"orders",children:(0,s.jsxs)(en,{children:[(0,s.jsxs)(ea,{children:[s.jsx(ei,{children:"Order History"}),s.jsx(eo,{children:"View and track your orders"})]}),s.jsx(el,{children:t.orders&&t.orders.nodes.length>0?s.jsx("div",{className:"space-y-6",children:t.orders.nodes.map(e=>(0,s.jsxs)("div",{className:"border border-[#e5e2d9] p-6 rounded-md",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"font-medium text-[#2c2c27]",children:["Order #",e.databaseId]}),(0,s.jsxs)("p",{className:"text-sm text-[#8a8778]",children:["Placed on ",new Date(e.date).toLocaleDateString()]}),e.paymentMethodTitle&&(0,s.jsxs)("p",{className:"text-xs text-[#8a8778]",children:["Payment: ",e.paymentMethodTitle]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"font-medium text-[#2c2c27]",children:["$",parseFloat(e.total||"0").toFixed(2)]}),s.jsx("span",{className:`text-xs px-2 py-1 rounded ${"completed"===e.status?"bg-green-100 text-green-800":"processing"===e.status?"bg-blue-100 text-blue-800":"cancelled"===e.status?"bg-red-100 text-red-800":"bg-yellow-100 text-yellow-800"}`,children:e.status.toUpperCase()})]})]}),(0,s.jsxs)("div",{className:"space-y-3 mb-4",children:[s.jsx("h4",{className:"text-sm font-medium text-[#5c5c52]",children:"Items"}),e.lineItems.nodes.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-4 p-3 bg-gray-50 rounded",children:[e.product.node.image&&s.jsx("div",{className:"w-12 h-12 bg-gray-200 rounded overflow-hidden flex-shrink-0",children:s.jsx("img",{src:e.product.node.image.sourceUrl,alt:e.product.node.image.altText||e.product.node.name,className:"w-full h-full object-cover"})}),(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("p",{className:"text-[#2c2c27] font-medium",children:e.product.node.name}),e.variation&&e.variation.node.attributes&&s.jsx("div",{className:"text-xs text-[#8a8778]",children:e.variation.node.attributes.nodes.map((t,r)=>(0,s.jsxs)("span",{children:[t.name,": ",t.value,r<e.variation.node.attributes.nodes.length-1&&", "]},r))}),(0,s.jsxs)("p",{className:"text-xs text-[#8a8778]",children:["Qty: ",e.quantity," \xd7 $",(parseFloat(e.total||"0")/e.quantity).toFixed(2)]})]}),s.jsx("div",{className:"text-right",children:(0,s.jsxs)("p",{className:"font-medium text-[#2c2c27]",children:["$",parseFloat(e.total||"0").toFixed(2)]})})]},t))]}),(0,s.jsxs)("div",{className:"border-t border-[#e5e2d9] pt-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[s.jsx("span",{className:"text-[#8a8778]",children:"Subtotal:"}),(0,s.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.subtotal||"0").toFixed(2)]})]}),e.shippingTotal&&parseFloat(e.shippingTotal)>0&&(0,s.jsxs)("div",{children:[s.jsx("span",{className:"text-[#8a8778]",children:"Shipping:"}),(0,s.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.shippingTotal).toFixed(2)]})]}),e.totalTax&&parseFloat(e.totalTax)>0&&(0,s.jsxs)("div",{children:[s.jsx("span",{className:"text-[#8a8778]",children:"Tax:"}),(0,s.jsxs)("p",{className:"font-medium",children:["$",parseFloat(e.totalTax).toFixed(2)]})]}),e.discountTotal&&parseFloat(e.discountTotal)>0&&(0,s.jsxs)("div",{children:[s.jsx("span",{className:"text-[#8a8778]",children:"Discount:"}),(0,s.jsxs)("p",{className:"font-medium text-green-600",children:["-$",parseFloat(e.discountTotal).toFixed(2)]})]})]}),(e.shipping||e.billing)&&(0,s.jsxs)("div",{className:"mt-4 grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.billing&&(0,s.jsxs)("div",{children:[s.jsx("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Billing Address"}),(0,s.jsxs)("div",{className:"text-xs text-[#8a8778]",children:[(0,s.jsxs)("p",{children:[e.billing.firstName," ",e.billing.lastName]}),e.billing.company&&s.jsx("p",{children:e.billing.company}),s.jsx("p",{children:e.billing.address1}),e.billing.address2&&s.jsx("p",{children:e.billing.address2}),(0,s.jsxs)("p",{children:[e.billing.city,", ",e.billing.state," ",e.billing.postcode]}),s.jsx("p",{children:e.billing.country}),e.billing.phone&&(0,s.jsxs)("p",{children:["Phone: ",e.billing.phone]})]})]}),e.shipping&&e.shipping.address1&&(0,s.jsxs)("div",{children:[s.jsx("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Shipping Address"}),(0,s.jsxs)("div",{className:"text-xs text-[#8a8778]",children:[(0,s.jsxs)("p",{children:[e.shipping.firstName," ",e.shipping.lastName]}),e.shipping.company&&s.jsx("p",{children:e.shipping.company}),s.jsx("p",{children:e.shipping.address1}),e.shipping.address2&&s.jsx("p",{children:e.shipping.address2}),(0,s.jsxs)("p",{children:[e.shipping.city,", ",e.shipping.state," ",e.shipping.postcode]}),s.jsx("p",{children:e.shipping.country})]})]})]}),e.customerNote&&(0,s.jsxs)("div",{className:"mt-4",children:[s.jsx("h5",{className:"text-sm font-medium text-[#5c5c52] mb-2",children:"Order Notes"}),s.jsx("p",{className:"text-xs text-[#8a8778] bg-gray-50 p-2 rounded",children:e.customerNote})]}),s.jsx("div",{className:"mt-4 flex justify-end",children:s.jsx(ec.z,{variant:"outline",size:"sm",children:"View Full Details"})})]})]},e.id))}):(0,s.jsxs)("div",{className:"text-center py-8",children:[s.jsx("p",{className:"text-[#8a8778] mb-4",children:"You haven't placed any orders yet."}),s.jsx(ec.z,{onClick:()=>e.push("/collection"),children:"Start Shopping"})]})})]})}),t.downloadableItems&&t.downloadableItems.nodes.length>0&&s.jsx(es,{value:"downloads",children:(0,s.jsxs)(en,{children:[(0,s.jsxs)(ea,{children:[s.jsx(ei,{children:"Downloadable Items"}),s.jsx(eo,{children:"Access your digital downloads and products"})]}),s.jsx(el,{children:s.jsx("div",{className:"space-y-4",children:t.downloadableItems.nodes.map((e,t)=>s.jsx("div",{className:"border border-[#e5e2d9] p-4 rounded-md",children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{className:"flex-1",children:[s.jsx("h3",{className:"font-medium text-[#2c2c27]",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-[#8a8778] mb-2",children:["Product: ",e.product.node.name]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{className:"text-xs text-[#8a8778]",children:"Download ID"}),s.jsx("p",{className:"text-[#2c2c27]",children:e.downloadId})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"text-xs text-[#8a8778]",children:"Downloads Remaining"}),s.jsx("p",{className:"text-[#2c2c27]",children:null!==e.downloadsRemaining?e.downloadsRemaining:"Unlimited"})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{className:"text-xs text-[#8a8778]",children:"Access Expires"}),s.jsx("p",{className:"text-[#2c2c27]",children:e.accessExpires?new Date(e.accessExpires).toLocaleDateString():"Never"})]})]})]}),s.jsx("div",{className:"ml-4",children:s.jsx(ec.z,{variant:"outline",size:"sm",disabled:0===e.downloadsRemaining,children:"Download"})})]})},t))})})]})})]})]})}},68897:(e,t,r)=>{"use strict";r.d(t,{CustomerProvider:()=>o,O:()=>i});var s=r(10326),n=r(17577);let a=(0,n.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),i=()=>(0,n.useContext)(a),o=({children:e})=>{let[t,r]=(0,n.useState)(null),[i,o]=(0,n.useState)(!1),[l,d]=(0,n.useState)(null),[c,u]=(0,n.useState)(null),f=async e=>{console.log("Login function called - minimal implementation")},m=async e=>{console.log("Register function called - minimal implementation")},p=async e=>(console.log("Update profile function called - minimal implementation"),{}),h=async()=>{console.log("Refresh customer function called - minimal implementation")};return s.jsx(a.Provider,{value:{customer:t,isLoading:i,isAuthenticated:!!t&&!!c,token:c,login:f,register:m,logout:()=>{console.log("Logout function called - minimal implementation"),r(null),u(null)},updateProfile:p,error:l,refreshCustomer:h},children:e})}},91664:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var s=r(10326);r(17577);var n=r(34214),a=r(79360),i=r(51223);let o=(0,a.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-[#2c2c27] text-[#f4f3f0] shadow-xs hover:bg-[#3d3d35]",destructive:"bg-[#ff4d4f] text-white shadow-xs hover:bg-[#ff4d4f]/90 focus-visible:ring-[#ff4d4f]/20 dark:focus-visible:ring-[#ff4d4f]/40",outline:"border border-[#e5e2d9] bg-[#f8f8f5] shadow-xs hover:bg-[#f4f3f0] hover:text-[#2c2c27]",secondary:"bg-[#e5e2d9] text-[#2c2c27] shadow-xs hover:bg-[#e5e2d9]/80",ghost:"hover:bg-[#f4f3f0] hover:text-[#2c2c27]",link:"text-[#2c2c27] underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:a=!1,...l}){let d=a?n.g7:"button";return s.jsx(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...l})}},41190:(e,t,r)=>{"use strict";r.d(t,{I:()=>i});var s=r(10326),n=r(17577),a=r(51223);let i=n.forwardRef(({className:e,type:t,...r},n)=>s.jsx("input",{type:t,"data-slot":"input",className:(0,a.cn)("border-[#e5e2d9] file:text-[#2c2c27] placeholder:text-[#8a8778] selection:bg-[#2c2c27] selection:text-[#f4f3f0] flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-[#8a8778] focus-visible:ring-[#8a8778]/50 focus-visible:ring-[3px]","aria-invalid:ring-[#ff4d4f]/20 dark:aria-invalid:ring-[#ff4d4f]/40 aria-invalid:border-[#ff4d4f]",e),ref:n,...r}));i.displayName="Input"},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>f});var s=r(10326),n=r(17577),a=r(92148),i=r(86462),o=r(54659),l=r(87888),d=r(18019),c=r(94019);let u=(0,n.createContext)(void 0);function f({children:e}){let[t,r]=(0,n.useState)([]);return(0,s.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",s=3e3)=>{let n=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:n,message:e,type:t,duration:s}])},removeToast:e=>{r(t=>t.filter(t=>t.id!==e))}},children:[e,s.jsx(p,{})]})}function m({toast:e,onRemove:t}){return(0,s.jsxs)(a.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(o.Z,{className:"h-5 w-5"});case"error":return s.jsx(l.Z,{className:"h-5 w-5"});default:return s.jsx(d.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(c.Z,{className:"h-4 w-4"})})]})}function p(){let{toasts:e,removeToast:t}=function(){let e=(0,n.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(i.M,{children:e.map(e=>s.jsx(m,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},51223:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(41135),n=r(31009);function a(...e){return(0,n.m6)((0,s.W)(e))}},71615:(e,t,r)=>{"use strict";var s=r(88757);r.o(s,"cookies")&&r.d(t,{cookies:function(){return s.cookies}})},58585:(e,t,r)=>{"use strict";var s=r(61085);r.o(s,"notFound")&&r.d(t,{notFound:function(){return s.notFound}}),r.o(s,"redirect")&&r.d(t,{redirect:function(){return s.redirect}})},33085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return a}});let s=r(45869),n=r(6278);class a{get isEnabled(){return this._provider.isEnabled}enable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,n.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,n.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return f},draftMode:function(){return m},headers:function(){return u}});let s=r(68996),n=r(53047),a=r(92044),i=r(72934),o=r(33085),l=r(6278),d=r(45869),c=r(54580);function u(){let e="headers",t=d.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return n.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,c.getExpectedRequestStore)(e).headers}function f(){let e="cookies",t=d.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,c.getExpectedRequestStore)(e),n=i.actionAsyncStorage.getStore();return(null==n?void 0:n.isAction)||(null==n?void 0:n.isAppRoute)?r.mutableCookies:r.cookies}function m(){let e=(0,c.getExpectedRequestStore)("draftMode");return new o.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61085:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return i},RedirectType:function(){return s.RedirectType},notFound:function(){return n.notFound},permanentRedirect:function(){return s.permanentRedirect},redirect:function(){return s.redirect}});let s=r(83953),n=r(16399);class a extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class i extends URLSearchParams{append(){throw new a}delete(){throw new a}set(){throw new a}sort(){throw new a}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return n},notFound:function(){return s}});let r="NEXT_NOT_FOUND";function s(){let e=Error(r);throw e.digest=r,e}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{"use strict";var s;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return s},getRedirectError:function(){return l},getRedirectStatusCodeFromError:function(){return p},getRedirectTypeFromError:function(){return m},getURLFromRedirectError:function(){return f},isRedirectError:function(){return u},permanentRedirect:function(){return c},redirect:function(){return d}});let n=r(54580),a=r(72934),i=r(8586),o="NEXT_REDIRECT";function l(e,t,r){void 0===r&&(r=i.RedirectStatusCode.TemporaryRedirect);let s=Error(o);s.digest=o+";"+t+";"+e+";"+r+";";let a=n.requestAsyncStorage.getStore();return a&&(s.mutableCookies=a.mutableCookies),s}function d(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=a.actionAsyncStorage.getStore();throw l(e,t,(null==r?void 0:r.isAction)?i.RedirectStatusCode.SeeOther:i.RedirectStatusCode.PermanentRedirect)}function u(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,s,n]=e.digest.split(";",4),a=Number(n);return t===o&&("replace"===r||"push"===r)&&"string"==typeof s&&!isNaN(a)&&a in i.RedirectStatusCode}function f(e){return u(e)?e.digest.split(";",3)[2]:null}function m(e){if(!u(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function p(e){if(!u(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(s||(s={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79925:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,a={};function i(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),s=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?s:`${s}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[s,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(s,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[s,n],...a]=o(e),{domain:i,expires:l,httponly:u,maxage:f,path:m,samesite:p,secure:h,partitioned:x,priority:g}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:s,value:decodeURIComponent(n),domain:i,...l&&{expires:new Date(l)},...u&&{httpOnly:!0},..."string"==typeof f&&{maxAge:Number(f)},path:m,...p&&{sameSite:d.includes(t=(t=p).toLowerCase())?t:void 0},...h&&{secure:!0},...g&&{priority:c.includes(r=(r=g).toLowerCase())?r:void 0},...x&&{partitioned:!0}})}((e,r)=>{for(var s in r)t(e,s,{get:r[s],enumerable:!0})})(a,{RequestCookies:()=>u,ResponseCookies:()=>f,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>i}),e.exports=((e,a,i,o)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let i of s(a))n.call(e,i)||void 0===i||t(e,i,{get:()=>a[i],enumerable:!(o=r(a,i))||o.enumerable});return e})(t({},"__esModule",{value:!0}),a);var d=["strict","lax","none"],c=["low","medium","high"],u=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let s="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===s).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,s=this._parsed;return s.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(s).map(([e,t])=>i(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>i(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},f=class{constructor(e){var t,r,s;this._parsed=new Map,this._headers=e;let n=null!=(s=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?s:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,s,n,a,i=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,a=!1;l();)if(","===(r=e.charAt(o))){for(s=o,o+=1,l(),n=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(a=!0,o=n,i.push(e.substring(t,s)),t=o):o=s+1}else o+=1;(!a||o>=e.length)&&i.push(e.substring(t,e.length))}return i}(n)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let s="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===s)}has(e){return this._parsed.has(e)}set(...e){let[t,r,s]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...s})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=i(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,s]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:s,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(i).join("; ")}}},53047:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return a},ReadonlyHeadersError:function(){return n}});let s=r(38238);class n extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new n}}class a extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return s.ReflectAdapter.get(t,r,n);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==i)return s.ReflectAdapter.get(t,i,n)},set(t,r,n,a){if("symbol"==typeof r)return s.ReflectAdapter.set(t,r,n,a);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);return s.ReflectAdapter.set(t,o??r,n,a)},has(t,r){if("symbol"==typeof r)return s.ReflectAdapter.has(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==a&&s.ReflectAdapter.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return s.ReflectAdapter.deleteProperty(t,r);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===a||s.ReflectAdapter.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return n.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new a(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return u},ReadonlyRequestCookiesError:function(){return i},RequestCookiesAdapter:function(){return o},appendMutableCookies:function(){return c},getModifiedCookieValues:function(){return d}});let s=r(92044),n=r(38238),a=r(45869);class i extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new i}}class o{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return i.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function d(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function c(e,t){let r=d(t);if(0===r.length)return!1;let n=new s.ResponseCookies(e),a=n.getAll();for(let e of r)n.set(e);for(let e of a)n.set(e);return!0}class u{static wrap(e,t){let r=new s.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let i=[],o=new Set,d=()=>{let e=a.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of i){let r=new s.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return i;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{d()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{d()}};default:return n.ReflectAdapter.get(e,t,r)}}})}}},92044:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RequestCookies:function(){return s.RequestCookies},ResponseCookies:function(){return s.ResponseCookies},stringifyCookie:function(){return s.stringifyCookie}});let s=r(79925)},36147:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>f,dynamic:()=>m,metadata:()=>p});var n=r(19510),a=r(58585),i=r(71615),o=r(70591),l=r(93690),d=r(63253),c=e([l]);l=(c.then?(await c)():c)[0];let m="force-dynamic",p={title:"My Account | Ankkor",description:"View your account details, order history, and manage your profile."},h=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",x=(0,l.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      username
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`;async function u(e){let t=new l.GraphQLClient(h,{headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${e}`}});try{let e=await t.request(x);return{success:!0,customer:e.customer}}catch(e){return console.error("Error fetching customer data:",e),{success:!1,error:"Failed to fetch customer data"}}}async function f(){let e=(0,i.cookies)().get("woo_auth_token");e&&e.value||(0,a.redirect)("/sign-in?redirect=/account");try{let t=(0,o.o)(e.value),r=Math.floor(Date.now()/1e3);t.exp<r&&(0,a.redirect)("/sign-in?redirect=/account&reason=expired")}catch(e){console.error("Invalid JWT token:",e),(0,a.redirect)("/sign-in?redirect=/account&reason=invalid")}let t=await u(e.value),r=t.success?t.customer:null;return r?(0,n.jsxs)("div",{className:"container mx-auto py-12 px-4",children:[n.jsx("h1",{className:"text-3xl font-serif mb-8",children:"My Account"}),r?n.jsx(d.Z,{}):n.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded",children:"Unable to load account information. Please try again later."})]}):(0,n.jsxs)("div",{className:"container mx-auto py-12 px-4",children:[n.jsx("h1",{className:"text-3xl font-serif mb-8",children:"My Account"}),(0,n.jsxs)("div",{className:"bg-red-50 border border-red-200 text-red-700 p-4 rounded",children:["Unable to load account information. Please try ",n.jsx("a",{href:"/sign-in",className:"underline",children:"signing in again"}),"."]})]})}s()}catch(e){s(e)}})},51806:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f,metadata:()=>u});var s=r(19510),n=r(10527),a=r.n(n),i=r(36822),o=r.n(i);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function f({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${a().variable} ${o().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(c,{children:s.jsx(d,{children:s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},63253:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\account\AccountDashboard.tsx#default`)},5023:()=>{},45226:(e,t,r)=>{"use strict";r.d(t,{WV:()=>i});var s=r(17577);r(60962);var n=r(34214),a=r(10326),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=s.forwardRef((e,r)=>{let{asChild:s,...i}=e,o=s?n.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{})},70591:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});class s extends Error{}function n(e,t){let r;if("string"!=typeof e)throw new s("Invalid token specified: must be a string");t||(t={});let n=!0===t.header?0:1,a=e.split(".")[n];if("string"!=typeof a)throw new s(`Invalid token specified: missing part #${n+1}`);try{r=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw Error("base64 string is not of the correct length")}try{var r;return r=t,decodeURIComponent(atob(r).replace(/(.)/g,(e,t)=>{let r=t.charCodeAt(0).toString(16).toUpperCase();return r.length<2&&(r="0"+r),"%"+r}))}catch(e){return atob(t)}}(a)}catch(e){throw new s(`Invalid token specified: invalid base64 for part #${n+1} (${e.message})`)}try{return JSON.parse(r)}catch(e){throw new s(`Invalid token specified: invalid json for part #${n+1} (${e.message})`)}}s.prototype.name="InvalidTokenError"}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,3373,2325],()=>r(42291));module.exports=s})();