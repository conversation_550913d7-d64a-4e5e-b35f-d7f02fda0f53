"use strict";(()=>{var e={};e.id=9261,e.ids=[9261],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},93690:e=>{e.exports=import("graphql-request")},80392:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{originalPathname:()=>y,patchFetch:()=>u,requestAsyncStorage:()=>c,routeModule:()=>d,serverHooks:()=>f,staticGenerationAsyncStorage:()=>p});var o=r(49303),a=r(88716),n=r(60670),i=r(75526),l=e([i]);i=(l.then?(await l)():l)[0];let d=new o.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/update-profile/route",pathname:"/api/auth/update-profile",filename:"route",bundlePath:"app/api/auth/update-profile/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\auth\\update-profile\\route.ts",nextConfigOutput:"standalone",userland:i}),{requestAsyncStorage:c,staticGenerationAsyncStorage:p,serverHooks:f}=d,y="/api/auth/update-profile/route";function u(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:p})}s()}catch(e){s(e)}})},71615:(e,t,r)=>{var s=r(88757);r.o(s,"cookies")&&r.d(t,{cookies:function(){return s.cookies}})},33085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DraftMode",{enumerable:!0,get:function(){return a}});let s=r(45869),o=r(6278);class a{get isEnabled(){return this._provider.isEnabled}enable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().enable()"),this._provider.enable()}disable(){let e=s.staticGenerationAsyncStorage.getStore();return e&&(0,o.trackDynamicDataAccessed)(e,"draftMode().disable()"),this._provider.disable()}constructor(e){this._provider=e}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88757:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cookies:function(){return p},draftMode:function(){return f},headers:function(){return c}});let s=r(68996),o=r(53047),a=r(92044),n=r(72934),i=r(33085),l=r(6278),u=r(45869),d=r(54580);function c(){let e="headers",t=u.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return o.HeadersAdapter.seal(new Headers({}));(0,l.trackDynamicDataAccessed)(t,e)}return(0,d.getExpectedRequestStore)(e).headers}function p(){let e="cookies",t=u.staticGenerationAsyncStorage.getStore();if(t){if(t.forceStatic)return s.RequestCookiesAdapter.seal(new a.RequestCookies(new Headers({})));(0,l.trackDynamicDataAccessed)(t,e)}let r=(0,d.getExpectedRequestStore)(e),o=n.actionAsyncStorage.getStore();return(null==o?void 0:o.isAction)||(null==o?void 0:o.isAppRoute)?r.mutableCookies:r.cookies}function f(){let e=(0,d.getExpectedRequestStore)("draftMode");return new i.DraftMode(e.draftMode)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},53047:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return a},ReadonlyHeadersError:function(){return o}});let s=r(38238);class o extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new o}}class a extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,o){if("symbol"==typeof r)return s.ReflectAdapter.get(t,r,o);let a=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===a);if(void 0!==n)return s.ReflectAdapter.get(t,n,o)},set(t,r,o,a){if("symbol"==typeof r)return s.ReflectAdapter.set(t,r,o,a);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return s.ReflectAdapter.set(t,i??r,o,a)},has(t,r){if("symbol"==typeof r)return s.ReflectAdapter.has(t,r);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0!==a&&s.ReflectAdapter.has(t,a)},deleteProperty(t,r){if("symbol"==typeof r)return s.ReflectAdapter.deleteProperty(t,r);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return void 0===a||s.ReflectAdapter.deleteProperty(t,a)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return o.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new a(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},68996:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return c},ReadonlyRequestCookiesError:function(){return n},RequestCookiesAdapter:function(){return i},appendMutableCookies:function(){return d},getModifiedCookieValues:function(){return u}});let s=r(92044),o=r(38238),a=r(45869);class n extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new n}}class i{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return n.callable;default:return o.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function u(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function d(e,t){let r=u(t);if(0===r.length)return!1;let o=new s.ResponseCookies(e),a=o.getAll();for(let e of r)o.set(e);for(let e of a)o.set(e);return!0}class c{static wrap(e,t){let r=new s.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let n=[],i=new Set,u=()=>{let e=a.staticGenerationAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),n=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of n){let r=new s.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case l:return n;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{u()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{u()}};default:return o.ReflectAdapter.get(e,t,r)}}})}}},75526:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{POST:()=>l});var o=r(87070),a=r(71615),n=r(93690),i=e([n]);n=(i.then?(await i)():i)[0];let u=process.env.WOOCOMMERCE_GRAPHQL_URL||"https://your-wordpress-site.com/graphql",d=(0,n.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`,c=(0,n.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      displayName
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
    }
  }
`;async function l(e){try{console.log("Update profile API called");let t=(0,a.cookies)(),r=t.get("woo_auth_token")?.value;if(!r)return console.log("No auth token found in cookies"),o.NextResponse.json({success:!1,message:"Not authenticated"},{status:401});let s=await e.json();console.log("Customer data to update:",s);let i=new n.GraphQLClient(u,{headers:{"Content-Type":"application/json",Accept:"application/json",Authorization:`Bearer ${r}`}}),l={input:{clientMutationId:"updateCustomer",...s}};console.log("GraphQL variables:",l);let p=await i.request(d,l);if(console.log("Update response:",p),!p.updateCustomer||!p.updateCustomer.customer)return console.error("Customer update failed: No customer data returned"),o.NextResponse.json({success:!1,message:"Failed to update customer profile"},{status:400});let f=await i.request(c);return console.log("Updated customer data:",f.customer),o.NextResponse.json({success:!0,customer:f.customer,message:"Profile updated successfully"})}catch(e){if(console.error("Error updating customer profile:",e),e.response?.errors){let t=e.response.errors[0];return o.NextResponse.json({success:!1,message:t.message||"Profile update failed"},{status:400})}return o.NextResponse.json({success:!1,message:e.message||"Internal server error"},{status:500})}}s()}catch(e){s(e)}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,5972],()=>r(80392));module.exports=s})();