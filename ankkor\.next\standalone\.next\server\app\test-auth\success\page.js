(()=>{var e={};e.id=6757,e.ids=[6757],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93690:e=>{"use strict";e.exports=import("graphql-request")},99842:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l}),s(90782),s(31710),s(12523);var a=s(23191),r=s(88716),n=s(37922),i=s.n(n),o=s(95231),d={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let l=["",{children:["test-auth",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90782)),"E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\success\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,31710)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\success\\page.tsx"],u="/test-auth/success/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/test-auth/success/page",pathname:"/test-auth/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},73372:(e,t,s)=>{Promise.resolve().then(s.bind(s,48720))},90434:(e,t,s)=>{"use strict";s.d(t,{default:()=>r.a});var a=s(79404),r=s.n(a)},48720:(e,t,s)=>{"use strict";s.a(e,async(e,a)=>{try{s.r(t),s.d(t,{default:()=>l});var r=s(10326),n=s(17577),i=s(90434),o=s(61296),d=e([o]);function l(){let[e,t]=(0,n.useState)(null),[s,a]=(0,n.useState)(!0);return r.jsx("div",{className:"container mx-auto py-12",children:(0,r.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[r.jsx("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Authentication Successful"}),s?r.jsx("p",{className:"text-center",children:"Loading user data..."}):e?(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx("div",{className:"p-4 bg-green-50 border border-green-200 text-green-700",children:r.jsx("p",{className:"font-medium",children:"Successfully authenticated!"})}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h2",{className:"text-lg font-medium",children:"User Information"}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Name:"})," ",e.firstName," ",e.lastName]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"Email:"})," ",e.email]}),(0,r.jsxs)("p",{children:[r.jsx("strong",{children:"ID:"})," ",e.id]})]})]}):r.jsx("div",{className:"p-4 bg-yellow-50 border border-yellow-200 text-yellow-700",children:r.jsx("p",{children:"No user data found. Authentication may have failed."})}),r.jsx("div",{className:"mt-8 flex justify-center",children:r.jsx(i.default,{href:"/test-auth",className:"bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#3c3c37]",children:"Back to Test Page"})})]})})}o=(d.then?(await d)():d)[0],a()}catch(e){a(e)}})},61296:(e,t,s)=>{"use strict";s.a(e,async(e,t)=>{try{var a=s(93690);s(18201);var r=e([a]);a=(r.then?(await r)():r)[0],(0,a.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RegisterUser($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      clientMutationId
      authToken
      refreshToken
      customer {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`,(0,a.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`,(0,a.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let n="https://maroon-lapwing-781450.hostingersite.com/graphql",i=n&&!n.startsWith("http")?`https://${n}`:n;new a.GraphQLClient(i,{headers:{"Content-Type":"application/json"}}),t()}catch(e){t(e)}})},90782:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\test-auth\success\page.tsx#default`)},18201:(e,t,s)=>{"use strict";class a extends Error{}a.prototype.name="InvalidTokenError"}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,8216,9404,8888],()=>s(99842));module.exports=a})();