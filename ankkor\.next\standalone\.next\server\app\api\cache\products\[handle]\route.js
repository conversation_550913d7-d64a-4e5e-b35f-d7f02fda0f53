"use strict";(()=>{var e={};e.id=4438,e.ids=[4438],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},93690:e=>{e.exports=import("graphql-request")},22137:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{originalPathname:()=>_,patchFetch:()=>i,requestAsyncStorage:()=>l,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>d});var n=t(49303),s=t(88716),a=t(60670),c=t(7496),u=e([c]);c=(u.then?(await u)():u)[0];let p=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/cache/products/[handle]/route",pathname:"/api/cache/products/[handle]",filename:"route",bundlePath:"app/api/cache/products/[handle]/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\cache\\products\\[handle]\\route.ts",nextConfigOutput:"standalone",userland:c}),{requestAsyncStorage:l,staticGenerationAsyncStorage:d,serverHooks:h}=p,_="/api/cache/products/[handle]/route";function i(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:d})}o()}catch(e){o(e)}})},7496:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{GET:()=>u});var n=t(87070),s=t(19910),a=t(79534),c=e([s]);async function u(e,{params:r}){let{handle:t}=r;if(!t)return n.NextResponse.json({error:"Product handle is required"},{status:400});try{let e=`product:${t}`,r=await (0,a.Fs)(e,async()=>(console.log(`Cache miss for product ${t}, fetching from WooCommerce`),(0,s.gF)(t)),a.mJ.MEDIUM);if(!r)return n.NextResponse.json({error:"Product not found"},{status:404});return n.NextResponse.json({product:r,_cache:{key:e,ttl:a.mJ.MEDIUM,expiresIn:`${Math.floor(a.mJ.MEDIUM/60)} minutes`}})}catch(e){return console.error(`Error fetching product ${t}:`,e),n.NextResponse.json({error:"Failed to fetch product",message:e.message},{status:500})}}s=(c.then?(await c)():c)[0],o()}catch(e){o(e)}})},79534:(e,r,t)=>{t.d(r,{Fs:()=>i,IV:()=>u,Pc:()=>s,U2:()=>a,mJ:()=>n,t8:()=>c});let o=new(t(94868)).s({url:process.env.UPSTASH_REDIS_REST_URL||process.env.NEXT_PUBLIC_KV_REST_API_URL||"",token:process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_TOKEN||""}),n={SHORT:300,MEDIUM:3600,LONG:86400,WEEK:604800};function s(){return!!(process.env.UPSTASH_REDIS_REST_URL&&process.env.UPSTASH_REDIS_REST_TOKEN||process.env.NEXT_PUBLIC_KV_REST_API_URL&&process.env.NEXT_PUBLIC_KV_REST_API_TOKEN)}async function a(e){if(!s())return null;try{return await o.get(e)||null}catch(r){return console.error(`Redis get error for key ${e}:`,r),null}}async function c(e,r,t){if(!s())return!1;try{return t?await o.setex(e,t,r):await o.set(e,r),!0}catch(r){return console.error(`Redis set error for key ${e}:`,r),!1}}async function u(e){if(!s())return!1;try{return await o.del(e),!0}catch(r){return console.error(`Redis delete error for key ${e}:`,r),!1}}async function i(e,r,t=n.MEDIUM){if(!s())return await r();try{let n=await o.get(e);if(null!==n)return console.log(`Cache hit for key: ${e}`),JSON.parse(n);console.log(`Cache miss for key: ${e}, fetching fresh data`);let s=await r();return await o.setex(e,t,JSON.stringify(s)),s}catch(t){return console.error(`Redis cache error for key ${e}:`,t),await r()}}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8948,5972,4766,4868,9910],()=>t(22137));module.exports=o})();