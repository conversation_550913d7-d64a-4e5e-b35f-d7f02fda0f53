(()=>{var e={};e.id=6757,e.ids=[6757],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},93690:e=>{"use strict";e.exports=import("graphql-request")},99842:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),s(90782),s(51806),s(12523);var r=s(23191),a=s(88716),n=s(37922),o=s.n(n),i=s(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let c=["",{children:["test-auth",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90782)),"E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\success\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],d=["E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\success\\page.tsx"],u="/test-auth/success/page",m={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-auth/success/page",pathname:"/test-auth/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},13417:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,12994,23)),Promise.resolve().then(s.t.bind(s,96114,23)),Promise.resolve().then(s.t.bind(s,9727,23)),Promise.resolve().then(s.t.bind(s,79671,23)),Promise.resolve().then(s.t.bind(s,41868,23)),Promise.resolve().then(s.t.bind(s,84759,23))},96799:(e,t,s)=>{Promise.resolve().then(s.bind(s,68897)),Promise.resolve().then(s.bind(s,75367))},54039:(e,t,s)=>{Promise.resolve().then(s.bind(s,83846))},73372:(e,t,s)=>{Promise.resolve().then(s.bind(s,48720))},90434:(e,t,s)=>{"use strict";s.d(t,{default:()=>a.a});var r=s(79404),a=s.n(r)},83846:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(10326);s(17577);var a=s(33265);let n=()=>r.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,r.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),r.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),o=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>r.jsx(n,{})});function i(){return r.jsx("div",{className:"container mx-auto py-20",children:r.jsx(o,{})})}},48720:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>c});var a=s(10326),n=s(17577),o=s(90434),i=s(61296),l=e([i]);function c(){let[e,t]=(0,n.useState)(null),[s,r]=(0,n.useState)(!0);return a.jsx("div",{className:"container mx-auto py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Authentication Successful"}),s?a.jsx("p",{className:"text-center",children:"Loading user data..."}):e?(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"p-4 bg-green-50 border border-green-200 text-green-700",children:a.jsx("p",{className:"font-medium",children:"Successfully authenticated!"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h2",{className:"text-lg font-medium",children:"User Information"}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Name:"})," ",e.firstName," ",e.lastName]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Email:"})," ",e.email]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"ID:"})," ",e.id]})]})]}):a.jsx("div",{className:"p-4 bg-yellow-50 border border-yellow-200 text-yellow-700",children:a.jsx("p",{children:"No user data found. Authentication may have failed."})}),a.jsx("div",{className:"mt-8 flex justify-center",children:a.jsx(o.default,{href:"/test-auth",className:"bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#3c3c37]",children:"Back to Test Page"})})]})})}i=(l.then?(await l)():l)[0],r()}catch(e){r(e)}})},68897:(e,t,s)=>{"use strict";s.d(t,{CustomerProvider:()=>i,O:()=>o});var r=s(10326),a=s(17577);let n=(0,a.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),o=()=>(0,a.useContext)(n),i=({children:e})=>{let[t,s]=(0,a.useState)(null),[o,i]=(0,a.useState)(!1),[l,c]=(0,a.useState)(null),[d,u]=(0,a.useState)(null),m=async e=>{console.log("Login function called - minimal implementation")},p=async e=>{console.log("Register function called - minimal implementation")},h=async e=>(console.log("Update profile function called - minimal implementation"),{}),g=async()=>{console.log("Refresh customer function called - minimal implementation")};return r.jsx(n.Provider,{value:{customer:t,isLoading:o,isAuthenticated:!!t&&!!d,token:d,login:m,register:p,logout:()=>{console.log("Logout function called - minimal implementation"),s(null),u(null)},updateProfile:h,error:l,refreshCustomer:g},children:e})}},75367:(e,t,s)=>{"use strict";s.d(t,{ToastProvider:()=>m});var r=s(10326),a=s(17577),n=s(92148),o=s(86462),i=s(54659),l=s(87888),c=s(18019),d=s(94019);let u=(0,a.createContext)(void 0);function m({children:e}){let[t,s]=(0,a.useState)([]);return(0,r.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",r=3e3)=>{let a=Math.random().toString(36).substring(2,9);s(s=>[...s,{id:a,message:e,type:t,duration:r}])},removeToast:e=>{s(t=>t.filter(t=>t.id!==e))}},children:[e,r.jsx(h,{})]})}function p({toast:e,onRemove:t}){return(0,r.jsxs)(n.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[r.jsx(()=>{switch(e.type){case"success":return r.jsx(i.Z,{className:"h-5 w-5"});case"error":return r.jsx(l.Z,{className:"h-5 w-5"});default:return r.jsx(c.Z,{className:"h-5 w-5"})}},{}),r.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),r.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:r.jsx(d.Z,{className:"h-4 w-4"})})]})}function h(){let{toasts:e,removeToast:t}=function(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return r.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:r.jsx(o.M,{children:e.map(e=>r.jsx(p,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},61296:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{ts:()=>l,x4:()=>o,z2:()=>i});var a=s(93690);s(18201);var n=e([a]);a=(n.then?(await n)():n)[0],(0,a.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RegisterUser($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      clientMutationId
      authToken
      refreshToken
      customer {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`,(0,a.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`,(0,a.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let c="https://maroon-lapwing-781450.hostingersite.com/graphql",d=c&&!c.startsWith("http")?`https://${c}`:c;async function o(e,t){try{let s=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e,password:t}),credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Login failed")}let r=await s.json();if(r.success&&r.user)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Login successful, user data received"),{success:!0,user:r.user,token:r.token};throw console.error("Login response missing user data"),Error("Login failed: Invalid response from server")}catch(e){return console.error("Login error:",e),{success:!1,message:e.message||"Login failed"}}}async function i(e,t,s,r){try{let a=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e,firstName:t,lastName:s,password:r}),credentials:"include"});if(!a.ok){let e=await a.json();throw Error(e.message||"Registration failed")}let n=await a.json();if(n.success&&n.customer)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Registration successful, user data received"),{success:!0,customer:n.customer,token:n.token};throw console.error("Registration response missing customer data"),Error("Registration failed: Invalid response from server")}catch(e){return console.error("Registration error:",e),{success:!1,message:e.message||"Registration failed"}}}async function l(){try{let e=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===e.status)return null;let t=await e.json();if(!e.ok||!t.success)return null;return t.user}catch(e){return console.error("Get user error:",e),null}}new a.GraphQLClient(d,{headers:{"Content-Type":"application/json"}}),r()}catch(e){r(e)}})},51806:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>u});var r=s(19510),a=s(10527),n=s.n(a),o=s(36822),i=s.n(o);s(5023);var l=s(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return r.jsx("html",{lang:"en",children:r.jsx("body",{className:`${n().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:r.jsx(d,{children:r.jsx(c,{children:r.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},90782:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\test-auth\success\page.tsx#default`)},5023:()=>{},18201:(e,t,s)=>{"use strict";class r extends Error{}r.prototype.name="InvalidTokenError"}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,3373,9404],()=>s(99842));module.exports=r})();