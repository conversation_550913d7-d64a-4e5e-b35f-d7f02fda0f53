(()=>{var e={};e.id=6757,e.ids=[6757],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},84770:e=>{"use strict";e.exports=require("crypto")},93690:e=>{"use strict";e.exports=import("graphql-request")},99842:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>l}),s(90782),s(52617),s(12523);var r=s(23191),a=s(88716),n=s(37922),o=s.n(n),i=s(95231),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);s.d(t,c);let l=["",{children:["test-auth",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,90782)),"E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\success\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,52617)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],u=["E:\\ankkorwoo\\ankkor\\src\\app\\test-auth\\success\\page.tsx"],d="/test-auth/success/page",p={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-auth/success/page",pathname:"/test-auth/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},73372:(e,t,s)=>{Promise.resolve().then(s.bind(s,48720))},48720:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{default:()=>l});var a=s(10326),n=s(17577),o=s(90434),i=s(61296),c=e([i]);function l(){let[e,t]=(0,n.useState)(null),[s,r]=(0,n.useState)(!0);return a.jsx("div",{className:"container mx-auto py-12",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto bg-white p-8 border border-gray-200",children:[a.jsx("h1",{className:"text-2xl font-bold mb-6 text-center",children:"Authentication Successful"}),s?a.jsx("p",{className:"text-center",children:"Loading user data..."}):e?(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:"p-4 bg-green-50 border border-green-200 text-green-700",children:a.jsx("p",{className:"font-medium",children:"Successfully authenticated!"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h2",{className:"text-lg font-medium",children:"User Information"}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Name:"})," ",e.firstName," ",e.lastName]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"Email:"})," ",e.email]}),(0,a.jsxs)("p",{children:[a.jsx("strong",{children:"ID:"})," ",e.id]})]})]}):a.jsx("div",{className:"p-4 bg-yellow-50 border border-yellow-200 text-yellow-700",children:a.jsx("p",{children:"No user data found. Authentication may have failed."})}),a.jsx("div",{className:"mt-8 flex justify-center",children:a.jsx(o.default,{href:"/test-auth",className:"bg-[#2c2c27] text-white py-2 px-4 hover:bg-[#3c3c37]",children:"Back to Test Page"})})]})})}i=(c.then?(await c)():c)[0],r()}catch(e){r(e)}})},61296:(e,t,s)=>{"use strict";s.a(e,async(e,r)=>{try{s.d(t,{ts:()=>c,x4:()=>o,z2:()=>i});var a=s(93690);s(18201);var n=e([a]);a=(n.then?(await n)():n)[0],(0,a.gql)`
  mutation LoginUser($username: String!, $password: String!) {
    login(input: {
      clientMutationId: "login"
      username: $username
      password: $password
    }) {
      authToken
      refreshToken
      user {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RegisterUser($input: RegisterCustomerInput!) {
    registerCustomer(input: $input) {
      clientMutationId
      authToken
      refreshToken
      customer {
        id
        databaseId
        email
        firstName
        lastName
      }
    }
  }
`,(0,a.gql)`
  mutation RefreshAuthToken($input: RefreshJwtAuthTokenInput!) {
    refreshJwtAuthToken(input: $input) {
      authToken
    }
  }
`,(0,a.gql)`
  query GetCustomer {
    customer {
      id
      databaseId
      email
      firstName
      lastName
      billing {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
        email
        phone
      }
      shipping {
        firstName
        lastName
        company
        address1
        address2
        city
        state
        postcode
        country
      }
      orders {
        nodes {
          id
          databaseId
          date
          status
          total
          lineItems {
            nodes {
              product {
                node {
                  id
                  name
                }
              }
              quantity
              total
            }
          }
        }
      }
    }
  }
`,(0,a.gql)`
  mutation UpdateCustomer($input: UpdateCustomerInput!) {
    updateCustomer(input: $input) {
      clientMutationId
      customer {
        id
        databaseId
        email
        firstName
        lastName
        displayName
        billing {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
          email
          phone
        }
        shipping {
          firstName
          lastName
          company
          address1
          address2
          city
          state
          postcode
          country
        }
      }
    }
  }
`;let l="https://maroon-lapwing-781450.hostingersite.com/graphql",u=l&&!l.startsWith("http")?`https://${l}`:l;async function o(e,t){try{let s=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"login",username:e,password:t}),credentials:"include"});if(!s.ok){let e=await s.json();throw Error(e.message||"Login failed")}let r=await s.json();if(r.success&&r.user)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Login successful, user data received"),{success:!0,user:r.user,token:r.token};throw console.error("Login response missing user data"),Error("Login failed: Invalid response from server")}catch(e){return console.error("Login error:",e),{success:!1,message:e.message||"Login failed"}}}async function i(e,t,s,r){try{let a=await fetch("/api/auth",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"register",email:e,firstName:t,lastName:s,password:r}),credentials:"include"});if(!a.ok){let e=await a.json();throw Error(e.message||"Registration failed")}let n=await a.json();if(n.success&&n.customer)return"undefined"!=typeof localStorage&&localStorage.setItem("auth_session_started",Date.now().toString()),console.log("Registration successful, user data received"),{success:!0,customer:n.customer,token:n.token};throw console.error("Registration response missing customer data"),Error("Registration failed: Invalid response from server")}catch(e){return console.error("Registration error:",e),{success:!1,message:e.message||"Registration failed"}}}async function c(){try{let e=await fetch("/api/auth/user",{method:"GET",headers:{"Content-Type":"application/json"}});if(401===e.status)return null;let t=await e.json();if(!e.ok||!t.success)return null;return t.user}catch(e){return console.error("Get user error:",e),null}}new a.GraphQLClient(u,{headers:{"Content-Type":"application/json"}}),r()}catch(e){r(e)}})},90782:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\test-auth\success\page.tsx#default`)},18201:(e,t,s)=>{"use strict";class r extends Error{}r.prototype.name="InvalidTokenError"}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[8948,805,1067],()=>s(99842));module.exports=r})();