(()=>{var e={};e.id=1214,e.ids=[1214],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},5799:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d}),r(36663),r(51806),r(12523);var s=r(23191),a=r(88716),n=r(37922),o=r.n(n),i=r(95231),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d=["",{children:["cart-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36663)),"E:\\ankkorwoo\\ankkor\\src\\app\\cart-test\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,51806)),"E:\\ankkorwoo\\ankkor\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,12523)),"E:\\ankkorwoo\\ankkor\\src\\app\\not-found.tsx"]}],c=["E:\\ankkorwoo\\ankkor\\src\\app\\cart-test\\page.tsx"],u="/cart-test/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/cart-test/page",pathname:"/cart-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},13417:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},96799:(e,t,r)=>{Promise.resolve().then(r.bind(r,68897)),Promise.resolve().then(r.bind(r,75367))},47474:(e,t,r)=>{Promise.resolve().then(r.bind(r,93198))},54039:(e,t,r)=>{Promise.resolve().then(r.bind(r,83846))},93198:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(10326);r(17577);var a=r(86806);function n(){let e=(0,a.rY)();return(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[s.jsx("h1",{className:"text-2xl font-bold mb-6",children:"Cart Test"}),(0,s.jsxs)("div",{className:"space-y-4",children:[s.jsx("button",{onClick:()=>{e.addToCart({productId:"123",quantity:1,name:"Test Product",price:"99.99",image:{url:"/placeholder-product.jpg",altText:"Test Product"}})},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Add Test Item to Cart"}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h2",{className:"text-lg font-semibold",children:["Cart Items (",e.itemCount,")"]}),0===e.items.length?s.jsx("p",{children:"No items in cart"}):s.jsx("ul",{className:"space-y-2",children:e.items.map(t=>(0,s.jsxs)("li",{className:"border p-2 rounded",children:[(0,s.jsxs)("div",{children:[t.name," - ₹",t.price," x ",t.quantity]}),s.jsx("button",{onClick:()=>e.removeCartItem(t.id),className:"text-red-500 text-sm",children:"Remove"})]},t.id))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("p",{children:["Subtotal: ₹",e.subtotal().toFixed(2)]}),(0,s.jsxs)("p",{children:["Total: ₹",e.total().toFixed(2)]})]}),s.jsx("button",{onClick:e.clearCart,className:"px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600",children:"Clear Cart"})]})]})}},83846:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(10326);r(17577);var a=r(33265);let n=()=>s.jsx("div",{className:"animate-pulse flex space-x-4",children:(0,s.jsxs)("div",{className:"flex-1 space-y-6 py-1",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4",children:[s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-2"}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded col-span-1"})]}),s.jsx("div",{className:"h-2 bg-[#e5e2d9] rounded"})]})]})}),o=(0,a.default)(async()=>{},{loadableGenerated:{modules:["app\\not-found.tsx -> ./not-found-content"]},ssr:!1,loading:()=>s.jsx(n,{})});function i(){return s.jsx("div",{className:"container mx-auto py-20",children:s.jsx(o,{})})}},68897:(e,t,r)=>{"use strict";r.d(t,{CustomerProvider:()=>i,O:()=>o});var s=r(10326),a=r(17577);let n=(0,a.createContext)({customer:null,isLoading:!1,isAuthenticated:!1,token:null,login:async()=>{},register:async()=>{},logout:()=>{},updateProfile:async()=>{},error:null,refreshCustomer:async()=>{}}),o=()=>(0,a.useContext)(n),i=({children:e})=>{let[t,r]=(0,a.useState)(null),[o,i]=(0,a.useState)(!1),[l,d]=(0,a.useState)(null),[c,u]=(0,a.useState)(null),m=async e=>{console.log("Login function called - minimal implementation")},x=async e=>{console.log("Register function called - minimal implementation")},p=async e=>(console.log("Update profile function called - minimal implementation"),{}),h=async()=>{console.log("Refresh customer function called - minimal implementation")};return s.jsx(n.Provider,{value:{customer:t,isLoading:o,isAuthenticated:!!t&&!!c,token:c,login:m,register:x,logout:()=>{console.log("Logout function called - minimal implementation"),r(null),u(null)},updateProfile:p,error:l,refreshCustomer:h},children:e})}},75367:(e,t,r)=>{"use strict";r.d(t,{ToastProvider:()=>m});var s=r(10326),a=r(17577),n=r(92148),o=r(86462),i=r(54659),l=r(87888),d=r(18019),c=r(94019);let u=(0,a.createContext)(void 0);function m({children:e}){let[t,r]=(0,a.useState)([]);return(0,s.jsxs)(u.Provider,{value:{toasts:t,addToast:(e,t="info",s=3e3)=>{let a=Math.random().toString(36).substring(2,9);r(r=>[...r,{id:a,message:e,type:t,duration:s}])},removeToast:e=>{r(t=>t.filter(t=>t.id!==e))}},children:[e,s.jsx(p,{})]})}function x({toast:e,onRemove:t}){return(0,s.jsxs)(n.E.div,{initial:{opacity:0,y:-50},animate:{opacity:1,y:0},exit:{opacity:0,x:300},className:`flex items-center p-4 rounded-lg border shadow-lg ${(()=>{switch(e.type){case"success":return"bg-[#f4f3f0] border-[#8a8778]";case"error":return"bg-red-50 border-red-200";default:return"bg-[#f8f8f5] border-[#e5e2d9]"}})()} max-w-md`,children:[s.jsx(()=>{switch(e.type){case"success":return s.jsx(i.Z,{className:"h-5 w-5"});case"error":return s.jsx(l.Z,{className:"h-5 w-5"});default:return s.jsx(d.Z,{className:"h-5 w-5"})}},{}),s.jsx("span",{className:"ml-3 text-sm font-medium flex-1",children:e.message}),s.jsx("button",{onClick:t,className:"ml-4 text-gray-400 hover:text-gray-600",children:s.jsx(c.Z,{className:"h-4 w-4"})})]})}function p(){let{toasts:e,removeToast:t}=function(){let e=(0,a.useContext)(u);if(void 0===e)throw Error("useToast must be used within a ToastProvider");return e}();return s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:s.jsx(o.M,{children:e.map(e=>s.jsx(x,{toast:e,onRemove:()=>t(e.id)},e.id))})})}},36663:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\cart-test\page.tsx#default`)},51806:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>u});var s=r(19510),a=r(10527),n=r.n(a),o=r(36822),i=r.n(o);r(5023);var l=r(68570);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#useCustomer`);let d=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\providers\CustomerProvider.tsx#CustomerProvider`),c=(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#ToastProvider`);(0,l.createProxy)(String.raw`E:\ankkorwoo\ankkor\src\components\ui\toast.tsx#useToast`);let u={title:"Ankkor | Timeless Menswear",description:"Elevated essentials for the discerning gentleman. Impeccably tailored garments crafted from the finest materials.",keywords:["menswear","luxury clothing","tailored","shirts","accessories"],icons:{icon:[{url:"/logo.PNG",sizes:"32x32",type:"image/png"},{url:"/logo.PNG",sizes:"16x16",type:"image/png"}],shortcut:"/logo.PNG",apple:"/logo.PNG"}};function m({children:e}){return s.jsx("html",{lang:"en",children:s.jsx("body",{className:`${n().variable} ${i().variable} font-sans antialiased min-h-screen bg-[#f8f8f5]`,children:s.jsx(c,{children:s.jsx(d,{children:s.jsx("main",{style:{paddingTop:0},className:"transition-all duration-300",children:e})})})})})}},12523:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(68570).createProxy)(String.raw`E:\ankkorwoo\ankkor\src\app\not-found.tsx#default`)},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8948,3373,7207,6806],()=>r(5799));module.exports=s})();