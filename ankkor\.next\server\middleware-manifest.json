{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Hx1nTQiaABiEcgdMqhHov", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oKReB0XYs7qi+uIjx1Hyxwls2h8TgR5ai+TEoPKCZdk=", "__NEXT_PREVIEW_MODE_ID": "7ce04a9510581115544a68035fb97584", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "0191d37c44b6b3feba85e04a7aa1d9f07c12b06e5f150df5d19fc0b448835af3", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a3e2ad125ac060381904867760eb6c984a7a2d5bbdc88460e67af51396f5bf4d"}}}, "functions": {}, "sortedMiddleware": ["/"]}