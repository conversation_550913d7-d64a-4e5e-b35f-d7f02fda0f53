{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api\\/status\\/redis(.json)?[\\/#\\?]?$", "originalSource": "/api/status/redis"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/account(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/account/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/checkout(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/checkout/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-in(.json)?[\\/#\\?]?$", "originalSource": "/sign-in"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/sign-up(.json)?[\\/#\\?]?$", "originalSource": "/sign-up"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Wip6vRnO43ELfm6SSZxZI", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "l8D5D3B91d5EaBqQE2+ruY00Hv+hncP3x7mGyWY4rK4=", "__NEXT_PREVIEW_MODE_ID": "80577f1d61c004df234011178ec187cc", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bcf1a2fd7d109ea18efb5bfc9d0ec16d522fde261450327f2a2907c87f3df3fb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "11d6258ad42ab38eb209dd05ec9918c10abf2b0352335e9ecdf07f5ea3d9f5c2"}}}, "functions": {}, "sortedMiddleware": ["/"]}