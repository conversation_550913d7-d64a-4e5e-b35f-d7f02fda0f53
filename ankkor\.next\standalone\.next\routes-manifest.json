{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}, {"source": "/home", "destination": "/", "statusCode": 308, "regex": "^(?!/_next)/home(?:/)?$"}, {"source": "/api/init", "destination": "/api/revalidate?type=all", "statusCode": 307, "regex": "^(?!/_next)/api/init(?:/)?$"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}], "regex": "^(?:/(.*))(?:/)?$"}, {"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "no-store, max-age=0"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}], "regex": "^/api(?:/(.*))(?:/)?$"}], "dynamicRoutes": [{"page": "/api/cache/products/[handle]", "regex": "^/api/cache/products/([^/]+?)(?:/)?$", "routeKeys": {"nxtPhandle": "nxtPhandle"}, "namedRegex": "^/api/cache/products/(?<nxtPhandle>[^/]+?)(?:/)?$"}, {"page": "/api/products/[id]/stock", "regex": "^/api/products/([^/]+?)/stock(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/products/(?<nxtPid>[^/]+?)/stock(?:/)?$"}, {"page": "/category/[slug]", "regex": "^/category/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/category/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/product/[slug]", "regex": "^/product/([^/]+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/product/(?<nxtPslug>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about", "regex": "^/about(?:/)?$", "routeKeys": {}, "namedRegex": "^/about(?:/)?$"}, {"page": "/about/craftsmanship", "regex": "^/about/craftsmanship(?:/)?$", "routeKeys": {}, "namedRegex": "^/about/craftsmanship(?:/)?$"}, {"page": "/about/sustainability", "regex": "^/about/sustainability(?:/)?$", "routeKeys": {}, "namedRegex": "^/about/sustainability(?:/)?$"}, {"page": "/account", "regex": "^/account(?:/)?$", "routeKeys": {}, "namedRegex": "^/account(?:/)?$"}, {"page": "/admin/products", "regex": "^/admin/products(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/products(?:/)?$"}, {"page": "/cart-test", "regex": "^/cart\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/cart\\-test(?:/)?$"}, {"page": "/categories", "regex": "^/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/categories(?:/)?$"}, {"page": "/checkout", "regex": "^/checkout(?:/)?$", "routeKeys": {}, "namedRegex": "^/checkout(?:/)?$"}, {"page": "/collection", "regex": "^/collection(?:/)?$", "routeKeys": {}, "namedRegex": "^/collection(?:/)?$"}, {"page": "/collection/polos", "regex": "^/collection/polos(?:/)?$", "routeKeys": {}, "namedRegex": "^/collection/polos(?:/)?$"}, {"page": "/collection/shirts", "regex": "^/collection/shirts(?:/)?$", "routeKeys": {}, "namedRegex": "^/collection/shirts(?:/)?$"}, {"page": "/customer-service", "regex": "^/customer\\-service(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer\\-service(?:/)?$"}, {"page": "/customer-service/contact", "regex": "^/customer\\-service/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer\\-service/contact(?:/)?$"}, {"page": "/customer-service/faq", "regex": "^/customer\\-service/faq(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer\\-service/faq(?:/)?$"}, {"page": "/customer-service/size-guide", "regex": "^/customer\\-service/size\\-guide(?:/)?$", "routeKeys": {}, "namedRegex": "^/customer\\-service/size\\-guide(?:/)?$"}, {"page": "/local-cart-test", "regex": "^/local\\-cart\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/local\\-cart\\-test(?:/)?$"}, {"page": "/order-confirmed", "regex": "^/order\\-confirmed(?:/)?$", "routeKeys": {}, "namedRegex": "^/order\\-confirmed(?:/)?$"}, {"page": "/privacy-policy", "regex": "^/privacy\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/privacy\\-policy(?:/)?$"}, {"page": "/return-policy", "regex": "^/return\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/return\\-policy(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/search", "regex": "^/search(?:/)?$", "routeKeys": {}, "namedRegex": "^/search(?:/)?$"}, {"page": "/shipping-policy", "regex": "^/shipping\\-policy(?:/)?$", "routeKeys": {}, "namedRegex": "^/shipping\\-policy(?:/)?$"}, {"page": "/sign-in", "regex": "^/sign\\-in(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-in(?:/)?$"}, {"page": "/sign-up", "regex": "^/sign\\-up(?:/)?$", "routeKeys": {}, "namedRegex": "^/sign\\-up(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/terms-of-service", "regex": "^/terms\\-of\\-service(?:/)?$", "routeKeys": {}, "namedRegex": "^/terms\\-of\\-service(?:/)?$"}, {"page": "/test", "regex": "^/test(?:/)?$", "routeKeys": {}, "namedRegex": "^/test(?:/)?$"}, {"page": "/test-auth", "regex": "^/test\\-auth(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-auth(?:/)?$"}, {"page": "/test-auth/success", "regex": "^/test\\-auth/success(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-auth/success(?:/)?$"}, {"page": "/wishlist", "regex": "^/wishlist(?:/)?$", "routeKeys": {}, "namedRegex": "^/wishlist(?:/)?$"}, {"page": "/woocommerce-cart-test", "regex": "^/woocommerce\\-cart\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/woocommerce\\-cart\\-test(?:/)?$"}, {"page": "/woocommerce-checkout-test", "regex": "^/woocommerce\\-checkout\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/woocommerce\\-checkout\\-test(?:/)?$"}, {"page": "/woocommerce-test", "regex": "^/woocommerce\\-test(?:/)?$", "routeKeys": {}, "namedRegex": "^/woocommerce\\-test(?:/)?$"}, {"page": "/woocommerce-test/success", "regex": "^/woocommerce\\-test/success(?:/)?$", "routeKeys": {}, "namedRegex": "^/woocommerce\\-test/success(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}