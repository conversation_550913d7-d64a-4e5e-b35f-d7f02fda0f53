(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[285],{33217:function(e,s,r){Promise.resolve().then(r.bind(r,6444))},6444:function(e,s,r){"use strict";r.r(s),r.d(s,{default:function(){return E}});var i=r(57437),t=r(2265),a=r(99376),n=r(29501),o=r(87758),d=r(59625),l=r(89134);let c=()=>new Promise(e=>{if(window.Razorpay){e(!0);return}let s=document.createElement("script");s.src="https://checkout.razorpay.com/v1/checkout.js",s.onload=()=>{console.log("Razorpay SDK loaded successfully"),e(!0)},s.onerror=()=>{console.error("Failed to load Razorpay SDK"),e(!1)},document.body.appendChild(s)}),p=async function(e,s){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};try{if(!e||e<=0)throw Error("Invalid amount");if(e<1)throw Error("Minimum order amount is ₹1");console.log("Creating Razorpay order:",{amount:e,receipt:s,notes:r});let i=await fetch("/api/razorpay/create-order",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:Math.round(100*e),receipt:s,notes:r})});if(!i.ok){let e=await i.json();if(console.error("Razorpay order creation failed:",e),400===i.status)throw Error(e.error||"Invalid order data");if(500===i.status)throw Error("Payment gateway error. Please try again.");throw Error(e.error||"Failed to create payment order")}let t=await i.json();return console.log("Razorpay order created successfully:",t.id),t}catch(e){if(console.error("Error creating Razorpay order:",e),e instanceof Error)throw e;throw Error("Failed to create payment order")}},m=async(e,s)=>{try{let r=await fetch("/api/razorpay/verify-payment",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({razorpay_payment_id:e.razorpay_payment_id,razorpay_order_id:e.razorpay_order_id,razorpay_signature:e.razorpay_signature,address:s.address,cartItems:s.cartItems,shipping:s.shipping})});if(!r.ok){let e=await r.json();throw Error(e.message||"Payment verification failed")}return await r.json()}catch(e){throw console.error("Error verifying payment:",e),e}},h=e=>new Promise((s,r)=>{try{if(!window.Razorpay){r(Error("Razorpay SDK not loaded"));return}new window.Razorpay({...e,handler:e=>{s(e)},modal:{ondismiss:()=>{r(Error("Payment canceled by user"))}}}).open()}catch(e){console.error("Error initializing Razorpay:",e),r(e)}}),g=async(e,s)=>{try{let r=await fetch("/api/shipping-rates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({pincode:e,cartItems:s})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to get shipping rates")}return await r.json()}catch(e){throw console.error("Error getting shipping rates:",e),e}},u=(0,d.Ue)()((0,l.tJ)((e,s)=>({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null,setCart:r=>{let i=r.reduce((e,s)=>e+("string"==typeof s.price?parseFloat(s.price):s.price)*s.quantity,0),{shippingCost:t}=s();e({cart:r,subtotal:i,finalAmount:i+t})},setShippingAddress:s=>{e({shippingAddress:s})},fetchShippingRates:async r=>{let{cart:i,subtotal:t}=s();if(!r||r.length<6){e({error:"Please enter a valid pincode"});return}e({isLoadingShipping:!0,error:null});try{let s=await g(r,i);e({shippingOptions:s,isLoadingShipping:!1,selectedShipping:null,shippingCost:0,finalAmount:t+0})}catch(s){console.error("Error fetching shipping rates:",s),e({error:s instanceof Error?s.message:"Failed to fetch shipping rates",isLoadingShipping:!1,shippingOptions:[]})}},setSelectedShipping:r=>{let{subtotal:i}=s(),t=i+r.cost;e({selectedShipping:r,shippingCost:r.cost,finalAmount:t})},calculateFinalAmount:()=>{let{subtotal:r,shippingCost:i,finalAmount:t}=s(),a=r+i;a!==t&&e({finalAmount:a})},setError:s=>{e({error:s})},setProcessingPayment:s=>{e({isProcessingPayment:s})},clearCheckout:()=>{e({cart:[],shippingAddress:null,shippingOptions:[],selectedShipping:null,subtotal:0,shippingCost:0,finalAmount:0,isLoadingShipping:!1,isProcessingPayment:!1,error:null})}}),{name:"checkout-storage",partialize:e=>({shippingAddress:e.shippingAddress,selectedShipping:e.selectedShipping})}));var x=r(3371),y=r(12381),f=r(40279),j=r(66840),N=t.forwardRef((e,s)=>(0,i.jsx)(j.WV.label,{...e,ref:s,onMouseDown:s=>{var r;s.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));N.displayName="Label";var v=r(93448);function b(e){let{className:s,...r}=e;return(0,i.jsx)(N,{"data-slot":"label",className:(0,v.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}var w=r(15863),P=r(40340),S=r(88226);function E(){let e=(0,a.useRouter)(),{isAuthenticated:s,isLoading:r}=(0,x.O)(),d=(0,o.rY)(),l=u(),[g,j]=(0,t.useState)(!1),{register:N,handleSubmit:v,watch:E,formState:{errors:A}}=(0,n.cI)(),F=E("pincode");(0,t.useEffect)(()=>{if(!s){e.push("/sign-in");return}if(0===d.items.length){e.push("/");return}l.setCart(d.items)},[d.items,e,s]),(0,t.useEffect)(()=>{c()},[]),(0,t.useEffect)(()=>{F&&6===F.length&&s&&l.fetchShippingRates(F)},[F,s]);let z=async e=>{let s={firstName:e.firstName,lastName:e.lastName,address1:e.address1,address2:e.address2,city:e.city,state:e.state,pincode:e.pincode,phone:e.phone};l.setShippingAddress(s)},k=async()=>{if(!l.shippingAddress){l.setError("Please fill in your shipping address");return}if(!l.selectedShipping){l.setError("Please select a shipping method");return}if(0===l.cart.length){l.setError("Your cart is empty");return}if(l.finalAmount<=0){l.setError("Invalid order amount");return}j(!0),l.setProcessingPayment(!0),l.setError(null);try{let s="rzp_live_H1Iyl4j48eSFYj";if(!s)throw Error("Payment gateway not configured. Please contact support.");console.log("Creating Razorpay order for amount:",l.finalAmount);let r=await p(l.finalAmount,"order_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),{customer_phone:l.shippingAddress.phone,customer_name:"".concat(l.shippingAddress.firstName," ").concat(l.shippingAddress.lastName),shipping_method:l.selectedShipping.name});console.log("Razorpay order created:",r.id),await h({key:s,amount:r.amount,currency:r.currency,name:"Ankkor",description:"Order Payment - ".concat(l.cart.length," item(s)"),order_id:r.id,handler:async s=>{console.log("Payment successful, verifying...",s),l.setError(null);try{let r=await m(s,{address:l.shippingAddress,cartItems:l.cart,shipping:l.selectedShipping});if(console.log("Payment verification result:",r),r.success)d.clearCart(),l.clearCheckout(),e.push("/order-confirmed?id=".concat(r.orderId));else throw Error(r.message||"Payment verification failed")}catch(e){console.error("Payment verification error:",e),l.setError(e instanceof Error?e.message:"Payment verification failed. Please contact support if amount was deducted.")}finally{j(!1),l.setProcessingPayment(!1)}},prefill:{name:"".concat(l.shippingAddress.firstName," ").concat(l.shippingAddress.lastName),contact:l.shippingAddress.phone},theme:{color:"#2c2c27"},modal:{ondismiss:()=>{console.log("Payment modal dismissed"),j(!1),l.setProcessingPayment(!1)}}})}catch(a){var s,r,i,t;console.error("Payment error:",a);let e="Payment failed. Please try again.";(null===(s=a.message)||void 0===s?void 0:s.includes("not configured"))?e=a.message:(null===(r=a.message)||void 0===r?void 0:r.includes("network"))||(null===(i=a.message)||void 0===i?void 0:i.includes("fetch"))?e="Network error. Please check your connection and try again.":(null===(t=a.message)||void 0===t?void 0:t.includes("amount"))?e="Invalid amount. Please refresh and try again.":a.message&&(e=a.message),l.setError(e)}finally{j(!1),l.setProcessingPayment(!1)}};return r?(0,i.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,i.jsxs)("div",{className:"flex items-center justify-center min-h-[400px]",children:[(0,i.jsx)(w.Z,{className:"h-8 w-8 animate-spin"}),(0,i.jsx)("span",{className:"ml-2",children:"Loading..."})]})}):s&&0!==d.items.length?(0,i.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-serif mb-8",children:"Checkout"}),l.error&&(0,i.jsx)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded",children:l.error}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,i.jsx)(P.Z,{className:"mr-2 h-5 w-5"}),"Shipping Address"]}),(0,i.jsxs)("form",{onSubmit:v(z),className:"space-y-4",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{htmlFor:"firstName",children:"First Name"}),(0,i.jsx)(f.I,{id:"firstName",...N("firstName",{required:"First name is required"}),className:A.firstName?"border-red-300":""}),A.firstName&&(0,i.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A.firstName.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{htmlFor:"lastName",children:"Last Name"}),(0,i.jsx)(f.I,{id:"lastName",...N("lastName",{required:"Last name is required"}),className:A.lastName?"border-red-300":""}),A.lastName&&(0,i.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A.lastName.message})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)(b,{htmlFor:"address1",children:"Address Line 1"}),(0,i.jsx)(f.I,{id:"address1",...N("address1",{required:"Address is required"}),className:A.address1?"border-red-300":""}),A.address1&&(0,i.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A.address1.message})]}),(0,i.jsxs)("div",{className:"md:col-span-2",children:[(0,i.jsx)(b,{htmlFor:"address2",children:"Address Line 2 (Optional)"}),(0,i.jsx)(f.I,{id:"address2",...N("address2")})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{htmlFor:"city",children:"City"}),(0,i.jsx)(f.I,{id:"city",...N("city",{required:"City is required"}),className:A.city?"border-red-300":""}),A.city&&(0,i.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A.city.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{htmlFor:"state",children:"State"}),(0,i.jsx)(f.I,{id:"state",...N("state",{required:"State is required"}),className:A.state?"border-red-300":""}),A.state&&(0,i.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A.state.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{htmlFor:"pincode",children:"Pincode"}),(0,i.jsx)(f.I,{id:"pincode",...N("pincode",{required:"Pincode is required",pattern:{value:/^[0-9]{6}$/,message:"Please enter a valid 6-digit pincode"}}),className:A.pincode?"border-red-300":"",placeholder:"Enter 6-digit pincode"}),A.pincode&&(0,i.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A.pincode.message})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)(b,{htmlFor:"phone",children:"Phone Number"}),(0,i.jsx)(f.I,{id:"phone",...N("phone",{required:"Phone number is required"}),className:A.phone?"border-red-300":""}),A.phone&&(0,i.jsx)("p",{className:"text-sm text-red-500 mt-1",children:A.phone.message})]})]}),(0,i.jsx)(y.z,{type:"submit",className:"mt-4 w-full bg-[#2c2c27] hover:bg-[#3c3c37] text-white",children:"Save Address & Continue"})]})]}),(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,i.jsx)(P.Z,{className:"mr-2 h-5 w-5"}),"Shipping Options"]}),l.isLoadingShipping?(0,i.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,i.jsx)(w.Z,{className:"h-6 w-6 animate-spin mr-2"}),(0,i.jsx)("span",{children:"Loading shipping options..."})]}):0===l.shippingOptions.length?(0,i.jsx)("div",{className:"text-gray-500 py-4",children:F&&6===F.length?"No shipping options available for this pincode":"Enter a valid pincode to see shipping options"}):(0,i.jsx)("div",{className:"space-y-3",children:l.shippingOptions.map(e=>{var s,r;return(0,i.jsx)("div",{className:"border rounded-lg p-4 cursor-pointer transition-colors ".concat((null===(s=l.selectedShipping)||void 0===s?void 0:s.id)===e.id?"border-[#2c2c27] bg-gray-50":"border-gray-200 hover:border-gray-300"),onClick:()=>l.setSelectedShipping(e),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("div",{children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("input",{type:"radio",name:"shipping",checked:(null===(r=l.selectedShipping)||void 0===r?void 0:r.id)===e.id,onChange:()=>l.setSelectedShipping(e),className:"mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-medium",children:e.name}),e.description&&(0,i.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),e.estimatedDays&&(0,i.jsxs)("p",{className:"text-sm text-gray-500",children:["Estimated delivery: ",e.estimatedDays]})]})]})}),(0,i.jsx)("div",{className:"text-lg font-medium",children:0===e.cost?"Free":"₹".concat(e.cost.toFixed(2))})]})},e.id)})})]}),(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm",children:[(0,i.jsxs)("h2",{className:"text-xl font-medium mb-4 flex items-center",children:[(0,i.jsx)(S.Z,{className:"mr-2 h-5 w-5"}),"Payment"]}),(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center p-4 border rounded-lg",children:[(0,i.jsx)("input",{type:"radio",id:"razorpay",name:"payment",checked:!0,readOnly:!0,className:"mr-3"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{htmlFor:"razorpay",className:"font-medium",children:"Razorpay"}),(0,i.jsx)("p",{className:"text-sm text-gray-600",children:"Pay securely with credit card, debit card, UPI, or net banking"})]})]}),(0,i.jsx)(y.z,{onClick:k,className:"w-full py-6 bg-[#2c2c27] hover:bg-[#3c3c37] text-white",disabled:g||!l.shippingAddress||!l.selectedShipping||l.isProcessingPayment,children:g||l.isProcessingPayment?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(w.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing Payment..."]}):"Proceed to Pay - ₹".concat(l.finalAmount.toFixed(2))})]})]})]}),(0,i.jsx)("div",{className:"lg:col-span-1",children:(0,i.jsxs)("div",{className:"bg-white p-6 border rounded-lg shadow-sm sticky top-8",children:[(0,i.jsx)("h2",{className:"text-xl font-medium mb-4",children:"Order Summary"}),(0,i.jsxs)("div",{className:"space-y-4",children:[l.cart.map(e=>{var s;return(0,i.jsxs)("div",{className:"flex gap-4 py-2 border-b",children:[(null===(s=e.image)||void 0===s?void 0:s.url)&&(0,i.jsx)("div",{className:"relative h-16 w-16 bg-gray-100 flex-shrink-0",children:(0,i.jsx)("img",{src:e.image.url,alt:e.name,className:"h-full w-full object-cover rounded"})}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h3",{className:"text-sm font-medium",children:e.name}),(0,i.jsxs)("p",{className:"text-sm text-gray-600",children:["₹","string"==typeof e.price?parseFloat(e.price).toFixed(2):e.price.toFixed(2)," \xd7 ",e.quantity]})]}),(0,i.jsxs)("div",{className:"text-right",children:["₹",("string"==typeof e.price?parseFloat(e.price)*e.quantity:e.price*e.quantity).toFixed(2)]})]},e.id)}),(0,i.jsxs)("div",{className:"pt-4 space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Subtotal"}),(0,i.jsxs)("span",{children:["₹",l.subtotal.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-gray-600",children:"Shipping"}),(0,i.jsx)("span",{children:l.selectedShipping?0===l.selectedShipping.cost?"Free":"₹".concat(l.selectedShipping.cost.toFixed(2)):"TBD"})]}),(0,i.jsxs)("div",{className:"flex justify-between text-lg font-medium pt-2 border-t",children:[(0,i.jsx)("span",{children:"Total"}),(0,i.jsxs)("span",{children:["₹",l.finalAmount.toFixed(2)]})]})]})]})]})})]})]}):null}}},function(e){e.O(0,[5540,4822,8787,8261,1539,2188,6003,7231,2323,9960,7696,8002,7111,8989,4513,8476,8496,8966,3903,6076,4596,62,6271,8726,8133,8049,2870,632,6613,9965,5270,2647,9971,9290,6459,7800,4818,1729,5526,4615,7406,1225,2068,4595,3280,8790,1104,7158,7044,9482,986,4754,1744],function(){return e(e.s=33217)}),_N_E=e.O()}]);