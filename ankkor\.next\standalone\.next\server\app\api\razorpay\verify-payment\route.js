"use strict";(()=>{var e={};e.id=552,e.ids=[552],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},84770:e=>{e.exports=require("crypto")},42902:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>h,patchFetch:()=>_,requestAsyncStorage:()=>y,routeModule:()=>l,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>c});var a=t(49303),o=t(88716),n=t(60670),i=t(87070),d=t(84770),p=t.n(d);async function c(e){try{let{razorpay_payment_id:r,razorpay_order_id:t,razorpay_signature:s,address:a,cartItems:o,shipping:n}=await e.json();if(!r||!t||!s)return i.NextResponse.json({success:!1,message:"Missing payment verification data"},{status:400});if(!a||!o||!n)return i.NextResponse.json({success:!1,message:"Missing order data"},{status:400});let d=process.env.RAZORPAY_KEY_SECRET;if(!d)return console.error("Razorpay key secret not configured"),i.NextResponse.json({success:!1,message:"Payment gateway not configured"},{status:500});if(p().createHmac("sha256",d).update(`${t}|${r}`).digest("hex")!==s)return console.error("Invalid payment signature"),i.NextResponse.json({success:!1,message:"Invalid payment signature"},{status:400});console.log("Payment signature verified successfully");let c=await u({address:a,cartItems:o,shipping:n,paymentDetails:{payment_id:r,order_id:t,signature:s}});return i.NextResponse.json({success:!0,orderId:c,message:"Payment verified and order created successfully"})}catch(e){return console.error("Payment verification error:",e),i.NextResponse.json({success:!1,message:e.message||"Payment verification failed"},{status:500})}}async function u(e){try{let r="https://maroon-lapwing-781450.hostingersite.com",t=process.env.WOOCOMMERCE_CONSUMER_KEY,s=process.env.WOOCOMMERCE_CONSUMER_SECRET;if(!r||!t||!s)throw Error("WooCommerce credentials not configured");e.cartItems.reduce((e,r)=>{let t="string"==typeof r.price?parseFloat(r.price):r.price;return e+t*r.quantity},0),e.shipping.cost;let a={billing:{first_name:e.address.firstName,last_name:e.address.lastName,address_1:e.address.address1,address_2:e.address.address2||"",city:e.address.city,state:e.address.state,postcode:e.address.pincode,country:"IN",phone:e.address.phone},shipping:{first_name:e.address.firstName,last_name:e.address.lastName,address_1:e.address.address1,address_2:e.address.address2||"",city:e.address.city,state:e.address.state,postcode:e.address.pincode,country:"IN"},line_items:e.cartItems.map(e=>({product_id:parseInt(e.productId),variation_id:e.variationId?parseInt(e.variationId):void 0,quantity:e.quantity})),shipping_lines:[{method_id:e.shipping.id,method_title:e.shipping.name,total:e.shipping.cost.toString()}],payment_method:"razorpay",payment_method_title:"Razorpay",set_paid:!0,status:"processing",meta_data:[{key:"razorpay_payment_id",value:e.paymentDetails.payment_id},{key:"razorpay_order_id",value:e.paymentDetails.order_id},{key:"razorpay_signature",value:e.paymentDetails.signature},{key:"payment_gateway",value:"razorpay_headless"}]};console.log("Creating WooCommerce order with payload:",JSON.stringify(a,null,2));let o=Buffer.from(`${t}:${s}`).toString("base64"),n=await fetch(`${r}/wp-json/wc/v3/orders`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Basic ${o}`},body:JSON.stringify(a)});if(!n.ok){let e=await n.json();throw console.error("WooCommerce API error:",e),Error(`WooCommerce API error: ${e.message||n.statusText}`)}let i=await n.json();return console.log("WooCommerce order created successfully:",i.id),await m(i,e.address),i.id.toString()}catch(e){throw console.error("Error creating WooCommerce order:",e),Error(`Failed to create order: ${e.message}`)}}async function m(e,r){try{console.log(`Order confirmation email should be sent for order ${e.id} to ${r.firstName} ${r.lastName}`)}catch(e){console.error("Error sending confirmation email:",e)}}let l=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/razorpay/verify-payment/route",pathname:"/api/razorpay/verify-payment",filename:"route",bundlePath:"app/api/razorpay/verify-payment/route"},resolvedPagePath:"E:\\ankkorwoo\\ankkor\\src\\app\\api\\razorpay\\verify-payment\\route.ts",nextConfigOutput:"standalone",userland:s}),{requestAsyncStorage:y,staticGenerationAsyncStorage:g,serverHooks:f}=l,h="/api/razorpay/verify-payment/route";function _(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[8948,5972],()=>t(42902));module.exports=s})();